(()=>{var e={};e.id=298,e.ids=[298],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},74295:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=s(70260),r=s(28203),i=s(25155),n=s.n(i),l=s(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1284)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\account\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\account\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8147:(e,t,s)=>{Promise.resolve().then(s.bind(s,1284))},2571:(e,t,s)=>{Promise.resolve().then(s.bind(s,99440))},99440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(45512),r=s(58009),i=s(97643),n=s(87021),l=s(81846),o=s(69193),c=s(86235),d=s(4269),m=s(4643),u=s(82901),h=s(19473),p=s(28531),x=s.n(p);function f(){let[e,t]=(0,r.useState)(null),[s,p]=(0,r.useState)([]),[f,g]=(0,r.useState)(!0),[y,j]=(0,r.useState)(null);return f?(0,a.jsx)("div",{className:"container py-8 md:py-12",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin"})})}):y?(0,a.jsx)("div",{className:"container py-8 md:py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:y}),(0,a.jsx)(x(),{href:"/login",children:(0,a.jsx)(n.$,{children:"Go to Login"})})]})}):(0,a.jsxs)("div",{className:"container py-8 md:py-12",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight mb-2",children:"My Account"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your account and view your activity"})]}),(0,a.jsx)(x(),{href:"/settings",children:(0,a.jsx)(n.$,{children:"Settings"})})]}),(0,a.jsxs)("div",{className:"grid gap-8 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Profile"})}),(0,a.jsxs)(i.Wu,{className:"flex flex-col items-center text-center",children:[(0,a.jsx)(l.eu,{className:"h-24 w-24 mb-4",children:(0,a.jsxs)(l.q5,{className:"text-2xl bg-primary/10 text-primary",children:[e?.user.firstName?.[0],e?.user.lastName?.[0]]})}),(0,a.jsxs)("h2",{className:"text-xl font-bold",children:[e?.user.firstName," ",e?.user.lastName]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e?.user.email}),(0,a.jsxs)("div",{className:"mt-4 p-2 bg-primary/10 text-primary rounded-full text-sm font-medium capitalize",children:[e?.user.plan," Member"]})]}),(0,a.jsx)(i.wL,{className:"flex justify-center",children:(0,a.jsx)(x(),{href:"/settings",children:(0,a.jsx)(n.$,{variant:"outline",children:"Edit Profile"})})})]}),(0,a.jsxs)(i.Zp,{className:"mt-6",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Subscription"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Current Plan"}),(0,a.jsx)("p",{className:"text-primary font-bold",children:"Medium Business"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Status"}),(0,a.jsx)("p",{className:"text-green-600 dark:text-green-400",children:"Active"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Purchased On"}),(0,a.jsx)("p",{children:"January 15, 2023"})]})]})}),(0,a.jsx)(i.wL,{children:(0,a.jsx)(x(),{href:"/pricing",className:"w-full",children:(0,a.jsx)(n.$,{variant:"outline",className:"w-full",children:"Upgrade Plan"})})})]})]}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsxs)(o.Tabs,{defaultValue:"activity",children:[(0,a.jsxs)(o.TabsList,{className:"bg-muted/50 p-1 rounded-lg",children:[(0,a.jsx)(o.TabsTrigger,{value:"activity",children:"Recent Activity"}),(0,a.jsx)(o.TabsTrigger,{value:"books",children:"My Books"}),(0,a.jsx)(o.TabsTrigger,{value:"plans",children:"My Plans"})]}),(0,a.jsx)(o.TabsContent,{value:"activity",className:"mt-6 space-y-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Recent Activity"}),(0,a.jsx)(i.BT,{children:"Your recent interactions with TelkTibeb"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(d.A,{className:"h-5 w-5 text-primary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:'Read "The Psychology of Money"'}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You read this book summary for 15 minutes"}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"})," 2 hours ago"]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-primary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:'Viewed "Local Coffee Shop" Business Plan'}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You viewed this business plan"}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"})," 1 day ago"]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-primary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:'Saved "Atomic Habits" for Offline Reading'}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You saved this book for offline access"}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"})," 3 days ago"]})]})]})]})})]})}),(0,a.jsx)(o.TabsContent,{value:"books",className:"mt-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"My Books"}),(0,a.jsx)(i.BT,{children:"Books you've interacted with"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex gap-4 p-4 border rounded-lg",children:[(0,a.jsx)("img",{src:"/placeholder.svg?height=80&width=60",alt:"Book cover",className:"w-[60px] h-[80px] object-cover rounded"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"The Psychology of Money"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Morgan Housel"}),(0,a.jsx)("div",{className:"flex gap-2 mt-2",children:(0,a.jsx)(x(),{href:"/books/the-psychology-of-money",children:(0,a.jsx)(n.$,{size:"sm",variant:"outline",children:"Continue"})})})]})]}),(0,a.jsxs)("div",{className:"flex gap-4 p-4 border rounded-lg",children:[(0,a.jsx)("img",{src:"/placeholder.svg?height=80&width=60",alt:"Book cover",className:"w-[60px] h-[80px] object-cover rounded"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Atomic Habits"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"James Clear"}),(0,a.jsx)("div",{className:"flex gap-2 mt-2",children:(0,a.jsx)(x(),{href:"/books/atomic-habits",children:(0,a.jsx)(n.$,{size:"sm",variant:"outline",children:"Continue"})})})]})]}),(0,a.jsxs)("div",{className:"flex gap-4 p-4 border rounded-lg",children:[(0,a.jsx)("img",{src:"/placeholder.svg?height=80&width=60",alt:"Book cover",className:"w-[60px] h-[80px] object-cover rounded"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Sapiens"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Yuval Noah Harari"}),(0,a.jsx)("div",{className:"flex gap-2 mt-2",children:(0,a.jsx)(x(),{href:"/books/sapiens",children:(0,a.jsx)(n.$,{size:"sm",variant:"outline",children:"Continue"})})})]})]})]})}),(0,a.jsx)(i.wL,{children:(0,a.jsx)(x(),{href:"/books",children:(0,a.jsx)(n.$,{variant:"outline",children:"View All Books"})})})]})}),(0,a.jsx)(o.TabsContent,{value:"plans",className:"mt-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"My Business Plans"}),(0,a.jsx)(i.BT,{children:"Business plans you've accessed"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-primary"}),(0,a.jsx)("h3",{className:"font-medium",children:"Local Coffee Shop"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Food & Beverage"}),(0,a.jsx)(x(),{href:"/business-plans/small/1",children:(0,a.jsx)(n.$,{size:"sm",variant:"outline",children:"View Plan"})})]}),(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-primary"}),(0,a.jsx)("h3",{className:"font-medium",children:"Freelance Web Development"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Technology"}),(0,a.jsx)(x(),{href:"/business-plans/small/2",children:(0,a.jsx)(n.$,{size:"sm",variant:"outline",children:"View Plan"})})]})]})}),(0,a.jsx)(i.wL,{children:(0,a.jsx)(x(),{href:"/business-plans",children:(0,a.jsx)(n.$,{variant:"outline",children:"View All Plans"})})})]})})]})})]})]})}s(97730)},69193:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>l,TabsContent:()=>d,TabsList:()=>o,TabsTrigger:()=>c});var a=s(45512),r=s(58009),i=s(55613),n=s(59462);let l=i.bL,o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));o.displayName=i.B8.displayName;let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));c.displayName=i.l9.displayName;let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));d.displayName=i.UC.displayName},97730:(e,t,s)=>{"use strict";s.d(t,{FH:()=>l,lj:()=>c,hS:()=>o,Pr:()=>d}),s(58009);let a=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:8000/api";class r{static{this.baseUrl=a}static async fetchWithErrorHandling(e,t={}){let s=`${this.baseUrl}${e}`;try{let e=await fetch(s,{headers:{"Content-Type":"application/json",...t.headers},...t});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(t){throw console.error(`API call failed for ${e}:`,t),t}}static async getBooks(e){let t=new URLSearchParams;e?.category&&t.append("category",e.category),e?.query&&t.append("query",e.query),e?.premium!==void 0&&t.append("premium",e.premium.toString()),e?.limit&&t.append("limit",e.limit.toString());let s=`/books/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(s)}static async getBookById(e){return this.fetchWithErrorHandling(`/books/${e}/`)}static async getBusinessPlans(e){let t=new URLSearchParams;e?.size&&t.append("size",e.size),e?.category&&t.append("category",e.category),e?.query&&t.append("query",e.query),e?.premium!==void 0&&t.append("premium",e.premium.toString());let s=`/business-plans/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(s)}static async getBusinessPlanById(e){return this.fetchWithErrorHandling(`/business-plans/${e}/`)}static async login(e){return this.fetchWithErrorHandling("/auth/login/",{method:"POST",body:JSON.stringify(e)})}static async register(e){return this.fetchWithErrorHandling("/auth/register/",{method:"POST",body:JSON.stringify(e)})}static async getUserProfile(e){return this.fetchWithErrorHandling(`/user/${e}/`)}static async updateUserProfile(e,t){return this.fetchWithErrorHandling(`/user/${e}/`,{method:"PATCH",body:JSON.stringify(t)})}static async processPayment(e){return this.fetchWithErrorHandling("/payments/",{method:"POST",body:JSON.stringify(e)})}static async verifyPayment(e){return this.fetchWithErrorHandling(`/payments/verify/${e}/`)}static async getPaymentHistory(e){return this.fetchWithErrorHandling(`/payments/?userId=${e}`)}static async getAdminStats(){return this.fetchWithErrorHandling("/admin/stats/")}static async getAdminUsers(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search);let s=`/admin/users/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(s)}static async getUserBookmarks(e){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/`)}static async addBookmark(e,t){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/`,{method:"POST",body:JSON.stringify({bookId:t})})}static async removeBookmark(e,t){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/${t}/`,{method:"DELETE"})}static async getUserAnalytics(e){return this.fetchWithErrorHandling(`/user/${e}/analytics/`)}}var i=s(12362);class n{static delay(e=500){return new Promise(t=>setTimeout(t,e))}static async getBooks(e){await this.delay();let t=[...i.tn];if(e?.category&&"all"!==e.category&&(t=t.filter(t=>t.category.toLowerCase()===e.category.toLowerCase())),e?.query){let s=e.query.toLowerCase();t=t.filter(e=>e.title.toLowerCase().includes(s)||e.author.toLowerCase().includes(s))}if(e?.premium!==void 0){let s=i.hr.filter(t=>t.isPremium===e.premium).map(e=>e.id);t=t.filter(e=>s.includes(e.id))}return e?.limit&&(t=t.slice(0,e.limit)),t}static async getBookById(e){await this.delay();let t=i.hr.find(t=>t.id===e);if(!t)throw Error(`Book with id ${e} not found`);return t}static async getBusinessPlans(e){await this.delay();let t=[...i.RJ];if(e?.size&&"all"!==e.size&&(t=t.filter(t=>t.size===e.size)),e?.category&&"all"!==e.category&&(t=t.filter(t=>t.category.toLowerCase()===e.category.toLowerCase())),e?.query){let s=e.query.toLowerCase();t=t.filter(e=>e.title.toLowerCase().includes(s)||e.category.toLowerCase().includes(s))}return e?.premium!==void 0&&(t=t.filter(t=>t.isPremium===e.premium)),t}static async getBusinessPlanById(e){await this.delay();let t=i.Z9.find(t=>t.id===e);if(!t)throw Error(`Business plan with id ${e} not found`);return t}static async login(e){return await this.delay(),{id:"user-1",firstName:"John",lastName:"Doe",email:e.email,plan:"medium",token:"mock-jwt-token-123"}}static async register(e){return await this.delay(),{id:`user-${Date.now()}`,firstName:e.firstName,lastName:e.lastName,email:e.email,plan:"base",token:"mock-jwt-token-456"}}static async getUserProfile(e){return await this.delay(),{user:{id:e,firstName:"John",lastName:"Doe",email:"<EMAIL>",plan:"medium"},activities:[{id:"activity-1",userId:e,type:"book_read",itemId:"the-psychology-of-money",itemTitle:"The Psychology of Money",timestamp:new Date(Date.now()-864e5).toISOString()},{id:"activity-2",userId:e,type:"plan_viewed",itemId:"small-1",itemTitle:"Local Coffee Shop",timestamp:new Date(Date.now()-1728e5).toISOString()}]}}static async updateUserProfile(e,t){return await this.delay(),{id:e,firstName:t.firstName||"John",lastName:t.lastName||"Doe",email:t.email||"<EMAIL>",plan:"medium"}}static async processPayment(e){return await this.delay(1e3),{success:!0,transactionId:`TRX-${Date.now()}-${Math.floor(1e3*Math.random())}`,message:"Payment processed successfully",timestamp:new Date().toISOString(),plan:e.plan}}static async verifyPayment(e){return await this.delay(),{success:!0,status:"completed",message:"Payment verified successfully"}}static async getPaymentHistory(e){return await this.delay(),[{success:!0,transactionId:"TRX-123456789",message:"Payment processed successfully",timestamp:new Date(Date.now()-2592e6).toISOString(),plan:"medium"}]}static async getAdminStats(){return await this.delay(),{totalUsers:1250,totalBooks:i.hr.length,totalBusinessPlans:i.Z9.length,revenueThisMonth:15750}}static async getAdminUsers(e){await this.delay();let t=[{id:"user-1",firstName:"John",lastName:"Doe",email:"<EMAIL>",plan:"medium",createdAt:"2023-01-15T00:00:00Z"},{id:"user-2",firstName:"Jane",lastName:"Smith",email:"<EMAIL>",plan:"base",createdAt:"2023-03-22T00:00:00Z"}];return{users:t,total:t.length,page:e?.page||1,totalPages:1}}static async getUserBookmarks(e){return await this.delay(),i.tn.slice(0,2)}static async addBookmark(e,t){return await this.delay(),{success:!0}}static async removeBookmark(e,t){return await this.delay(),{success:!0}}static async getUserAnalytics(e){return await this.delay(),{readingStats:{booksRead:12,totalReadingTime:2400,averageReadingSpeed:250,streakDays:7},recentActivity:[{id:"activity-1",type:"book_read",itemTitle:"The Psychology of Money",timestamp:new Date(Date.now()-864e5).toISOString()},{id:"activity-2",type:"plan_viewed",itemTitle:"Local Coffee Shop",timestamp:new Date(Date.now()-1728e5).toISOString()}]}}}let l="true"===process.env.NEXT_PUBLIC_USE_MOCK_API?n:r,o=e=>e instanceof Error?e.message:"An unexpected error occurred";process.env.NEXT_PUBLIC_API_BASE_URL;let c={getToken:()=>null,setToken:e=>{},removeToken:()=>{},isAuthenticated:()=>!!c.getToken()},d={getCurrentUser:()=>null,setCurrentUser:e=>{},removeCurrentUser:()=>{},logout:()=>{c.removeToken(),d.removeCurrentUser()}}},82901:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(41680).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},4643:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(41680).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},86235:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(41680).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1284:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\account\\page.tsx","default")},55613:(e,t,s)=>{"use strict";s.d(t,{B8:()=>A,UC:()=>$,bL:()=>S,l9:()=>B});var a=s(58009),r=s(31412),i=s(6004),n=s(48305),l=s(98060),o=s(30830),c=s(59018),d=s(13024),m=s(30096),u=s(45512),h="Tabs",[p,x]=(0,i.A)(h,[n.RG]),f=(0,n.RG)(),[g,y]=p(h),j=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:r,defaultValue:i,orientation:n="horizontal",dir:l,activationMode:p="automatic",...x}=e,f=(0,c.jH)(l),[y,j]=(0,d.i)({prop:a,onChange:r,defaultProp:i??"",caller:h});return(0,u.jsx)(g,{scope:s,baseId:(0,m.B)(),value:y,onValueChange:j,orientation:n,dir:f,activationMode:p,children:(0,u.jsx)(o.sG.div,{dir:f,"data-orientation":n,...x,ref:t})})});j.displayName=h;var v="TabsList",b=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...r}=e,i=y(v,s),l=f(s);return(0,u.jsx)(n.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:a,children:(0,u.jsx)(o.sG.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:t})})});b.displayName=v;var N="TabsTrigger",w=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:i=!1,...l}=e,c=y(N,s),d=f(s),m=T(c.baseId,a),h=C(c.baseId,a),p=a===c.value;return(0,u.jsx)(n.q7,{asChild:!0,...d,focusable:!i,active:p,children:(0,u.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...l,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||i||!e||c.onValueChange(a)})})})});w.displayName=N;var k="TabsContent",P=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,forceMount:i,children:n,...c}=e,d=y(k,s),m=T(d.baseId,r),h=C(d.baseId,r),p=r===d.value,x=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(l.C,{present:i||p,children:({present:s})=>(0,u.jsx)(o.sG.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":m,hidden:!s,id:h,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:s&&n})})});function T(e,t){return`${e}-trigger-${t}`}function C(e,t){return`${e}-content-${t}`}P.displayName=k;var S=j,A=b,B=w,$=P}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[884,999,756],()=>s(74295));module.exports=a})();