"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/page",{

/***/ "(app-pages-browser)/./app/books/page.tsx":
/*!****************************!*\
  !*** ./app/books/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BooksPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_book_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/book-card */ \"(app-pages-browser)/./components/book-card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Gift,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Gift,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction BooksPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [priceFilter, setPriceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [books, setBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredBooks, setFilteredBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch books from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BooksPage.useEffect\": ()=>{\n            const fetchBooks = {\n                \"BooksPage.useEffect.fetchBooks\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const data = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.api.getBooks();\n                        setBooks(data);\n                        setFilteredBooks(data);\n                    } catch (err) {\n                        setError((0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.handleApiError)(err));\n                        console.error(\"Error fetching books:\", err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BooksPage.useEffect.fetchBooks\"];\n            fetchBooks();\n        }\n    }[\"BooksPage.useEffect\"], []);\n    // Filter books based on search query, active category, and price filter\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BooksPage.useEffect\": ()=>{\n            let result = [\n                ...books\n            ];\n            if (activeCategory !== \"all\") {\n                result = result.filter({\n                    \"BooksPage.useEffect\": (book)=>book.category.toLowerCase() === activeCategory.toLowerCase()\n                }[\"BooksPage.useEffect\"]);\n            }\n            if (priceFilter !== \"all\") {\n                if (priceFilter === \"free\") {\n                    result = result.filter({\n                        \"BooksPage.useEffect\": (book)=>book.isFree || book.price !== undefined && book.price === 0\n                    }[\"BooksPage.useEffect\"]);\n                } else if (priceFilter === \"paid\") {\n                    result = result.filter({\n                        \"BooksPage.useEffect\": (book)=>!book.isFree && book.price !== undefined && book.price > 0\n                    }[\"BooksPage.useEffect\"]);\n                }\n            }\n            if (searchQuery) {\n                const query = searchQuery.toLowerCase();\n                result = result.filter({\n                    \"BooksPage.useEffect\": (book)=>book.title.toLowerCase().includes(query) || book.author.toLowerCase().includes(query)\n                }[\"BooksPage.useEffect\"]);\n            }\n            setFilteredBooks(result);\n        }\n    }[\"BooksPage.useEffect\"], [\n        books,\n        searchQuery,\n        activeCategory,\n        priceFilter\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-8 md:py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold tracking-tight mb-2\",\n                                children: \"Digital Books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Discover and purchase premium digital books - secure reading, no downloads\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/free-books\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"bg-green-100 text-green-800 hover:bg-green-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Browse Free Books\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full md:w-auto flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full sm:w-64\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        type: \"search\",\n                                        placeholder: \"Search books...\",\n                                        className: \"w-full pl-8 rounded-full bg-card border-none\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                                        defaultValue: \"all\",\n                                        value: activeCategory,\n                                        onValueChange: setActiveCategory,\n                                        className: \"w-full sm:w-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                            className: \"bg-muted/50 p-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"all\",\n                                                    className: \"rounded-full\",\n                                                    children: \"All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"finance\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Finance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"productivity\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Productivity\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"entrepreneurship\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Entrepreneurship\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                                        defaultValue: \"all\",\n                                        value: priceFilter,\n                                        onValueChange: (value)=>setPriceFilter(value),\n                                        className: \"w-full sm:w-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                            className: \"bg-muted/50 p-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"all\",\n                                                    className: \"rounded-full\",\n                                                    children: \"All Books\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"free\",\n                                                    className: \"rounded-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"mr-1 h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Free\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"paid\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Premium\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                children: [\n                    ...Array(8)\n                ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-[3/4] bg-muted rounded-lg mb-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-3/4 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-muted rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-destructive\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>window.location.reload(),\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                        children: filteredBooks.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_book_card__WEBPACK_IMPORTED_MODULE_7__.BookCard, {\n                                title: book.title,\n                                author: book.author,\n                                coverUrl: book.coverUrl,\n                                category: book.category,\n                                rating: book.rating,\n                                id: book.id,\n                                price: book.price || 9.99,\n                                isPurchased: book.isPurchased || false,\n                                isFree: book.isFree || book.price === 0\n                            }, book.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    filteredBooks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"No books found matching your search criteria.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 13\n                    }, this),\n                    filteredBooks.length > 0 && filteredBooks.length < books.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"rounded-full\",\n                            onClick: ()=>{\n                                setSearchQuery(\"\");\n                                setActiveCategory(\"all\");\n                            },\n                            children: \"Clear Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 13\n                    }, this),\n                    filteredBooks.length === books.length && books.length >= 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"rounded-full\",\n                            children: \"Load More\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(BooksPage, \"yXKv1xEVpLkoYf3pxU7uvRj6JiA=\");\n_c = BooksPage;\nvar _c;\n$RefreshReg$(_c, \"BooksPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/books/page.tsx\n"));

/***/ })

});