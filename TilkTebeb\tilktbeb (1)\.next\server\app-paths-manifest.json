{"/_not-found/page": "app/_not-found/page.js", "/account/page": "app/account/page.js", "/analytics/page": "app/analytics/page.js", "/blog/page": "app/blog/page.js", "/bookmarks/page": "app/bookmarks/page.js", "/books/[slug]/page": "app/books/[slug]/page.js", "/books/page": "app/books/page.js", "/bundles/page": "app/bundles/page.js", "/business-plans/[size]/[id]/page": "app/business-plans/[size]/[id]/page.js", "/business-plans/page": "app/business-plans/page.js", "/checkout/page": "app/checkout/page.js", "/library/page": "app/library/page.js", "/login/page": "app/login/page.js", "/page": "app/page.js", "/offline-library/page": "app/offline-library/page.js", "/pricing/page": "app/pricing/page.js", "/privacy/page": "app/privacy/page.js", "/reader/[bookId]/page": "app/reader/[bookId]/page.js", "/security/page": "app/security/page.js", "/settings/page": "app/settings/page.js", "/signup/page": "app/signup/page.js", "/terms/page": "app/terms/page.js", "/admin/books/[id]/page": "app/admin/books/[id]/page.js", "/admin/books/page": "app/admin/books/page.js", "/admin/login/page": "app/admin/login/page.js", "/admin/page": "app/admin/page.js"}