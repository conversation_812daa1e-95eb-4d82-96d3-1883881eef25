"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/page",{

/***/ "(app-pages-browser)/./components/book-card.tsx":
/*!**********************************!*\
  !*** ./components/book-card.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookCard: () => (/* binding */ BookCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ BookCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction BookCard(param) {\n    let { id, title, author, coverUrl, category, rating, price = 9.99, isPurchased = false, isFree = false } = param;\n    _s();\n    const [showPurchaseModal, setShowPurchaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const book = {\n        id,\n        title,\n        author,\n        coverUrl,\n        category,\n        rating,\n        price,\n        isPurchased,\n        isFree,\n        pages: 300,\n        language: \"English\",\n        summary: \"<p>Discover the insights and strategies that make this book a must-read in the \".concat(category, \" category.</p>\")\n    };\n    const isBookFree = isFree || price === 0;\n    const handlePurchaseClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setShowPurchaseModal(true);\n    };\n    const handlePurchaseComplete = ()=>{\n        // Handle successful purchase\n        console.log(\"Successfully purchased \".concat(title));\n    // You could update the book state here or refresh the page\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"book-card overflow-hidden h-full transition-all duration-300 hover:shadow-xl group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/books/\".concat(id),\n                className: \"block\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-[3/4] relative overflow-hidden rounded-t-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: coverUrl || \"/placeholder.svg\",\n                                alt: \"\".concat(title, \" book cover\"),\n                                className: \"object-cover w-full h-full transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 glass dark:glass-dark text-secondary text-xs px-2 py-1 rounded-full\",\n                                children: category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            isPurchased && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: \"Owned\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            isBookFree && !isPurchased && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: \"FREE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4 glass dark:glass-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-sm\",\n                                children: author\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 fill-secondary text-secondary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: rating.toFixed(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-primary\",\n                                        children: isBookFree ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-green-500 hover:bg-green-600\",\n                                            children: \"FREE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this) : \"$\".concat(price.toFixed(2))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                className: \"p-4 pt-0 glass dark:glass-dark\",\n                children: isPurchased ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full\",\n                    variant: \"outline\",\n                    children: \"Read Now\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full gap-2\",\n                    onClick: handlePurchaseClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        \"Buy Now\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(BookCard, \"GMDkQOp6xhk7B/PDWUW4Vk/PMws=\");\n_c = BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/book-card.tsx\n"));

/***/ })

});