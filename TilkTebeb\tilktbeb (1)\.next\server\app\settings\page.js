(()=>{var e={};e.id=662,e.ids=[662],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},15269:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(70260),n=s(28203),i=s(25155),a=s.n(i),o=s(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,42619)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\settings\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},95933:(e,t,s)=>{Promise.resolve().then(s.bind(s,80953)),Promise.resolve().then(s.bind(s,7086)),Promise.resolve().then(s.bind(s,42591)),Promise.resolve().then(s.bind(s,2281)),Promise.resolve().then(s.bind(s,36805))},14493:(e,t,s)=>{Promise.resolve().then(s.bind(s,64381)),Promise.resolve().then(s.bind(s,51186)),Promise.resolve().then(s.bind(s,53261)),Promise.resolve().then(s.bind(s,44105)),Promise.resolve().then(s.bind(s,69193))},53261:(e,t,s)=>{"use strict";s.d(t,{Label:()=>c});var r=s(45512),n=s(58009),i=s(30830),a=n.forwardRef((e,t)=>(0,r.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var o=s(21643),l=s(59462);let d=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a,{ref:s,className:(0,l.cn)(d(),e),...t}));c.displayName=a.displayName},44105:(e,t,s)=>{"use strict";s.d(t,{Switch:()=>N});var r=s(45512),n=s(58009),i=s(31412),a=s(29952),o=s(6004),l=s(13024),d=s(66582),c=s(38762),u=s(30830),p="Switch",[m,f]=(0,o.A)(p),[b,h]=m(p),x=n.forwardRef((e,t)=>{let{__scopeSwitch:s,name:o,checked:d,defaultChecked:c,required:m,disabled:f,value:h="on",onCheckedChange:x,form:v,...g}=e,[w,N]=n.useState(null),k=(0,a.s)(t,e=>N(e)),T=n.useRef(!1),C=!w||v||!!w.closest("form"),[R,P]=(0,l.i)({prop:d,defaultProp:c??!1,onChange:x,caller:p});return(0,r.jsxs)(b,{scope:s,checked:R,disabled:f,children:[(0,r.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":R,"aria-required":m,"data-state":y(R),"data-disabled":f?"":void 0,disabled:f,value:h,...g,ref:k,onClick:(0,i.m)(e.onClick,e=>{P(e=>!e),C&&(T.current=e.isPropagationStopped(),T.current||e.stopPropagation())})}),C&&(0,r.jsx)(j,{control:w,bubbles:!T.current,name:o,value:h,checked:R,required:m,disabled:f,form:v,style:{transform:"translateX(-100%)"}})]})});x.displayName=p;var v="SwitchThumb",g=n.forwardRef((e,t)=>{let{__scopeSwitch:s,...n}=e,i=h(v,s);return(0,r.jsx)(u.sG.span,{"data-state":y(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:t})});g.displayName=v;var j=n.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:i=!0,...o},l)=>{let u=n.useRef(null),p=(0,a.s)(u,l),m=(0,d.Z)(s),f=(0,c.X)(t);return n.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==s&&t){let r=new Event("click",{bubbles:i});t.call(e,s),e.dispatchEvent(r)}},[m,s,i]),(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...o,tabIndex:-1,ref:p,style:{...o.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var w=s(59462);let N=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(x,{className:(0,w.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:s,children:(0,r.jsx)(g,{className:(0,w.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));N.displayName=x.displayName},69193:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>o,TabsContent:()=>c,TabsList:()=>l,TabsTrigger:()=>d});var r=s(45512),n=s(58009),i=s(55613),a=s(59462);let o=i.bL,l=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.B8,{ref:s,className:(0,a.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=i.B8.displayName;let d=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.l9,{ref:s,className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));d.displayName=i.l9.displayName;let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.UC,{ref:s,className:(0,a.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=i.UC.displayName},42619:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(62740),n=s(36805),i=s(19935),a=s(7248),o=s(76301),l=s(55946);let d=o.forwardRef(({className:e,type:t,...s},n)=>(0,r.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...s}));d.displayName="Input";var c=s(42591),u=s(2281),p=s(80953),m=s(7086);function f(){return(0,r.jsxs)("div",{className:"container py-8 md:py-12",children:[(0,r.jsx)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight mb-2",children:"Settings"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your account settings and preferences"})]})}),(0,r.jsxs)(n.Tabs,{defaultValue:"profile",className:"space-y-8",children:[(0,r.jsxs)(n.TabsList,{className:"bg-muted/50 p-1 rounded-lg",children:[(0,r.jsx)(n.TabsTrigger,{value:"profile",children:"Profile"}),(0,r.jsx)(n.TabsTrigger,{value:"appearance",children:"Appearance"}),(0,r.jsx)(n.TabsTrigger,{value:"notifications",children:"Notifications"}),(0,r.jsx)(n.TabsTrigger,{value:"reading",children:"Reading"})]}),(0,r.jsx)(n.TabsContent,{value:"profile",children:(0,r.jsxs)("div",{className:"grid gap-8 md:grid-cols-2",children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Personal Information"}),(0,r.jsx)(i.BT,{children:"Update your personal details"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{htmlFor:"first-name",children:"First Name"}),(0,r.jsx)(d,{id:"first-name",placeholder:"John"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{htmlFor:"last-name",children:"Last Name"}),(0,r.jsx)(d,{id:"last-name",placeholder:"Doe"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{htmlFor:"email",children:"Email"}),(0,r.jsx)(d,{id:"email",type:"email",placeholder:"<EMAIL>"})]})]}),(0,r.jsx)(i.wL,{children:(0,r.jsx)(a.$,{children:"Save Changes"})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Password"}),(0,r.jsx)(i.BT,{children:"Change your password"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{htmlFor:"current-password",children:"Current Password"}),(0,r.jsx)(d,{id:"current-password",type:"password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{htmlFor:"new-password",children:"New Password"}),(0,r.jsx)(d,{id:"new-password",type:"password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{htmlFor:"confirm-password",children:"Confirm New Password"}),(0,r.jsx)(d,{id:"confirm-password",type:"password"})]})]}),(0,r.jsx)(i.wL,{children:(0,r.jsx)(a.$,{children:"Update Password"})})]})]})}),(0,r.jsx)(n.TabsContent,{value:"appearance",children:(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Appearance"}),(0,r.jsx)(i.BT,{children:"Customize how TelkTibeb looks"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{children:"Theme"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(m.ThemeToggle,{}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Toggle between light and dark mode"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{children:"Background Style"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(p.ThemeSelector,{}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Choose a background theme"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(c.Label,{htmlFor:"reduced-motion",children:"Reduced Motion"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Reduce animations and transitions"})]}),(0,r.jsx)(u.Switch,{id:"reduced-motion"})]})]})]})}),(0,r.jsx)(n.TabsContent,{value:"notifications",children:(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Notification Settings"}),(0,r.jsx)(i.BT,{children:"Manage how you receive notifications"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(c.Label,{htmlFor:"new-books",children:"New Book Summaries"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Get notified when new book summaries are added"})]}),(0,r.jsx)(u.Switch,{id:"new-books",defaultChecked:!0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(c.Label,{htmlFor:"new-plans",children:"New Business Plans"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Get notified when new business plans are added"})]}),(0,r.jsx)(u.Switch,{id:"new-plans",defaultChecked:!0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(c.Label,{htmlFor:"promotions",children:"Promotions"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive promotional offers and discounts"})]}),(0,r.jsx)(u.Switch,{id:"promotions"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(c.Label,{htmlFor:"email-notifications",children:"Email Notifications"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via email"})]}),(0,r.jsx)(u.Switch,{id:"email-notifications",defaultChecked:!0})]})]})]})}),(0,r.jsx)(n.TabsContent,{value:"reading",children:(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Reading Preferences"}),(0,r.jsx)(i.BT,{children:"Customize your reading experience"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(c.Label,{htmlFor:"offline-auto-save",children:"Auto-save for Offline"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically save books you read for offline access"})]}),(0,r.jsx)(u.Switch,{id:"offline-auto-save"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)(c.Label,{htmlFor:"auto-bookmark",children:"Auto-bookmark"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically bookmark books you're reading"})]}),(0,r.jsx)(u.Switch,{id:"auto-bookmark",defaultChecked:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.Label,{children:"Default Font Size"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(a.$,{variant:"outline",size:"sm",className:"rounded-full h-8 w-8 p-0",children:"S"}),(0,r.jsx)(a.$,{variant:"default",size:"sm",className:"rounded-full h-8 w-8 p-0",children:"M"}),(0,r.jsx)(a.$,{variant:"outline",size:"sm",className:"rounded-full h-8 w-8 p-0",children:"L"})]})]})]})]})})]})]})}},80953:(e,t,s)=>{"use strict";s.d(t,{ThemeSelector:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call ThemeSelector() from the server but ThemeSelector is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\theme-selector.tsx","ThemeSelector")},7086:(e,t,s)=>{"use strict";s.d(t,{ThemeToggle:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\theme-toggle.tsx","ThemeToggle")},7248:(e,t,s)=>{"use strict";s.d(t,{$:()=>p});var r=s(62740),n=s(76301);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var a=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:s,...r}=e;if(n.isValidElement(s)){let e,a;let o=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?s.ref:(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?s.props.ref:s.props.ref||s.ref,l=function(e,t){let s={...t};for(let r in t){let n=e[r],i=t[r];/^on[A-Z]/.test(r)?n&&i?s[r]=(...e)=>{let t=i(...e);return n(...e),t}:n&&(s[r]=n):"style"===r?s[r]={...n,...i}:"className"===r&&(s[r]=[n,i].filter(Boolean).join(" "))}return{...e,...s}}(r,s.props);return s.type!==n.Fragment&&(l.ref=t?function(...e){return t=>{let s=!1,r=e.map(e=>{let r=i(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():i(e[t],null)}}}}(t,o):o),n.cloneElement(s,l)}return n.Children.count(s)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=n.forwardRef((e,s)=>{let{children:i,...a}=e,o=n.Children.toArray(i),d=o.find(l);if(d){let e=d.props.children,i=o.map(t=>t!==d?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,r.jsx)(t,{...a,ref:s,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,r.jsx)(t,{...a,ref:s,children:i})});return s.displayName=`${e}.Slot`,s}("Slot"),o=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var d=s(67699),c=s(55946);let u=(0,d.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=n.forwardRef(({className:e,variant:t,size:s,asChild:n=!1,...i},o)=>{let l=n?a:"button";return(0,r.jsx)(l,{className:(0,c.cn)(u({variant:t,size:s,className:e})),ref:o,...i})});p.displayName="Button"},19935:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>a,aR:()=>o,wL:()=>u});var r=s(62740),n=s(76301),i=s(55946);let a=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));a.displayName="Card";let o=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},42591:(e,t,s)=>{"use strict";s.d(t,{Label:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call Label() from the server but Label is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\ui\\label.tsx","Label")},2281:(e,t,s)=>{"use strict";s.d(t,{Switch:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call Switch() from the server but Switch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\ui\\switch.tsx","Switch")},36805:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>n,TabsContent:()=>o,TabsList:()=>i,TabsTrigger:()=>a});var r=s(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\ui\\tabs.tsx","Tabs"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\ui\\tabs.tsx","TabsList"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\ui\\tabs.tsx","TabsTrigger"),o=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\ui\\tabs.tsx","TabsContent")},55946:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var r=s(13673),n=s(47317);function i(...e){return(0,n.QP)((0,r.$)(e))}},55613:(e,t,s)=>{"use strict";s.d(t,{B8:()=>L,UC:()=>S,bL:()=>P,l9:()=>A});var r=s(58009),n=s(31412),i=s(6004),a=s(48305),o=s(98060),l=s(30830),d=s(59018),c=s(13024),u=s(30096),p=s(45512),m="Tabs",[f,b]=(0,i.A)(m,[a.RG]),h=(0,a.RG)(),[x,v]=f(m),g=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:n,defaultValue:i,orientation:a="horizontal",dir:o,activationMode:f="automatic",...b}=e,h=(0,d.jH)(o),[v,g]=(0,c.i)({prop:r,onChange:n,defaultProp:i??"",caller:m});return(0,p.jsx)(x,{scope:s,baseId:(0,u.B)(),value:v,onValueChange:g,orientation:a,dir:h,activationMode:f,children:(0,p.jsx)(l.sG.div,{dir:h,"data-orientation":a,...b,ref:t})})});g.displayName=m;var j="TabsList",y=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...n}=e,i=v(j,s),o=h(s);return(0,p.jsx)(a.bL,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:r,children:(0,p.jsx)(l.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});y.displayName=j;var w="TabsTrigger",N=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:i=!1,...o}=e,d=v(w,s),c=h(s),u=C(d.baseId,r),m=R(d.baseId,r),f=r===d.value;return(0,p.jsx)(a.q7,{asChild:!0,...c,focusable:!i,active:f,children:(0,p.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":m,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...o,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;f||i||!e||d.onValueChange(r)})})})});N.displayName=w;var k="TabsContent",T=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:n,forceMount:i,children:a,...d}=e,c=v(k,s),u=C(c.baseId,n),m=R(c.baseId,n),f=n===c.value,b=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>b.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(o.C,{present:i||f,children:({present:s})=>(0,p.jsx)(l.sG.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:m,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:b.current?"0s":void 0},children:s&&a})})});function C(e,t){return`${e}-trigger-${t}`}function R(e,t){return`${e}-content-${t}`}T.displayName=k;var P=g,L=y,A=N,S=T},66582:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(58009);function n(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},67699:(e,t,s)=>{"use strict";s.d(t,{F:()=>a});var r=s(13673);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,a=(e,t)=>s=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:a,defaultVariants:o}=t,l=Object.keys(a).map(e=>{let t=null==s?void 0:s[e],r=null==o?void 0:o[e];if(null===t)return null;let i=n(t)||n(r);return a[e][i]}),d=s&&Object.entries(s).reduce((e,t)=>{let[s,r]=t;return void 0===r||(e[s]=r),e},{});return i(e,l,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:s,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...o,...d}[t]):({...o,...d})[t]===s})?[...e,s,r]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[884,85,999],()=>s(15269));module.exports=r})();