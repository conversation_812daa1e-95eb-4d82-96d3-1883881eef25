(()=>{var e={};e.id=528,e.ids=[528],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},7031:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=a(70260),n=a(28203),s=a(25155),o=a.n(s),i=a(67292),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let d=["",{children:["offline-library",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,42654)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\offline-library\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(a.bind(a,16327)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\offline-library\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\offline-library\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/offline-library/page",pathname:"/offline-library",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55541:(e,t,a)=>{Promise.resolve().then(a.bind(a,42654))},97397:(e,t,a)=>{Promise.resolve().then(a.bind(a,75015))},96487:()=>{},78335:()=>{},75015:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>Z});var r=a(45512),n=a(58009),s=a(28531),o=a.n(s),i=a(87021),l=a(97643),d=a(25409),c=a(69193),u=a(54069);let m="books",f="business-plans",h="reading-progress",p="notes",x="highlights";function b(){return new Promise((e,t)=>{let a=indexedDB.open("telktibeb-offline",2);a.onupgradeneeded=e=>{let t=e.target.result;t.objectStoreNames.contains(m)||t.createObjectStore(m,{keyPath:"id"}),t.objectStoreNames.contains(f)||t.createObjectStore(f,{keyPath:"id"}),t.objectStoreNames.contains(h)||t.createObjectStore(h,{keyPath:"id"}).createIndex("contentId","contentId",{unique:!1}),t.objectStoreNames.contains(p)||t.createObjectStore(p,{keyPath:"id"}).createIndex("contentId","contentId",{unique:!1}),t.objectStoreNames.contains(x)||t.createObjectStore(x,{keyPath:"id"}).createIndex("contentId","contentId",{unique:!1})},a.onsuccess=t=>{e(t.target.result)},a.onerror=e=>{console.error("Database error:",e.target.error),t(Error("Failed to open database"))}})}async function g(e){try{let t=await b(),a=t.transaction(m,"readonly"),r=a.objectStore(m);return new Promise((n,s)=>{let o=r.getAll();o.onsuccess=()=>{let t=o.result;e?.filterBy&&e?.filterValue&&(t=t.filter(t=>{let a=t[e.filterBy];return a&&a.toString().toLowerCase().includes(e.filterValue.toLowerCase())})),e?.sortBy?t.sort((t,a)=>{let r=t[e.sortBy],n=a[e.sortBy];return r<n?"desc"===e.sortOrder?1:-1:r>n?"desc"===e.sortOrder?-1:1:0}):t.sort((e,t)=>new Date(t.savedAt).getTime()-new Date(e.savedAt).getTime()),n(t)},o.onerror=()=>s(o.error),a.oncomplete=()=>t.close()})}catch(e){throw console.error("Error getting all offline books:",e),e}}async function y(e){try{let t=await b(),a=t.transaction(m,"readwrite"),r=a.objectStore(m);return new Promise((n,s)=>{let o=r.delete(e);o.onsuccess=()=>n(),o.onerror=()=>s(o.error),a.oncomplete=()=>t.close()})}catch(e){throw console.error("Error deleting offline book:",e),e}}async function v(){try{let e=await b(),t=e.transaction(m,"readonly"),a=e.transaction(f,"readonly"),r=t.objectStore(m),n=a.objectStore(f),s=new Promise((e,t)=>{let a=r.count();a.onsuccess=()=>e(a.result),a.onerror=()=>t(a.error)}),o=new Promise((e,t)=>{let a=n.count();a.onsuccess=()=>e(a.result),a.onerror=()=>t(a.error)}),[i,l]=await Promise.all([s,o]);return{books:i,businessPlans:l,totalSize:(50*i+30*l)*1024}}catch(e){throw console.error("Error getting storage stats:",e),e}}async function j(){try{let e=await b(),t=[m,f,h,p,x].map(t=>new Promise((a,r)=>{let n=e.transaction(t,"readwrite"),s=n.objectStore(t).clear();s.onsuccess=()=>a(),s.onerror=()=>r(s.error),n.oncomplete=()=>e.close()}));await Promise.all(t)}catch(e){throw console.error("Error clearing offline data:",e),e}}var w=a(52706),N=a(16873),k=a(86235),M=a(4269),C=a(4643),P=a(49656),A=a(41680);let D=(0,A.A)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),S=(0,A.A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var T=a(70801),R=a(77766);let W=Symbol.for("constructDateFrom");function F(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&W in e?e[W](t):e instanceof Date?new e.constructor(t):new Date(t)}let B={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function E(e){return (t={})=>{let a=t.width?String(t.width):e.defaultWidth;return e.formats[a]||e.formats[e.defaultWidth]}}let q={date:E({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:E({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:E({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},z={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function L(e){return(t,a)=>{let r;if("formatting"===(a?.context?String(a.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,n=a?.width?String(a.width):t;r=e.formattingValues[n]||e.formattingValues[t]}else{let t=e.defaultWidth,n=a?.width?String(a.width):e.defaultWidth;r=e.values[n]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function O(e){return(t,a={})=>{let r;let n=a.width,s=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],o=t.match(s);if(!o)return null;let i=o[0],l=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(let a=0;a<e.length;a++)if(t(e[a]))return a}(l,e=>e.test(i)):function(e,t){for(let a in e)if(Object.prototype.hasOwnProperty.call(e,a)&&t(e[a]))return a}(l,e=>e.test(i));return r=e.valueCallback?e.valueCallback(d):d,{value:r=a.valueCallback?a.valueCallback(r):r,rest:t.slice(i.length)}}}let U={code:"en-US",formatDistance:(e,t,a)=>{let r;let n=B[e];return(r="string"==typeof n?n:1===t?n.one:n.other.replace("{{count}}",t.toString()),a?.addSuffix)?a.comparison&&a.comparison>0?"in "+r:r+" ago":r},formatLong:q,formatRelative:(e,t,a,r)=>z[e],localize:{ordinalNumber:(e,t)=>{let a=Number(e),r=a%100;if(r>20||r<10)switch(r%10){case 1:return a+"st";case 2:return a+"nd";case 3:return a+"rd"}return a+"th"},era:L({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:L({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:L({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:L({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:L({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,a={})=>{let r=t.match(e.matchPattern);if(!r)return null;let n=r[0],s=t.match(e.parsePattern);if(!s)return null;let o=e.valueCallback?e.valueCallback(s[0]):s[0];return{value:o=a.valueCallback?a.valueCallback(o):o,rest:t.slice(n.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:O({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:O({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:O({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:O({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},Y={};function X(e,t){return F(t||e,e)}function I(e){let t=X(e),a=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return a.setUTCFullYear(t.getFullYear()),+e-+a}function V(e,...t){let a=F.bind(null,e||t.find(e=>"object"==typeof e));return t.map(a)}function _(e,t){let a=+X(e)-+X(t);return a<0?-1:a>0?1:a}var J=a(59462);function Z(){let[e,t]=(0,n.useState)([]),[a,s]=(0,n.useState)([]),[m,f]=(0,n.useState)(!0),[h,p]=(0,n.useState)(""),[x,b]=(0,n.useState)("savedAt"),[A,W]=(0,n.useState)("desc"),[B,E]=(0,n.useState)(null),[q,z]=(0,n.useState)(!1),{toast:L}=(0,T.dj)(),O=async()=>{try{let e=await v();E(e)}catch(e){console.error("Error loading storage stats:",e)}},Z=async e=>{let a=x===e&&"desc"===A?"asc":"desc";b(e),W(a);try{f(!0);let r=await g({sortBy:e,sortOrder:a});t(r)}catch(e){console.error("Error sorting books:",e)}finally{f(!1)}},H=async a=>{try{await y(a),t(e.filter(e=>e.id!==a)),O(),L({title:"Book removed",description:"The book has been removed from your offline library"})}catch(e){console.error("Error deleting book:",e),L({title:"Error",description:"Failed to remove the book from your offline library",variant:"destructive"})}},$=async()=>{try{z(!0),await j(),t([]),s([]),O(),L({title:"Offline library cleared",description:"All offline content has been removed"})}catch(e){console.error("Error clearing offline data:",e),L({title:"Error",description:"Failed to clear offline library",variant:"destructive"})}finally{z(!1)}};return(0,r.jsxs)("div",{className:"container py-8 md:py-12",children:[(0,r.jsxs)(o(),{href:"/books",className:"inline-flex items-center gap-1 text-muted-foreground hover:text-foreground mb-6",children:[(0,r.jsx)(w.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Back to Books"})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight mb-2",children:"Offline Library"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Books and summaries you've saved for offline reading"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(N.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(d.p,{type:"search",placeholder:"Search offline books...",className:"pl-8 w-full md:w-[200px]",value:h,onChange:e=>p(e.target.value)})]}),(0,r.jsxs)(u.l6,{value:x,onValueChange:Z,children:[(0,r.jsx)(u.bq,{className:"w-[180px]",children:(0,r.jsx)(u.yv,{placeholder:"Sort by"})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:"savedAt",children:"Date Saved"}),(0,r.jsx)(u.eb,{value:"title",children:"Title"}),(0,r.jsx)(u.eb,{value:"author",children:"Author"}),(0,r.jsx)(u.eb,{value:"category",children:"Category"})]})]})]})]}),(0,r.jsxs)(c.Tabs,{defaultValue:"books",className:"w-full",children:[(0,r.jsxs)(c.TabsList,{children:[(0,r.jsxs)(c.TabsTrigger,{value:"books",children:["Books (",e.length,")"]}),(0,r.jsx)(c.TabsTrigger,{value:"storage",children:"Storage"})]}),(0,r.jsx)(c.TabsContent,{value:"books",children:m?(0,r.jsxs)("div",{className:"flex justify-center items-center h-64",children:[(0,r.jsx)(k.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-2",children:"Loading your offline library..."})]}):0===a.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4",children:(0,r.jsx)(M.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Your offline library is empty"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-6",children:"Save books for offline reading to access them when you don't have an internet connection."}),(0,r.jsx)(o(),{href:"/books",children:(0,r.jsx)(i.$,{children:"Browse Books"})})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:a.map(e=>{var t,a;return(0,r.jsxs)(l.Zp,{className:"flex flex-col h-full",children:[(0,r.jsxs)(l.aR,{className:"pb-4",children:[(0,r.jsx)(l.ZB,{className:"line-clamp-1",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["by ",e.author]})]}),(0,r.jsxs)(l.Wu,{className:"pb-4 flex-grow",children:[(0,r.jsx)("div",{className:"aspect-[3/4] relative overflow-hidden rounded-md mb-4",children:(0,r.jsx)("img",{src:e.coverUrl||"/placeholder.svg?height=240&width=180",alt:`${e.title} book cover`,className:"object-cover w-full h-full"})}),(0,r.jsx)("div",{className:"bg-primary/10 text-primary text-xs px-2 py-1 rounded-full w-fit mb-2",children:e.category}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)(C.A,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:["Saved ",(t=new Date(e.savedAt),a={addSuffix:!0},function(e,t,a){var r,n,s,o;let i;let l=a?.locale??Y.locale??U,d=_(e,t);if(isNaN(d))throw RangeError("Invalid time value");let c=Object.assign({},a,{addSuffix:a?.addSuffix,comparison:d}),[u,m]=V(a?.in,...d>0?[t,e]:[e,t]),f=(r=m,n=u,(o=void 0,e=>{let t=(o?Math[o]:Math.trunc)(e);return 0===t?0:t})((+X(r)-+X(n))/1e3)),h=Math.round((f-(I(m)-I(u))/1e3)/60);if(h<2){if(a?.includeSeconds){if(f<5)return l.formatDistance("lessThanXSeconds",5,c);if(f<10)return l.formatDistance("lessThanXSeconds",10,c);if(f<20)return l.formatDistance("lessThanXSeconds",20,c);if(f<40)return l.formatDistance("halfAMinute",0,c);else if(f<60)return l.formatDistance("lessThanXMinutes",1,c);else return l.formatDistance("xMinutes",1,c)}return 0===h?l.formatDistance("lessThanXMinutes",1,c):l.formatDistance("xMinutes",h,c)}if(h<45)return l.formatDistance("xMinutes",h,c);if(h<90)return l.formatDistance("aboutXHours",1,c);if(h<1440){let e=Math.round(h/60);return l.formatDistance("aboutXHours",e,c)}if(h<2520)return l.formatDistance("xDays",1,c);if(h<43200){let e=Math.round(h/1440);return l.formatDistance("xDays",e,c)}if(h<86400)return i=Math.round(h/43200),l.formatDistance("aboutXMonths",i,c);if((i=function(e,t,a){let[r,n,s]=V(void 0,e,e,t),o=_(n,s),i=Math.abs(function(e,t,a){let[r,n]=V(void 0,e,t);return 12*(r.getFullYear()-n.getFullYear())+(r.getMonth()-n.getMonth())}(n,s));if(i<1)return 0;1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-o*i);let l=_(n,s)===-o;(function(e,t){let a=X(e,void 0);return+function(e,t){let a=X(e,t?.in);return a.setHours(23,59,59,999),a}(a,void 0)==+function(e,t){let a=X(e,t?.in),r=a.getMonth();return a.setFullYear(a.getFullYear(),r+1,0),a.setHours(23,59,59,999),a}(a,void 0)})(r)&&1===i&&1===_(r,s)&&(l=!1);let d=o*(i-+l);return 0===d?0:d}(m,u))<12){let e=Math.round(h/43200);return l.formatDistance("xMonths",e,c)}{let e=i%12,t=Math.trunc(i/12);return e<3?l.formatDistance("aboutXYears",t,c):e<9?l.formatDistance("overXYears",t,c):l.formatDistance("almostXYears",t+1,c)}}(t,F(t,Date.now()),a))]})]})]}),(0,r.jsxs)(l.wL,{className:"flex flex-col gap-2",children:[(0,r.jsx)(o(),{href:`/books/${e.id}`,className:"w-full",children:(0,r.jsx)(i.$,{variant:"outline",className:"w-full",children:"Read Now"})}),(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",className:"w-full text-destructive hover:text-destructive",onClick:()=>H(e.id),children:[(0,r.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Remove"]})]})]},e.id)})})}),(0,r.jsx)(c.TabsContent,{value:"storage",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Storage Usage"})}),(0,r.jsx)(l.Wu,{children:B?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(M.A,{className:"h-5 w-5 text-primary"}),(0,r.jsx)("span",{children:"Books"})]}),(0,r.jsx)("span",{className:"font-medium",children:B.books})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(D,{className:"h-5 w-5 text-primary"}),(0,r.jsx)("span",{children:"Total Storage Used"})]}),(0,r.jsx)("span",{className:"font-medium",children:(0,J.z)(B.totalSize)})]}),(0,r.jsx)("div",{className:"bg-muted p-4 rounded-md mt-4",children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Your browser allows approximately 50-100MB of storage for offline content. Consider removing unused content if you're saving many books."})})]}):(0,r.jsxs)("div",{className:"flex justify-center items-center h-32",children:[(0,r.jsx)(k.A,{className:"h-6 w-6 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-2",children:"Loading storage information..."})]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Manage Offline Data"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"You can clear all offline data if you're experiencing issues or want to free up storage space. This will remove all saved books and reading progress."}),(0,r.jsxs)("div",{className:"bg-amber-50 dark:bg-amber-900/20 p-4 rounded-md border border-amber-200 dark:border-amber-800 flex items-start gap-2",children:[(0,r.jsx)(S,{className:"h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0"}),(0,r.jsx)("p",{className:"text-sm text-amber-800 dark:text-amber-300",children:"Clearing offline data cannot be undone. You'll need to re-download any content you want to access offline."})]})]})}),(0,r.jsx)(l.wL,{children:(0,r.jsxs)(R.Lt,{children:[(0,r.jsx)(R.tv,{asChild:!0,children:(0,r.jsx)(i.$,{variant:"destructive",className:"w-full",disabled:q||B?.books===0,children:q?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(k.A,{className:"h-4 w-4 animate-spin mr-2"}),"Clearing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Clear All Offline Data"]})})}),(0,r.jsxs)(R.EO,{children:[(0,r.jsxs)(R.wd,{children:[(0,r.jsx)(R.r7,{children:"Are you absolutely sure?"}),(0,r.jsx)(R.$v,{children:"This will permanently delete all your offline books and reading progress. This action cannot be undone."})]}),(0,r.jsxs)(R.ck,{children:[(0,r.jsx)(R.Zr,{children:"Cancel"}),(0,r.jsx)(R.Rx,{onClick:$,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Clear All Data"})]})]})]})})]})]})})]})]})}},77766:(e,t,a)=>{"use strict";a.d(t,{$v:()=>x,EO:()=>m,Lt:()=>l,Rx:()=>b,Zr:()=>g,ck:()=>h,r7:()=>p,tv:()=>d,wd:()=>f});var r=a(45512),n=a(58009),s=a(33445),o=a(59462),i=a(87021);let l=s.bL,d=s.l9,c=s.ZL,u=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.hJ,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:a}));u.displayName=s.hJ.displayName;let m=n.forwardRef(({className:e,...t},a)=>(0,r.jsxs)(c,{children:[(0,r.jsx)(u,{}),(0,r.jsx)(s.UC,{ref:a,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));m.displayName=s.UC.displayName;let f=({className:e,...t})=>(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});f.displayName="AlertDialogHeader";let h=({className:e,...t})=>(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});h.displayName="AlertDialogFooter";let p=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.hE,{ref:a,className:(0,o.cn)("text-lg font-semibold",e),...t}));p.displayName=s.hE.displayName;let x=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.VY,{ref:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=s.VY.displayName;let b=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.rc,{ref:a,className:(0,o.cn)((0,i.r)(),e),...t}));b.displayName=s.rc.displayName;let g=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.ZD,{ref:a,className:(0,o.cn)((0,i.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));g.displayName=s.ZD.displayName},97643:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>u});var r=a(45512),n=a(58009),s=a(59462);let o=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let i=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,s.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,s.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},54069:(e,t,a)=>{"use strict";a.d(t,{bq:()=>m,eb:()=>x,gC:()=>p,l6:()=>c,yv:()=>u});var r=a(45512),n=a(58009),s=a(81477),o=a(98755),i=a(28638),l=a(24849),d=a(59462);let c=s.bL;s.YJ;let u=s.WT,m=n.forwardRef(({className:e,children:t,...a},n)=>(0,r.jsxs)(s.l9,{ref:n,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=s.l9.displayName;let f=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}));f.displayName=s.PP.displayName;let h=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})}));h.displayName=s.wn.displayName;let p=n.forwardRef(({className:e,children:t,position:a="popper",...n},o)=>(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{ref:o,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...n,children:[(0,r.jsx)(f,{}),(0,r.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(h,{})]})}));p.displayName=s.UC.displayName,n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=s.JU.displayName;let x=n.forwardRef(({className:e,children:t,...a},n)=>(0,r.jsxs)(s.q7,{ref:n,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})}),(0,r.jsx)(s.p4,{children:t})]}));x.displayName=s.q7.displayName,n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=s.wv.displayName},69193:(e,t,a)=>{"use strict";a.d(t,{Tabs:()=>i,TabsContent:()=>c,TabsList:()=>l,TabsTrigger:()=>d});var r=a(45512),n=a(58009),s=a(55613),o=a(59462);let i=s.bL,l=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.B8,{ref:a,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=s.B8.displayName;let d=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.l9,{ref:a,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));d.displayName=s.l9.displayName;let c=n.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.UC,{ref:a,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=s.UC.displayName},4643:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(41680).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},86235:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(41680).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},16327:(e,t,a)=>{"use strict";function r(){return null}a.r(t),a.d(t,{default:()=>r})},42654:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\offline-library\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\offline-library\\page.tsx","default")},55613:(e,t,a)=>{"use strict";a.d(t,{B8:()=>D,UC:()=>T,bL:()=>A,l9:()=>S});var r=a(58009),n=a(31412),s=a(6004),o=a(48305),i=a(98060),l=a(30830),d=a(59018),c=a(13024),u=a(30096),m=a(45512),f="Tabs",[h,p]=(0,s.A)(f,[o.RG]),x=(0,o.RG)(),[b,g]=h(f),y=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,onValueChange:n,defaultValue:s,orientation:o="horizontal",dir:i,activationMode:h="automatic",...p}=e,x=(0,d.jH)(i),[g,y]=(0,c.i)({prop:r,onChange:n,defaultProp:s??"",caller:f});return(0,m.jsx)(b,{scope:a,baseId:(0,u.B)(),value:g,onValueChange:y,orientation:o,dir:x,activationMode:h,children:(0,m.jsx)(l.sG.div,{dir:x,"data-orientation":o,...p,ref:t})})});y.displayName=f;var v="TabsList",j=r.forwardRef((e,t)=>{let{__scopeTabs:a,loop:r=!0,...n}=e,s=g(v,a),i=x(a);return(0,m.jsx)(o.bL,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:r,children:(0,m.jsx)(l.sG.div,{role:"tablist","aria-orientation":s.orientation,...n,ref:t})})});j.displayName=v;var w="TabsTrigger",N=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,disabled:s=!1,...i}=e,d=g(w,a),c=x(a),u=C(d.baseId,r),f=P(d.baseId,r),h=r===d.value;return(0,m.jsx)(o.q7,{asChild:!0,...c,focusable:!s,active:h,children:(0,m.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":f,"data-state":h?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:u,...i,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||s||!e||d.onValueChange(r)})})})});N.displayName=w;var k="TabsContent",M=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:n,forceMount:s,children:o,...d}=e,c=g(k,a),u=C(c.baseId,n),f=P(c.baseId,n),h=n===c.value,p=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(i.C,{present:s||h,children:({present:a})=>(0,m.jsx)(l.sG.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&o})})});function C(e,t){return`${e}-trigger-${t}`}function P(e,t){return`${e}-content-${t}`}M.displayName=k;var A=y,D=j,S=N,T=M}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[884,174,932,999],()=>a(7031));module.exports=r})();