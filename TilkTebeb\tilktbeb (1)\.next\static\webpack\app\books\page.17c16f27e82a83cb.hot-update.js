"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/page",{

/***/ "(app-pages-browser)/./app/books/page.tsx":
/*!****************************!*\
  !*** ./app/books/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BooksPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_book_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/book-card */ \"(app-pages-browser)/./components/book-card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Gift,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Gift,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction BooksPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [priceFilter, setPriceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [books, setBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredBooks, setFilteredBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch books from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BooksPage.useEffect\": ()=>{\n            const fetchBooks = {\n                \"BooksPage.useEffect.fetchBooks\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const data = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.api.getBooks();\n                        setBooks(data);\n                        setFilteredBooks(data);\n                    } catch (err) {\n                        setError((0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.handleApiError)(err));\n                        console.error(\"Error fetching books:\", err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BooksPage.useEffect.fetchBooks\"];\n            fetchBooks();\n        }\n    }[\"BooksPage.useEffect\"], []);\n    // Filter books based on search query, active category, and price filter\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BooksPage.useEffect\": ()=>{\n            let result = [\n                ...books\n            ];\n            if (activeCategory !== \"all\") {\n                result = result.filter({\n                    \"BooksPage.useEffect\": (book)=>book.category.toLowerCase() === activeCategory.toLowerCase()\n                }[\"BooksPage.useEffect\"]);\n            }\n            if (priceFilter !== \"all\") {\n                if (priceFilter === \"free\") {\n                    result = result.filter({\n                        \"BooksPage.useEffect\": (book)=>book.isFree || book.price !== undefined && book.price === 0\n                    }[\"BooksPage.useEffect\"]);\n                } else if (priceFilter === \"paid\") {\n                    result = result.filter({\n                        \"BooksPage.useEffect\": (book)=>!book.isFree && book.price !== undefined && book.price > 0\n                    }[\"BooksPage.useEffect\"]);\n                }\n            }\n            if (searchQuery) {\n                const query = searchQuery.toLowerCase();\n                result = result.filter({\n                    \"BooksPage.useEffect\": (book)=>book.title.toLowerCase().includes(query) || book.author.toLowerCase().includes(query)\n                }[\"BooksPage.useEffect\"]);\n            }\n            setFilteredBooks(result);\n        }\n    }[\"BooksPage.useEffect\"], [\n        books,\n        searchQuery,\n        activeCategory,\n        priceFilter\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-8 md:py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold tracking-tight mb-2\",\n                                children: \"Digital Books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Discover and purchase premium digital books - secure reading, no downloads\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/free-books\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"bg-green-100 text-green-800 hover:bg-green-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Browse Free Books\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full md:w-auto flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full sm:w-64\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        type: \"search\",\n                                        placeholder: \"Search books...\",\n                                        className: \"w-full pl-8 rounded-full bg-card border-none\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                                        defaultValue: \"all\",\n                                        value: activeCategory,\n                                        onValueChange: setActiveCategory,\n                                        className: \"w-full sm:w-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                            className: \"bg-muted/50 p-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"all\",\n                                                    className: \"rounded-full\",\n                                                    children: \"All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"finance\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Finance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"productivity\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Productivity\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"entrepreneurship\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Entrepreneurship\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                                        defaultValue: \"all\",\n                                        value: priceFilter,\n                                        onValueChange: (value)=>setPriceFilter(value),\n                                        className: \"w-full sm:w-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                            className: \"bg-muted/50 p-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"all\",\n                                                    className: \"rounded-full\",\n                                                    children: \"All Books\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"free\",\n                                                    className: \"rounded-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"mr-1 h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Free\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"paid\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Premium\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                children: [\n                    ...Array(8)\n                ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-[3/4] bg-muted rounded-lg mb-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-3/4 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-muted rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-destructive\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>window.location.reload(),\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                        children: filteredBooks.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_book_card__WEBPACK_IMPORTED_MODULE_7__.BookCard, {\n                                title: book.title,\n                                author: book.author,\n                                coverUrl: book.coverUrl,\n                                category: book.category,\n                                rating: book.rating,\n                                id: book.id,\n                                price: book.price || 9.99,\n                                isPurchased: book.isPurchased || false\n                            }, book.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    filteredBooks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"No books found matching your search criteria.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 13\n                    }, this),\n                    filteredBooks.length > 0 && filteredBooks.length < books.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"rounded-full\",\n                            onClick: ()=>{\n                                setSearchQuery(\"\");\n                                setActiveCategory(\"all\");\n                            },\n                            children: \"Clear Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 13\n                    }, this),\n                    filteredBooks.length === books.length && books.length >= 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"rounded-full\",\n                            children: \"Load More\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(BooksPage, \"yXKv1xEVpLkoYf3pxU7uvRj6JiA=\");\n_c = BooksPage;\nvar _c;\n$RefreshReg$(_c, \"BooksPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/books/page.tsx\n"));

/***/ })

});