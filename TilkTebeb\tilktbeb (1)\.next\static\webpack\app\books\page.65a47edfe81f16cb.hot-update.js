"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/page",{

/***/ "(app-pages-browser)/./app/books/page.tsx":
/*!****************************!*\
  !*** ./app/books/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BooksPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_book_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/book-card */ \"(app-pages-browser)/./components/book-card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Gift,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Gift,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction BooksPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [priceFilter, setPriceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [books, setBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredBooks, setFilteredBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch books from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BooksPage.useEffect\": ()=>{\n            const fetchBooks = {\n                \"BooksPage.useEffect.fetchBooks\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const data = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.api.getBooks();\n                        setBooks(data);\n                        setFilteredBooks(data);\n                    } catch (err) {\n                        setError((0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.handleApiError)(err));\n                        console.error(\"Error fetching books:\", err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BooksPage.useEffect.fetchBooks\"];\n            fetchBooks();\n        }\n    }[\"BooksPage.useEffect\"], []);\n    // Filter books based on search query, active category, and price filter\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BooksPage.useEffect\": ()=>{\n            let result = [\n                ...books\n            ];\n            if (activeCategory !== \"all\") {\n                result = result.filter({\n                    \"BooksPage.useEffect\": (book)=>book.category.toLowerCase() === activeCategory.toLowerCase()\n                }[\"BooksPage.useEffect\"]);\n            }\n            if (priceFilter !== \"all\") {\n                if (priceFilter === \"free\") {\n                    result = result.filter({\n                        \"BooksPage.useEffect\": (book)=>book.isFree || book.price !== undefined && book.price === 0\n                    }[\"BooksPage.useEffect\"]);\n                } else if (priceFilter === \"paid\") {\n                    result = result.filter({\n                        \"BooksPage.useEffect\": (book)=>!book.isFree && book.price !== undefined && book.price > 0\n                    }[\"BooksPage.useEffect\"]);\n                }\n            }\n            if (searchQuery) {\n                const query = searchQuery.toLowerCase();\n                result = result.filter({\n                    \"BooksPage.useEffect\": (book)=>book.title.toLowerCase().includes(query) || book.author.toLowerCase().includes(query)\n                }[\"BooksPage.useEffect\"]);\n            }\n            setFilteredBooks(result);\n        }\n    }[\"BooksPage.useEffect\"], [\n        books,\n        searchQuery,\n        activeCategory,\n        priceFilter\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-8 md:py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold tracking-tight mb-2\",\n                                children: \"Digital Books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Discover and purchase premium digital books - secure reading, no downloads\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/free-books\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"bg-green-100 text-green-800 hover:bg-green-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Browse Free Books\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full md:w-auto flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full sm:w-64\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        type: \"search\",\n                                        placeholder: \"Search books...\",\n                                        className: \"w-full pl-8 rounded-full bg-card border-none\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                                        defaultValue: \"all\",\n                                        value: activeCategory,\n                                        onValueChange: setActiveCategory,\n                                        className: \"w-full sm:w-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                            className: \"bg-muted/50 p-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"all\",\n                                                    className: \"rounded-full\",\n                                                    children: \"All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"finance\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Finance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"productivity\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Productivity\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"entrepreneurship\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Entrepreneurship\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                                        defaultValue: \"all\",\n                                        value: priceFilter,\n                                        onValueChange: (value)=>setPriceFilter(value),\n                                        className: \"w-full sm:w-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                            className: \"bg-muted/50 p-1 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"all\",\n                                                    className: \"rounded-full\",\n                                                    children: \"All Books\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"free\",\n                                                    className: \"rounded-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gift_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"mr-1 h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Free\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                    value: \"paid\",\n                                                    className: \"rounded-full\",\n                                                    children: \"Premium\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                children: [\n                    ...Array(8)\n                ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-[3/4] bg-muted rounded-lg mb-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-3/4 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-muted rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-destructive\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>window.location.reload(),\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                        children: filteredBooks.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_book_card__WEBPACK_IMPORTED_MODULE_7__.BookCard, {\n                                title: book.title,\n                                author: book.author,\n                                coverUrl: book.coverUrl,\n                                category: book.category,\n                                rating: book.rating,\n                                id: book.id,\n                                price: book.price || 9.99,\n                                isPurchased: book.isPurchased || false,\n                                isFree: book.isFree || book.price === 0\n                            }, book.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    filteredBooks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"No books found matching your search criteria.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this),\n                    filteredBooks.length > 0 && filteredBooks.length < books.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"rounded-full\",\n                            onClick: ()=>{\n                                setSearchQuery(\"\");\n                                setActiveCategory(\"all\");\n                            },\n                            children: \"Clear Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, this),\n                    filteredBooks.length === books.length && books.length >= 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"rounded-full\",\n                            children: \"Load More\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(BooksPage, \"yXKv1xEVpLkoYf3pxU7uvRj6JiA=\");\n_c = BooksPage;\nvar _c;\n$RefreshReg$(_c, \"BooksPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/books/page.tsx\n"));

/***/ })

});