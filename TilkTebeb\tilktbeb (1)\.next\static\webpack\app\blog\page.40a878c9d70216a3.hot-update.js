"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./app/blog/page.tsx":
/*!***************************!*\
  !*** ./app/blog/page.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_blog_data__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/blog-data */ \"(app-pages-browser)/./lib/blog-data.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Use the blog posts from the centralized data file\nconst mockPosts = [\n    ..._lib_blog_data__WEBPACK_IMPORTED_MODULE_7__.mockBlogPosts,\n    // Additional posts for the listing page\n    {\n        id: \"1\",\n        title: \"The Future of Digital Reading: Why Security Matters\",\n        excerpt: \"Explore how secure digital reading platforms are revolutionizing the way we consume books while protecting authors' intellectual property.\",\n        author: \"Sarah Johnson\",\n        publishedDate: \"2024-01-15\",\n        readTime: 5,\n        category: \"Technology\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"future-digital-reading-security\"\n    },\n    {\n        id: \"2\",\n        title: \"Author Interview: Building a Sustainable Writing Career\",\n        excerpt: \"We sit down with bestselling author Michael Chen to discuss the challenges and opportunities in today's publishing landscape.\",\n        author: \"Emma Davis\",\n        publishedDate: \"2024-01-12\",\n        readTime: 8,\n        category: \"Author Interview\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"author-interview-michael-chen\"\n    },\n    {\n        id: \"3\",\n        title: \"5 Essential Writing Tips from Industry Professionals\",\n        excerpt: \"Learn the secrets that professional writers use to craft compelling narratives and engage their readers from page one.\",\n        author: \"David Wilson\",\n        publishedDate: \"2024-01-10\",\n        readTime: 6,\n        category: \"Writing Tips\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"essential-writing-tips-professionals\"\n    },\n    {\n        id: \"4\",\n        title: \"Supporting Independent Authors in the Digital Age\",\n        excerpt: \"Discover how digital platforms are empowering independent authors and creating new opportunities for creative expression.\",\n        author: \"Lisa Rodriguez\",\n        publishedDate: \"2024-01-08\",\n        readTime: 7,\n        category: \"Creator Support\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"supporting-independent-authors\"\n    },\n    {\n        id: \"5\",\n        title: \"The Psychology of Reading: Why We Love Stories\",\n        excerpt: \"Delve into the science behind our love for stories and how digital reading can enhance our connection to literature.\",\n        author: \"Dr. Amanda Foster\",\n        publishedDate: \"2024-01-05\",\n        readTime: 9,\n        category: \"Psychology\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"psychology-of-reading\"\n    },\n    {\n        id: \"6\",\n        title: \"Building Your Personal Library: A Curator's Guide\",\n        excerpt: \"Learn how to build a meaningful digital library that reflects your interests and supports your personal growth journey.\",\n        author: \"Robert Kim\",\n        publishedDate: \"2024-01-03\",\n        readTime: 4,\n        category: \"Reading Tips\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"building-personal-library-guide\"\n    }\n];\nconst categories = [\n    \"All\",\n    \"Technology\",\n    \"Author Interview\",\n    \"Writing Tips\",\n    \"Creator Support\",\n    \"Psychology\",\n    \"Reading Tips\"\n];\nfunction BlogPage() {\n    _s();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlogPage.useEffect\": ()=>{\n            // Simulate API call\n            setTimeout({\n                \"BlogPage.useEffect\": ()=>{\n                    setPosts(mockPosts);\n                    setIsLoading(false);\n                }\n            }[\"BlogPage.useEffect\"], 1000);\n        }\n    }[\"BlogPage.useEffect\"], []);\n    const filteredPosts = posts.filter((post)=>{\n        const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) || post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) || post.author.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === \"All\" || post.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-8 md:py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight mb-4\",\n                        children: \"Astewai Blog\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-muted-foreground max-w-2xl mx-auto\",\n                        children: \"Discover insights about digital reading, author interviews, writing advice, and the future of literature\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                    type: \"search\",\n                                    placeholder: \"Search articles...\",\n                                    className: \"w-full pl-8 rounded-full bg-card border-none\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: selectedCategory === category ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"rounded-full\",\n                                children: category\n                            }, category, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5,\n                    6\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-48 bg-muted rounded-t-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-muted rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/blog/\".concat(post.slug),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"h-full transition-all duration-300 hover:shadow-xl group cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[2/1] relative overflow-hidden rounded-t-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: post.coverImage,\n                                                    alt: post.title,\n                                                    className: \"object-cover w-full h-full transition-transform duration-500 group-hover:scale-105\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"bg-white/90 text-gray-800\",\n                                                        children: post.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"line-clamp-2 group-hover:text-primary transition-colors\",\n                                                children: post.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground line-clamp-3 mb-4\",\n                                                    children: post.excerpt\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-xs text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: post.author\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 201,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: formatDate(post.publishedDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        post.readTime,\n                                                                        \" min read\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 pb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-primary text-sm font-medium group-hover:gap-2 transition-all\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Read More\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 transition-transform group-hover:translate-x-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this)\n                            }, post.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    filteredPosts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"No articles found matching your criteria.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPage, \"D+H3AfZ6WuIqCtj/0JlvkspIESM=\");\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blog/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/blog-data.ts":
/*!**************************!*\
  !*** ./lib/blog-data.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBlogPostBySlug: () => (/* binding */ getBlogPostBySlug),\n/* harmony export */   getRelatedPosts: () => (/* binding */ getRelatedPosts),\n/* harmony export */   mockBlogPosts: () => (/* binding */ mockBlogPosts)\n/* harmony export */ });\n// Blog post types and mock data for Astewai blog\n// Mock data for blog posts with full content\nconst mockBlogPosts = [\n    {\n        id: \"1\",\n        title: \"The Future of Digital Reading: Why Security Matters\",\n        excerpt: \"Explore how secure digital reading platforms are revolutionizing the way we consume books while protecting authors' intellectual property.\",\n        content: \"\\n      <p>In an era where digital transformation touches every aspect of our lives, the way we read and consume literature has undergone a remarkable evolution. Digital reading platforms have emerged as powerful tools that not only enhance the reading experience but also address critical concerns around intellectual property protection and content security.</p>\\n\\n      <h2>The Digital Reading Revolution</h2>\\n      \\n      <p>The shift from physical books to digital platforms represents more than just a change in medium—it's a fundamental transformation in how we interact with written content. Digital reading platforms offer unprecedented convenience, allowing readers to access vast libraries from anywhere in the world, adjust reading preferences for optimal comfort, and engage with content in ways that were previously impossible.</p>\\n\\n      <p>However, with this convenience comes responsibility. As more authors and publishers embrace digital distribution, the need for robust security measures has become paramount. The challenge lies in balancing accessibility with protection, ensuring that readers can enjoy seamless experiences while authors' rights are respected and preserved.</p>\\n\\n      <h2>Security Challenges in Digital Publishing</h2>\\n\\n      <p>Digital content faces unique vulnerabilities that physical books simply don't encounter. Piracy, unauthorized distribution, and content theft pose significant threats to authors' livelihoods and publishers' business models. Traditional DRM (Digital Rights Management) solutions, while effective to some degree, often create friction in the user experience, leading to frustration among legitimate readers.</p>\\n\\n      <p>Modern secure reading platforms are addressing these challenges through innovative approaches that prioritize both security and user experience. By implementing advanced encryption, secure authentication systems, and intelligent content protection mechanisms, these platforms create environments where authors can confidently share their work while readers enjoy uncompromised access.</p>\\n\\n      <h2>The Technology Behind Secure Reading</h2>\\n\\n      <p>Today's leading digital reading platforms employ sophisticated technologies to ensure content security without sacrificing usability. These include:</p>\\n\\n      <ul>\\n        <li><strong>End-to-end encryption:</strong> Protecting content during transmission and storage</li>\\n        <li><strong>Watermarking:</strong> Embedding invisible identifiers to track content distribution</li>\\n        <li><strong>Secure authentication:</strong> Ensuring only authorized users can access purchased content</li>\\n        <li><strong>Real-time monitoring:</strong> Detecting and preventing unauthorized sharing attempts</li>\\n      </ul>\\n\\n      <h2>Benefits for Authors and Publishers</h2>\\n\\n      <p>Secure digital reading platforms offer numerous advantages for content creators. Authors can reach global audiences instantly, receive detailed analytics about reader engagement, and maintain greater control over their intellectual property. Publishers benefit from reduced distribution costs, enhanced market reach, and improved ability to experiment with pricing and promotional strategies.</p>\\n\\n      <p>Moreover, these platforms enable new revenue models, such as subscription services and micro-transactions, that can provide more sustainable income streams for authors while offering readers greater flexibility in how they access content.</p>\\n\\n      <h2>The Reader Experience</h2>\\n\\n      <p>From a reader's perspective, secure digital platforms offer unparalleled convenience and customization. Features like adjustable fonts, background colors, and reading speeds cater to individual preferences and accessibility needs. Social features allow readers to share insights, participate in discussions, and connect with authors and fellow readers.</p>\\n\\n      <p>The integration of multimedia elements, interactive content, and real-time updates creates immersive reading experiences that extend beyond traditional text-based consumption. Readers can access supplementary materials, author interviews, and related content that enriches their understanding and engagement with the material.</p>\\n\\n      <h2>Looking Ahead</h2>\\n\\n      <p>As we look to the future, the evolution of secure digital reading platforms will likely incorporate emerging technologies such as artificial intelligence, blockchain, and advanced analytics. These innovations promise to further enhance security measures while creating even more personalized and engaging reading experiences.</p>\\n\\n      <p>The success of digital reading platforms ultimately depends on their ability to serve all stakeholders—authors, publishers, and readers—while maintaining the highest standards of security and user experience. As the industry continues to mature, we can expect to see increasingly sophisticated solutions that make digital reading not just secure, but truly transformative.</p>\\n\\n      <p>The future of reading is digital, and with the right security measures in place, it's a future that benefits everyone in the literary ecosystem.</p>\\n    \",\n        author: \"Sarah Johnson\",\n        publishedDate: \"2024-01-15\",\n        readTime: 5,\n        category: \"Technology\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"future-digital-reading-security\",\n        tags: [\n            \"Digital Reading\",\n            \"Security\",\n            \"Technology\",\n            \"Publishing\"\n        ]\n    },\n    {\n        id: \"2\",\n        title: \"Author Interview: Building a Sustainable Writing Career\",\n        excerpt: \"We sit down with bestselling author Michael Chen to discuss the challenges and opportunities in today's publishing landscape.\",\n        content: \"\\n      <p>In this exclusive interview, we speak with Michael Chen, bestselling author of \\\"The Digital Entrepreneur\\\" and \\\"Innovation Mindset,\\\" about his journey from aspiring writer to successful author and the strategies he's used to build a sustainable writing career in today's rapidly evolving publishing landscape.</p>\\n\\n      <h2>The Journey Begins</h2>\\n\\n      <p><strong>Astewai:</strong> Michael, thank you for joining us today. Can you tell us about your journey into writing?</p>\\n\\n      <p><strong>Michael Chen:</strong> Thank you for having me. My journey into writing wasn't traditional. I started as a software engineer, but I always had stories and ideas I wanted to share. The turning point came when I realized that my technical background gave me unique insights into the digital transformation happening across industries. I decided to write about it, and that became my first book.</p>\\n\\n      <h2>Finding Your Voice</h2>\\n\\n      <p><strong>Astewai:</strong> How did you develop your unique voice as an author?</p>\\n\\n      <p><strong>Michael:</strong> Finding your voice is really about finding the intersection between what you're passionate about and what you can offer that's different. For me, it was combining technical expertise with storytelling. I realized that complex concepts could be made accessible through narrative, and that became my signature approach.</p>\\n\\n      <p>The key is authenticity. Don't try to write like someone else—write like yourself, but the best version of yourself. Your unique perspective and experiences are your greatest assets as a writer.</p>\\n\\n      <h2>The Business of Writing</h2>\\n\\n      <p><strong>Astewai:</strong> What advice do you have for authors trying to build a sustainable career?</p>\\n\\n      <p><strong>Michael:</strong> Sustainability in writing requires thinking beyond just the books. You need to build a platform, engage with your audience, and diversify your income streams. This might include speaking engagements, consulting, online courses, or subscription content.</p>\\n\\n      <p>Also, understand your metrics. Know your audience, track your sales, and understand what resonates with readers. The publishing industry has become increasingly data-driven, and authors who embrace this have a significant advantage.</p>\\n\\n      <h2>Embracing Digital Platforms</h2>\\n\\n      <p><strong>Astewai:</strong> How has the rise of digital reading platforms changed the game for authors?</p>\\n\\n      <p><strong>Michael:</strong> Digital platforms have democratized publishing in incredible ways. Authors can now reach global audiences without traditional gatekeepers, experiment with different formats and pricing models, and get real-time feedback from readers.</p>\\n\\n      <p>The direct relationship with readers is particularly powerful. Through digital platforms, I can see which chapters resonate most, where readers tend to stop, and what topics generate the most discussion. This data helps me write better books and serve my audience more effectively.</p>\\n\\n      <h2>Overcoming Challenges</h2>\\n\\n      <p><strong>Astewai:</strong> What have been your biggest challenges as an author?</p>\\n\\n      <p><strong>Michael:</strong> The biggest challenge is probably the constant need to market yourself while also finding time to write. It's easy to get caught up in promotion and social media and lose sight of the craft itself.</p>\\n\\n      <p>Another challenge is dealing with the uncertainty. Writing income can be unpredictable, and it takes time to build a sustainable career. You need to be prepared for the long game and have strategies to manage the financial ups and downs.</p>\\n\\n      <h2>Advice for Aspiring Authors</h2>\\n\\n      <p><strong>Astewai:</strong> What would you tell someone just starting their writing journey?</p>\\n\\n      <p><strong>Michael:</strong> First, write consistently. Even if it's just 15 minutes a day, consistency beats intensity. Second, read voraciously in your genre and beyond—you can't be a good writer without being a good reader.</p>\\n\\n      <p>Third, don't wait for permission. Start publishing, even if it's just blog posts or social media content. Build your audience gradually and learn from their feedback. The barrier to entry has never been lower, so take advantage of that.</p>\\n\\n      <p>Finally, be patient with yourself. Building a writing career takes time, and every author's journey is different. Focus on improving your craft and serving your readers, and the rest will follow.</p>\\n\\n      <h2>The Future of Publishing</h2>\\n\\n      <p><strong>Astewai:</strong> Where do you see the publishing industry heading?</p>\\n\\n      <p><strong>Michael:</strong> I think we'll see continued growth in digital platforms, more interactive and multimedia content, and increased personalization. AI will play a bigger role in helping authors with research, editing, and even marketing.</p>\\n\\n      <p>But at the end of the day, good storytelling and valuable content will always be at the heart of successful publishing. Technology changes, but the human need for stories and knowledge remains constant.</p>\\n\\n      <p><strong>Astewai:</strong> Thank you, Michael, for sharing your insights with our readers.</p>\\n\\n      <p><strong>Michael:</strong> My pleasure. Keep writing, keep learning, and remember that every published author was once where you are now.</p>\\n    \",\n        author: \"Emma Davis\",\n        publishedDate: \"2024-01-12\",\n        readTime: 8,\n        category: \"Author Interview\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"author-interview-michael-chen\",\n        tags: [\n            \"Author Interview\",\n            \"Writing Career\",\n            \"Publishing\",\n            \"Digital Platforms\"\n        ]\n    }\n];\n// Helper function to get blog post by slug\nfunction getBlogPostBySlug(slug) {\n    return mockBlogPosts.find((post)=>post.slug === slug);\n}\n// Helper function to get related posts\nfunction getRelatedPosts(currentSlug) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    const currentPost = getBlogPostBySlug(currentSlug);\n    if (!currentPost) return [];\n    return mockBlogPosts.filter((post)=>post.slug !== currentSlug && post.category === currentPost.category).slice(0, limit);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/blog-data.ts\n"));

/***/ })

});