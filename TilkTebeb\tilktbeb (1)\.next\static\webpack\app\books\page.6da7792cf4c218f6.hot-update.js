"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/page",{

/***/ "(app-pages-browser)/./components/book-card.tsx":
/*!**********************************!*\
  !*** ./components/book-card.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookCard: () => (/* binding */ BookCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ BookCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction BookCard(param) {\n    let { id, title, author, coverUrl, category, rating, price = 9.99, isPurchased = false } = param;\n    _s();\n    const [showPurchaseModal, setShowPurchaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const book = {\n        id,\n        title,\n        author,\n        coverUrl,\n        category,\n        rating,\n        price,\n        isPurchased,\n        pages: 300,\n        language: \"English\",\n        summary: \"<p>Discover the insights and strategies that make this book a must-read in the \".concat(category, \" category.</p>\")\n    };\n    const handlePurchaseClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setShowPurchaseModal(true);\n    };\n    const handlePurchaseComplete = ()=>{\n        // Handle successful purchase\n        console.log(\"Successfully purchased \".concat(title));\n    // You could update the book state here or refresh the page\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"book-card overflow-hidden h-full transition-all duration-300 hover:shadow-xl group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/books/\".concat(id),\n                className: \"block\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-[3/4] relative overflow-hidden rounded-t-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: coverUrl || \"/placeholder.svg\",\n                                alt: \"\".concat(title, \" book cover\"),\n                                className: \"object-cover w-full h-full transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 glass dark:glass-dark text-secondary text-xs px-2 py-1 rounded-full\",\n                                children: category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            isPurchased && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: \"Owned\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4 glass dark:glass-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-sm\",\n                                children: author\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 fill-secondary text-secondary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: rating.toFixed(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-primary\",\n                                        children: [\n                                            \"$\",\n                                            price.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                className: \"p-4 pt-0 glass dark:glass-dark\",\n                children: isPurchased ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full\",\n                    variant: \"outline\",\n                    children: \"Read Now\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full gap-2\",\n                    onClick: handlePurchaseClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        \"Buy Now\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(BookCard, \"GMDkQOp6xhk7B/PDWUW4Vk/PMws=\");\n_c = BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/book-card.tsx\n"));

/***/ })

});