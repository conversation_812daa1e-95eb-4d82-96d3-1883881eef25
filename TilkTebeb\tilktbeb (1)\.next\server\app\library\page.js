(()=>{var e={};e.id=382,e.ids=[382],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},81887:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(70260),a=s(28203),i=s(25155),l=s.n(i),o=s(67292),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(r,n);let d=["",{children:["library",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94700)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\library\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\library\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/library/page",pathname:"/library",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29567:(e,r,s)=>{Promise.resolve().then(s.bind(s,94700))},89815:(e,r,s)=>{Promise.resolve().then(s.bind(s,96615))},96615:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(45512),a=s(58009),i=s(28531),l=s.n(i),o=s(87021),n=s(97643),d=s(25409),c=s(77252),u=s(16873);let p=(0,s(41680).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var g=s(10617),m=s(4269);let h=[{id:"atomic-habits",title:"Atomic Habits",author:"James Clear",coverUrl:"/placeholder.svg?height=240&width=180",category:"Productivity",rating:4.9,pages:320,language:"English",summary:"An Easy & Proven Way to Build Good Habits & Break Bad Ones",price:13.99,isPurchased:!0,purchaseDate:"2024-01-15",lastRead:"2024-01-20",readingProgress:65},{id:"zero-to-one",title:"Zero to One",author:"Peter Thiel",coverUrl:"/placeholder.svg?height=240&width=180",category:"Entrepreneurship",rating:4.8,pages:224,language:"English",summary:"Notes on Startups, or How to Build the Future",price:12.99,isPurchased:!0,purchaseDate:"2024-01-10",lastRead:"2024-01-18",readingProgress:100},{id:"good-to-great",title:"Good to Great",author:"Jim Collins",coverUrl:"/placeholder.svg?height=240&width=180",category:"Leadership",rating:4.7,pages:300,language:"English",summary:"Why Some Companies Make the Leap... and Others Don't",price:11.99,isPurchased:!0,purchaseDate:"2024-01-05",readingProgress:25},{id:"lean-startup",title:"The Lean Startup",author:"Eric Ries",coverUrl:"/placeholder.svg?height=240&width=180",category:"Startup",rating:4.6,pages:336,language:"English",summary:"How Today's Entrepreneurs Use Continuous Innovation to Create Radically Successful Businesses",price:10.99,isPurchased:!0,purchaseDate:"2024-01-01",readingProgress:0}];function x(){let[e,r]=(0,a.useState)(h),[s,i]=(0,a.useState)(""),[x,f]=(0,a.useState)("recent"),[b,v]=(0,a.useState)("all"),[j,y]=(0,a.useState)(!0),N=e.filter(e=>{let r=e.title.toLowerCase().includes(s.toLowerCase())||e.author.toLowerCase().includes(s.toLowerCase()),t=(()=>{switch(b){case"reading":return e.readingProgress>0&&e.readingProgress<100;case"completed":return 100===e.readingProgress;case"unread":return 0===e.readingProgress;default:return!0}})();return r&&t}).sort((e,r)=>{switch(x){case"title":return e.title.localeCompare(r.title);case"author":return e.author.localeCompare(r.author);case"progress":return r.readingProgress-e.readingProgress;default:return new Date(r.purchaseDate).getTime()-new Date(e.purchaseDate).getTime()}}),w=e=>0===e?"bg-gray-200":100===e?"bg-green-500":"bg-primary",P=e=>0===e?(0,t.jsx)(c.E,{variant:"outline",children:"Unread"}):100===e?(0,t.jsx)(c.E,{className:"bg-green-500",children:"Completed"}):(0,t.jsx)(c.E,{variant:"secondary",children:"Reading"});return(0,t.jsxs)("div",{className:"container py-8 md:py-12",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold tracking-tight mb-2",children:"My Library"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Your purchased books and reading progress"})]}),(0,t.jsx)("div",{className:"w-full md:w-auto flex flex-col sm:flex-row gap-4",children:(0,t.jsxs)("div",{className:"relative w-full sm:w-64",children:[(0,t.jsx)(u.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(d.p,{type:"search",placeholder:"Search your library...",className:"w-full pl-8 rounded-full bg-card border-none",value:s,onChange:e=>i(e.target.value)})]})})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filter:"}),["all","reading","completed","unread"].map(e=>(0,t.jsx)(o.$,{variant:b===e?"default":"outline",size:"sm",onClick:()=>v(e),children:e.charAt(0).toUpperCase()+e.slice(1)},e))]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Sort:"}),(0,t.jsxs)("select",{value:x,onChange:e=>f(e.target.value),className:"text-sm border rounded px-2 py-1 bg-background",children:[(0,t.jsx)("option",{value:"recent",children:"Recently Added"}),(0,t.jsx)("option",{value:"title",children:"Title"}),(0,t.jsx)("option",{value:"author",children:"Author"}),(0,t.jsx)("option",{value:"progress",children:"Progress"})]})]})]}),j?(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[1,2,3,4].map(e=>(0,t.jsxs)(n.Zp,{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-48 bg-muted rounded-t-lg"}),(0,t.jsxs)(n.Wu,{className:"p-4",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-muted rounded mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-muted rounded"})]})]},e))}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:N.map(e=>(0,t.jsxs)(n.Zp,{className:"overflow-hidden h-full transition-all duration-300 hover:shadow-xl group",children:[(0,t.jsxs)("div",{className:"aspect-[3/4] relative overflow-hidden",children:[(0,t.jsx)("img",{src:e.coverUrl,alt:`${e.title} cover`,className:"object-cover w-full h-full transition-transform duration-500 group-hover:scale-105"}),(0,t.jsx)("div",{className:"absolute top-2 right-2",children:P(e.readingProgress)})]}),(0,t.jsxs)(n.Wu,{className:"p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg line-clamp-1 mb-1",children:e.title}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm mb-3",children:e.author}),(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[e.readingProgress,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${w(e.readingProgress)}`,style:{width:`${e.readingProgress}%`}})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(g.A,{className:"h-3 w-3 fill-yellow-400 text-yellow-400"}),(0,t.jsx)("span",{children:e.rating})]}),(0,t.jsxs)("span",{children:[e.pages," pages"]})]}),(0,t.jsx)(l(),{href:`/reader/${e.id}`,children:(0,t.jsxs)(o.$,{className:"w-full gap-2",children:[(0,t.jsx)(m.A,{className:"h-4 w-4"}),0===e.readingProgress?"Start Reading":100===e.readingProgress?"Read Again":"Continue Reading"]})})]})]},e.id))}),0===N.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(m.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:s?"No books found matching your search.":"Your library is empty. Start by purchasing some books!"}),!s&&(0,t.jsx)(l(),{href:"/books",className:"mt-4 inline-block",children:(0,t.jsx)(o.$,{children:"Browse Books"})})]})]})]})}},77252:(e,r,s)=>{"use strict";s.d(r,{E:()=>o});var t=s(45512);s(58009);var a=s(21643),i=s(59462);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...s}){return(0,t.jsx)("div",{className:(0,i.cn)(l({variant:r}),e),...s})}},97643:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>o,wL:()=>u});var t=s(45512),a=s(58009),i=s(59462);let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));n.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},10617:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(41680).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},94700:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\library\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\library\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[884,999],()=>s(81887));module.exports=t})();