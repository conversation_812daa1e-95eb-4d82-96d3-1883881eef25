"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/page",{

/***/ "(app-pages-browser)/./lib/blog-data.ts":
/*!**************************!*\
  !*** ./lib/blog-data.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBlogPostBySlug: () => (/* binding */ getBlogPostBySlug),\n/* harmony export */   getRelatedPosts: () => (/* binding */ getRelatedPosts),\n/* harmony export */   mockBlogPosts: () => (/* binding */ mockBlogPosts)\n/* harmony export */ });\n// Blog post types and mock data for Astewai blog\n// Mock data for blog posts with full content\nconst mockBlogPosts = [\n    {\n        id: \"1\",\n        title: \"The Future of Digital Reading: Why Security Matters\",\n        excerpt: \"Explore how secure digital reading platforms are revolutionizing the way we consume books while protecting authors' intellectual property.\",\n        content: \"\\n      <p>In an era where digital transformation touches every aspect of our lives, the way we read and consume literature has undergone a remarkable evolution. Digital reading platforms have emerged as powerful tools that not only enhance the reading experience but also address critical concerns around intellectual property protection and content security.</p>\\n\\n      <h2>The Digital Reading Revolution</h2>\\n      \\n      <p>The shift from physical books to digital platforms represents more than just a change in medium—it's a fundamental transformation in how we interact with written content. Digital reading platforms offer unprecedented convenience, allowing readers to access vast libraries from anywhere in the world, adjust reading preferences for optimal comfort, and engage with content in ways that were previously impossible.</p>\\n\\n      <p>However, with this convenience comes responsibility. As more authors and publishers embrace digital distribution, the need for robust security measures has become paramount. The challenge lies in balancing accessibility with protection, ensuring that readers can enjoy seamless experiences while authors' rights are respected and preserved.</p>\\n\\n      <h2>Security Challenges in Digital Publishing</h2>\\n\\n      <p>Digital content faces unique vulnerabilities that physical books simply don't encounter. Piracy, unauthorized distribution, and content theft pose significant threats to authors' livelihoods and publishers' business models. Traditional DRM (Digital Rights Management) solutions, while effective to some degree, often create friction in the user experience, leading to frustration among legitimate readers.</p>\\n\\n      <p>Modern secure reading platforms are addressing these challenges through innovative approaches that prioritize both security and user experience. By implementing advanced encryption, secure authentication systems, and intelligent content protection mechanisms, these platforms create environments where authors can confidently share their work while readers enjoy uncompromised access.</p>\\n\\n      <h2>The Technology Behind Secure Reading</h2>\\n\\n      <p>Today's leading digital reading platforms employ sophisticated technologies to ensure content security without sacrificing usability. These include:</p>\\n\\n      <ul>\\n        <li><strong>End-to-end encryption:</strong> Protecting content during transmission and storage</li>\\n        <li><strong>Watermarking:</strong> Embedding invisible identifiers to track content distribution</li>\\n        <li><strong>Secure authentication:</strong> Ensuring only authorized users can access purchased content</li>\\n        <li><strong>Real-time monitoring:</strong> Detecting and preventing unauthorized sharing attempts</li>\\n      </ul>\\n\\n      <h2>Benefits for Authors and Publishers</h2>\\n\\n      <p>Secure digital reading platforms offer numerous advantages for content creators. Authors can reach global audiences instantly, receive detailed analytics about reader engagement, and maintain greater control over their intellectual property. Publishers benefit from reduced distribution costs, enhanced market reach, and improved ability to experiment with pricing and promotional strategies.</p>\\n\\n      <p>Moreover, these platforms enable new revenue models, such as subscription services and micro-transactions, that can provide more sustainable income streams for authors while offering readers greater flexibility in how they access content.</p>\\n\\n      <h2>The Reader Experience</h2>\\n\\n      <p>From a reader's perspective, secure digital platforms offer unparalleled convenience and customization. Features like adjustable fonts, background colors, and reading speeds cater to individual preferences and accessibility needs. Social features allow readers to share insights, participate in discussions, and connect with authors and fellow readers.</p>\\n\\n      <p>The integration of multimedia elements, interactive content, and real-time updates creates immersive reading experiences that extend beyond traditional text-based consumption. Readers can access supplementary materials, author interviews, and related content that enriches their understanding and engagement with the material.</p>\\n\\n      <h2>Looking Ahead</h2>\\n\\n      <p>As we look to the future, the evolution of secure digital reading platforms will likely incorporate emerging technologies such as artificial intelligence, blockchain, and advanced analytics. These innovations promise to further enhance security measures while creating even more personalized and engaging reading experiences.</p>\\n\\n      <p>The success of digital reading platforms ultimately depends on their ability to serve all stakeholders—authors, publishers, and readers—while maintaining the highest standards of security and user experience. As the industry continues to mature, we can expect to see increasingly sophisticated solutions that make digital reading not just secure, but truly transformative.</p>\\n\\n      <p>The future of reading is digital, and with the right security measures in place, it's a future that benefits everyone in the literary ecosystem.</p>\\n    \",\n        author: \"Sarah Johnson\",\n        publishedDate: \"2024-01-15\",\n        readTime: 5,\n        category: \"Technology\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"future-digital-reading-security\",\n        tags: [\n            \"Digital Reading\",\n            \"Security\",\n            \"Technology\",\n            \"Publishing\"\n        ]\n    },\n    {\n        id: \"2\",\n        title: \"Author Interview: Building a Sustainable Writing Career\",\n        excerpt: \"We sit down with bestselling author Michael Chen to discuss the challenges and opportunities in today's publishing landscape.\",\n        content: \"\\n      <p>In this exclusive interview, we speak with Michael Chen, bestselling author of \\\"The Digital Entrepreneur\\\" and \\\"Innovation Mindset,\\\" about his journey from aspiring writer to successful author and the strategies he's used to build a sustainable writing career in today's rapidly evolving publishing landscape.</p>\\n\\n      <h2>The Journey Begins</h2>\\n\\n      <p><strong>Astewai:</strong> Michael, thank you for joining us today. Can you tell us about your journey into writing?</p>\\n\\n      <p><strong>Michael Chen:</strong> Thank you for having me. My journey into writing wasn't traditional. I started as a software engineer, but I always had stories and ideas I wanted to share. The turning point came when I realized that my technical background gave me unique insights into the digital transformation happening across industries. I decided to write about it, and that became my first book.</p>\\n\\n      <h2>Finding Your Voice</h2>\\n\\n      <p><strong>Astewai:</strong> How did you develop your unique voice as an author?</p>\\n\\n      <p><strong>Michael:</strong> Finding your voice is really about finding the intersection between what you're passionate about and what you can offer that's different. For me, it was combining technical expertise with storytelling. I realized that complex concepts could be made accessible through narrative, and that became my signature approach.</p>\\n\\n      <p>The key is authenticity. Don't try to write like someone else—write like yourself, but the best version of yourself. Your unique perspective and experiences are your greatest assets as a writer.</p>\\n\\n      <h2>The Business of Writing</h2>\\n\\n      <p><strong>Astewai:</strong> What advice do you have for authors trying to build a sustainable career?</p>\\n\\n      <p><strong>Michael:</strong> Sustainability in writing requires thinking beyond just the books. You need to build a platform, engage with your audience, and diversify your income streams. This might include speaking engagements, consulting, online courses, or subscription content.</p>\\n\\n      <p>Also, understand your metrics. Know your audience, track your sales, and understand what resonates with readers. The publishing industry has become increasingly data-driven, and authors who embrace this have a significant advantage.</p>\\n\\n      <h2>Embracing Digital Platforms</h2>\\n\\n      <p><strong>Astewai:</strong> How has the rise of digital reading platforms changed the game for authors?</p>\\n\\n      <p><strong>Michael:</strong> Digital platforms have democratized publishing in incredible ways. Authors can now reach global audiences without traditional gatekeepers, experiment with different formats and pricing models, and get real-time feedback from readers.</p>\\n\\n      <p>The direct relationship with readers is particularly powerful. Through digital platforms, I can see which chapters resonate most, where readers tend to stop, and what topics generate the most discussion. This data helps me write better books and serve my audience more effectively.</p>\\n\\n      <h2>Overcoming Challenges</h2>\\n\\n      <p><strong>Astewai:</strong> What have been your biggest challenges as an author?</p>\\n\\n      <p><strong>Michael:</strong> The biggest challenge is probably the constant need to market yourself while also finding time to write. It's easy to get caught up in promotion and social media and lose sight of the craft itself.</p>\\n\\n      <p>Another challenge is dealing with the uncertainty. Writing income can be unpredictable, and it takes time to build a sustainable career. You need to be prepared for the long game and have strategies to manage the financial ups and downs.</p>\\n\\n      <h2>Advice for Aspiring Authors</h2>\\n\\n      <p><strong>Astewai:</strong> What would you tell someone just starting their writing journey?</p>\\n\\n      <p><strong>Michael:</strong> First, write consistently. Even if it's just 15 minutes a day, consistency beats intensity. Second, read voraciously in your genre and beyond—you can't be a good writer without being a good reader.</p>\\n\\n      <p>Third, don't wait for permission. Start publishing, even if it's just blog posts or social media content. Build your audience gradually and learn from their feedback. The barrier to entry has never been lower, so take advantage of that.</p>\\n\\n      <p>Finally, be patient with yourself. Building a writing career takes time, and every author's journey is different. Focus on improving your craft and serving your readers, and the rest will follow.</p>\\n\\n      <h2>The Future of Publishing</h2>\\n\\n      <p><strong>Astewai:</strong> Where do you see the publishing industry heading?</p>\\n\\n      <p><strong>Michael:</strong> I think we'll see continued growth in digital platforms, more interactive and multimedia content, and increased personalization. AI will play a bigger role in helping authors with research, editing, and even marketing.</p>\\n\\n      <p>But at the end of the day, good storytelling and valuable content will always be at the heart of successful publishing. Technology changes, but the human need for stories and knowledge remains constant.</p>\\n\\n      <p><strong>Astewai:</strong> Thank you, Michael, for sharing your insights with our readers.</p>\\n\\n      <p><strong>Michael:</strong> My pleasure. Keep writing, keep learning, and remember that every published author was once where you are now.</p>\\n    \",\n        author: \"Emma Davis\",\n        publishedDate: \"2024-01-12\",\n        readTime: 8,\n        category: \"Author Interview\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"author-interview-michael-chen\",\n        tags: [\n            \"Author Interview\",\n            \"Writing Career\",\n            \"Publishing\",\n            \"Digital Platforms\"\n        ]\n    }\n];\n// Helper function to get blog post by slug\nfunction getBlogPostBySlug(slug) {\n    return mockBlogPosts.find((post)=>post.slug === slug);\n}\n// Helper function to get related posts\nfunction getRelatedPosts(currentSlug) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    const currentPost = getBlogPostBySlug(currentSlug);\n    if (!currentPost) return [];\n    return mockBlogPosts.filter((post)=>post.slug !== currentSlug && post.category === currentPost.category).slice(0, limit);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/blog-data.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/mock-api-service.ts":
/*!*********************************!*\
  !*** ./lib/mock-api-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockApiService: () => (/* binding */ MockApiService)\n/* harmony export */ });\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./lib/mock-data.ts\");\n/* harmony import */ var _blog_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blog-data */ \"(app-pages-browser)/./lib/blog-data.ts\");\n\n\n// Mock API service for development when Django backend is not available\nclass MockApiService {\n    // Simulate network delay\n    static delay() {\n        let ms = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 500;\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    // Books API methods\n    static async getBooks(params) {\n        await this.delay();\n        let filteredBooks = [\n            ..._mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"all\") {\n            filteredBooks = filteredBooks.filter((book)=>book.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredBooks = filteredBooks.filter((book)=>book.title.toLowerCase().includes(searchQuery) || book.author.toLowerCase().includes(searchQuery));\n        }\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) {\n            const fullBooks = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.filter((book)=>book.isPremium === params.premium);\n            const premiumBookIds = fullBooks.map((book)=>book.id);\n            filteredBooks = filteredBooks.filter((book)=>premiumBookIds.includes(book.id));\n        }\n        if (params === null || params === void 0 ? void 0 : params.limit) {\n            filteredBooks = filteredBooks.slice(0, params.limit);\n        }\n        return filteredBooks;\n    }\n    static async getBookById(id) {\n        await this.delay();\n        const book = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.find((book)=>book.id === id);\n        if (!book) {\n            throw new Error(\"Book with id \".concat(id, \" not found\"));\n        }\n        return book;\n    }\n    // Free Books API methods\n    static async getFreeBooks(params) {\n        await this.delay();\n        let freeBooks = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews.filter((book)=>{\n            const fullBook = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.find((b)=>b.id === book.id);\n            return (fullBook === null || fullBook === void 0 ? void 0 : fullBook.isFree) || (fullBook === null || fullBook === void 0 ? void 0 : fullBook.price) === 0;\n        });\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"All\") {\n            freeBooks = freeBooks.filter((book)=>book.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            freeBooks = freeBooks.filter((book)=>book.title.toLowerCase().includes(searchQuery) || book.author.toLowerCase().includes(searchQuery) || book.category.toLowerCase().includes(searchQuery));\n        }\n        if (params === null || params === void 0 ? void 0 : params.sortBy) {\n            freeBooks.sort((a, b)=>{\n                switch(params.sortBy){\n                    case \"rating\":\n                        return b.rating - a.rating;\n                    case \"author\":\n                        return a.author.localeCompare(b.author);\n                    case \"title\":\n                    default:\n                        return a.title.localeCompare(b.title);\n                }\n            });\n        }\n        if (params === null || params === void 0 ? void 0 : params.limit) {\n            freeBooks = freeBooks.slice(0, params.limit);\n        }\n        return freeBooks;\n    }\n    // Blog API methods\n    static async getBlogPosts(params) {\n        await this.delay();\n        let filteredPosts = [\n            ..._blog_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"All\") {\n            filteredPosts = filteredPosts.filter((post)=>post.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredPosts = filteredPosts.filter((post)=>post.title.toLowerCase().includes(searchQuery) || post.excerpt.toLowerCase().includes(searchQuery) || post.author.toLowerCase().includes(searchQuery));\n        }\n        if (params === null || params === void 0 ? void 0 : params.limit) {\n            filteredPosts = filteredPosts.slice(0, params.limit);\n        }\n        return filteredPosts;\n    }\n    static async getBlogPostBySlug(slug) {\n        await this.delay();\n        const post = _blog_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts.find((post)=>post.slug === slug);\n        if (!post) {\n            throw new Error(\"Blog post with slug \".concat(slug, \" not found\"));\n        }\n        return post;\n    }\n    static async getRelatedBlogPosts(currentSlug) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n        await this.delay();\n        const currentPost = _blog_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts.find((post)=>post.slug === currentSlug);\n        if (!currentPost) return [];\n        return _blog_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts.filter((post)=>post.slug !== currentSlug && post.category === currentPost.category).slice(0, limit);\n    }\n    // Business Plans API methods\n    static async getBusinessPlans(params) {\n        await this.delay();\n        let filteredPlans = [\n            ..._mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlanPreviews\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.size) && params.size !== \"all\") {\n            filteredPlans = filteredPlans.filter((plan)=>plan.size === params.size);\n        }\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"all\") {\n            filteredPlans = filteredPlans.filter((plan)=>plan.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredPlans = filteredPlans.filter((plan)=>plan.title.toLowerCase().includes(searchQuery) || plan.category.toLowerCase().includes(searchQuery));\n        }\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) {\n            filteredPlans = filteredPlans.filter((plan)=>plan.isPremium === params.premium);\n        }\n        return filteredPlans;\n    }\n    static async getBusinessPlanById(id) {\n        await this.delay();\n        const plan = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlans.find((plan)=>plan.id === id);\n        if (!plan) {\n            throw new Error(\"Business plan with id \".concat(id, \" not found\"));\n        }\n        return plan;\n    }\n    // Authentication API methods\n    static async login(credentials) {\n        await this.delay();\n        // Mock successful login for demo purposes\n        return {\n            id: \"user-1\",\n            firstName: \"John\",\n            lastName: \"Doe\",\n            email: credentials.email,\n            plan: \"medium\",\n            token: \"mock-jwt-token-123\"\n        };\n    }\n    static async register(userData) {\n        await this.delay();\n        // Mock successful registration\n        return {\n            id: \"user-\".concat(Date.now()),\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            email: userData.email,\n            plan: \"base\",\n            token: \"mock-jwt-token-456\"\n        };\n    }\n    // User API methods\n    static async getUserProfile(userId) {\n        await this.delay();\n        return {\n            user: {\n                id: userId,\n                firstName: \"John\",\n                lastName: \"Doe\",\n                email: \"<EMAIL>\",\n                plan: \"medium\"\n            },\n            activities: [\n                {\n                    id: \"activity-1\",\n                    userId: userId,\n                    type: \"book_read\",\n                    itemId: \"the-psychology-of-money\",\n                    itemTitle: \"The Psychology of Money\",\n                    timestamp: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: \"activity-2\",\n                    userId: userId,\n                    type: \"plan_viewed\",\n                    itemId: \"small-1\",\n                    itemTitle: \"Local Coffee Shop\",\n                    timestamp: new Date(Date.now() - 172800000).toISOString()\n                }\n            ]\n        };\n    }\n    static async updateUserProfile(userId, userData) {\n        await this.delay();\n        return {\n            id: userId,\n            firstName: userData.firstName || \"John\",\n            lastName: userData.lastName || \"Doe\",\n            email: userData.email || \"<EMAIL>\",\n            plan: \"medium\"\n        };\n    }\n    // Payment API methods\n    static async processPayment(paymentData) {\n        await this.delay(1000) // Longer delay for payment processing\n        ;\n        // Mock successful payment\n        return {\n            success: true,\n            transactionId: \"TRX-\".concat(Date.now(), \"-\").concat(Math.floor(Math.random() * 1000)),\n            message: \"Payment processed successfully\",\n            timestamp: new Date().toISOString(),\n            plan: paymentData.plan\n        };\n    }\n    static async verifyPayment(transactionId) {\n        await this.delay();\n        return {\n            success: true,\n            status: \"completed\",\n            message: \"Payment verified successfully\"\n        };\n    }\n    static async getPaymentHistory(userId) {\n        await this.delay();\n        return [\n            {\n                success: true,\n                transactionId: \"TRX-123456789\",\n                message: \"Payment processed successfully\",\n                timestamp: new Date(Date.now() - 2592000000).toISOString(),\n                plan: \"medium\"\n            }\n        ];\n    }\n    // Admin API methods\n    static async getAdminStats() {\n        await this.delay();\n        return {\n            totalUsers: 1250,\n            totalBooks: _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.length,\n            totalBusinessPlans: _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlans.length,\n            revenueThisMonth: 15750\n        };\n    }\n    static async getAdminUsers(params) {\n        await this.delay();\n        const mockUsers = [\n            {\n                id: \"user-1\",\n                firstName: \"John\",\n                lastName: \"Doe\",\n                email: \"<EMAIL>\",\n                plan: \"medium\",\n                createdAt: \"2023-01-15T00:00:00Z\"\n            },\n            {\n                id: \"user-2\",\n                firstName: \"Jane\",\n                lastName: \"Smith\",\n                email: \"<EMAIL>\",\n                plan: \"base\",\n                createdAt: \"2023-03-22T00:00:00Z\"\n            }\n        ];\n        return {\n            users: mockUsers,\n            total: mockUsers.length,\n            page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n            totalPages: 1\n        };\n    }\n    // Bookmarks API methods\n    static async getUserBookmarks(userId) {\n        await this.delay();\n        // Return first 2 books as bookmarked for demo\n        return _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews.slice(0, 2);\n    }\n    static async addBookmark(userId, bookId) {\n        await this.delay();\n        return {\n            success: true\n        };\n    }\n    static async removeBookmark(userId, bookId) {\n        await this.delay();\n        return {\n            success: true\n        };\n    }\n    // Analytics API methods\n    static async getUserAnalytics(userId) {\n        await this.delay();\n        return {\n            readingStats: {\n                booksRead: 12,\n                totalReadingTime: 2400,\n                averageReadingSpeed: 250,\n                streakDays: 7\n            },\n            recentActivity: [\n                {\n                    id: \"activity-1\",\n                    type: \"book_read\",\n                    itemTitle: \"The Psychology of Money\",\n                    timestamp: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: \"activity-2\",\n                    type: \"plan_viewed\",\n                    itemTitle: \"Local Coffee Shop\",\n                    timestamp: new Date(Date.now() - 172800000).toISOString()\n                }\n            ]\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-api-service.ts\n"));

/***/ })

});