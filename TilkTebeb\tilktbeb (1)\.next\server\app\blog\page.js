(()=>{var e={};e.id=831,e.ids=[831],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79177:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(70260),a=t(28203),i=t(25155),l=t.n(i),o=t(67292),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(r,n);let d=["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56523)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\blog\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\blog\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93316:(e,r,t)=>{Promise.resolve().then(t.bind(t,56523))},23172:(e,r,t)=>{Promise.resolve().then(t.bind(t,20620))},20620:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(45512),a=t(58009),i=t(28531),l=t.n(i),o=t(87021),n=t(97643),d=t(77252),c=t(25409),p=t(16873),u=t(87798),m=t(45723),x=t(4643);let h=(0,t(41680).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),g=["All","Technology","Author Interview","Writing Tips","Creator Support","Psychology","Reading Tips"];function f(){let[e,r]=(0,a.useState)([]),[t,i]=(0,a.useState)(""),[f,v]=(0,a.useState)("All"),[b,j]=(0,a.useState)(!0),N=e.filter(e=>{let r=e.title.toLowerCase().includes(t.toLowerCase())||e.excerpt.toLowerCase().includes(t.toLowerCase())||e.author.toLowerCase().includes(t.toLowerCase()),s="All"===f||e.category===f;return r&&s}),y=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,s.jsxs)("div",{className:"container py-8 md:py-12",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight mb-4",children:"Astewai Blog"}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Discover insights about digital reading, author interviews, writing advice, and the future of literature"})]}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8 mb-8",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(c.p,{type:"search",placeholder:"Search articles...",className:"w-full pl-8 rounded-full bg-card border-none",value:t,onChange:e=>i(e.target.value)})]})}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:g.map(e=>(0,s.jsx)(o.$,{variant:f===e?"default":"outline",size:"sm",onClick:()=>v(e),className:"rounded-full",children:e},e))})]}),b?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,s.jsxs)(n.Zp,{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-48 bg-muted rounded-t-lg"}),(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"h-4 bg-muted rounded mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-muted rounded mb-4"}),(0,s.jsx)("div",{className:"h-8 bg-muted rounded"})]})]},e))}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:N.map(e=>(0,s.jsx)(l(),{href:`/blog/${e.slug}`,children:(0,s.jsxs)(n.Zp,{className:"h-full transition-all duration-300 hover:shadow-xl group cursor-pointer",children:[(0,s.jsxs)("div",{className:"aspect-[2/1] relative overflow-hidden rounded-t-xl",children:[(0,s.jsx)("img",{src:e.coverImage,alt:e.title,className:"object-cover w-full h-full transition-transform duration-500 group-hover:scale-105"}),(0,s.jsx)("div",{className:"absolute top-2 right-2",children:(0,s.jsx)(d.E,{variant:"secondary",className:"bg-white/90 text-gray-800",children:e.category})})]}),(0,s.jsx)(n.aR,{className:"pb-4",children:(0,s.jsx)(n.ZB,{className:"line-clamp-2 group-hover:text-primary transition-colors",children:e.title})}),(0,s.jsxs)(n.Wu,{className:"pb-4",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-3 mb-4",children:e.excerpt}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(u.A,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:e.author})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:y(e.publishedDate)})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(x.A,{className:"h-3 w-3"}),(0,s.jsxs)("span",{children:[e.readTime," min read"]})]})]})]}),(0,s.jsx)("div",{className:"px-6 pb-6",children:(0,s.jsxs)("div",{className:"flex items-center text-primary text-sm font-medium group-hover:gap-2 transition-all",children:[(0,s.jsx)("span",{children:"Read More"}),(0,s.jsx)(h,{className:"h-4 w-4 transition-transform group-hover:translate-x-1"})]})})]})},e.id))}),0===N.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(p.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"No articles found matching your criteria."})]})]})]})}},77252:(e,r,t)=>{"use strict";t.d(r,{E:()=>o});var s=t(45512);t(58009);var a=t(21643),i=t(59462);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...t}){return(0,s.jsx)("div",{className:(0,i.cn)(l({variant:r}),e),...t})}},97643:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>o,wL:()=>p});var s=t(45512),a=t(58009),i=t(59462);let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));n.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let p=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));p.displayName="CardFooter"},45723:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(41680).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},4643:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(41680).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},56523:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\blog\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[884,999],()=>t(79177));module.exports=s})();