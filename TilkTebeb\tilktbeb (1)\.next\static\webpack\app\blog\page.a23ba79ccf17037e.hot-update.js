"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./app/blog/page.tsx":
/*!***************************!*\
  !*** ./app/blog/page.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Clock,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Additional mock posts for the listing page (these would come from API in production)\nconst additionalMockPosts = [\n    {\n        id: \"3\",\n        title: \"5 Essential Writing Tips from Industry Professionals\",\n        excerpt: \"Learn the secrets that professional writers use to craft compelling narratives and engage their readers from page one.\",\n        author: \"David Wilson\",\n        publishedDate: \"2024-01-10\",\n        readTime: 6,\n        category: \"Writing Tips\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"essential-writing-tips-professionals\"\n    },\n    {\n        id: \"4\",\n        title: \"Supporting Independent Authors in the Digital Age\",\n        excerpt: \"Discover how digital platforms are empowering independent authors and creating new opportunities for creative expression.\",\n        author: \"Lisa Rodriguez\",\n        publishedDate: \"2024-01-08\",\n        readTime: 7,\n        category: \"Creator Support\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"supporting-independent-authors\"\n    },\n    {\n        id: \"5\",\n        title: \"The Psychology of Reading: Why We Love Stories\",\n        excerpt: \"Delve into the science behind our love for stories and how digital reading can enhance our connection to literature.\",\n        author: \"Dr. Amanda Foster\",\n        publishedDate: \"2024-01-05\",\n        readTime: 9,\n        category: \"Psychology\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"psychology-of-reading\"\n    },\n    {\n        id: \"6\",\n        title: \"Building Your Personal Library: A Curator's Guide\",\n        excerpt: \"Learn how to build a meaningful digital library that reflects your interests and supports your personal growth journey.\",\n        author: \"Robert Kim\",\n        publishedDate: \"2024-01-03\",\n        readTime: 4,\n        category: \"Reading Tips\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"building-personal-library-guide\"\n    }\n];\nconst categories = [\n    \"All\",\n    \"Technology\",\n    \"Author Interview\",\n    \"Writing Tips\",\n    \"Creator Support\",\n    \"Psychology\",\n    \"Reading Tips\"\n];\nfunction BlogPage() {\n    _s();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlogPage.useEffect\": ()=>{\n            const fetchBlogPosts = {\n                \"BlogPage.useEffect.fetchBlogPosts\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const blogPosts = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.getBlogPosts({\n                            category: selectedCategory !== \"All\" ? selectedCategory : undefined,\n                            query: searchQuery || undefined\n                        });\n                        // Combine API posts with additional mock posts for demo\n                        setPosts([\n                            ...blogPosts,\n                            ...additionalMockPosts\n                        ]);\n                    } catch (err) {\n                        setError((0,_lib_api__WEBPACK_IMPORTED_MODULE_7__.handleApiError)(err));\n                        console.error(\"Error fetching blog posts:\", err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BlogPage.useEffect.fetchBlogPosts\"];\n            fetchBlogPosts();\n        }\n    }[\"BlogPage.useEffect\"], [\n        selectedCategory,\n        searchQuery\n    ]);\n    const filteredPosts = posts.filter((post)=>{\n        const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) || post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) || post.author.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === \"All\" || post.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-8 md:py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight mb-4\",\n                        children: \"Astewai Blog\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-muted-foreground max-w-2xl mx-auto\",\n                        children: \"Discover insights about digital reading, author interviews, writing advice, and the future of literature\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                    type: \"search\",\n                                    placeholder: \"Search articles...\",\n                                    className: \"w-full pl-8 rounded-full bg-card border-none\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: selectedCategory === category ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"rounded-full\",\n                                children: category\n                            }, category, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5,\n                    6\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-48 bg-muted rounded-t-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-muted rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/blog/\".concat(post.slug),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"h-full transition-all duration-300 hover:shadow-xl group cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[2/1] relative overflow-hidden rounded-t-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: post.coverImage,\n                                                    alt: post.title,\n                                                    className: \"object-cover w-full h-full transition-transform duration-500 group-hover:scale-105\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 right-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"bg-white/90 text-gray-800\",\n                                                        children: post.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"line-clamp-2 group-hover:text-primary transition-colors\",\n                                                children: post.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground line-clamp-3 mb-4\",\n                                                    children: post.excerpt\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-xs text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: post.author\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: formatDate(post.publishedDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 196,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        post.readTime,\n                                                                        \" min read\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 pb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-primary text-sm font-medium group-hover:gap-2 transition-all\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Read More\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 transition-transform group-hover:translate-x-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 17\n                                }, this)\n                            }, post.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    filteredPosts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Clock_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"No articles found matching your criteria.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\blog\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPage, \"YJLBZUonFCwsPqJ8YzA4ws15mg8=\");\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blog/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api-service.ts":
/*!****************************!*\
  !*** ./lib/api-service.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiService: () => (/* binding */ ApiService)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Configuration for Django API\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';\n// API service class for handling external Django API calls\nclass ApiService {\n    // Generic fetch wrapper with error handling\n    static async fetchWithErrorHandling(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseUrl).concat(endpoint);\n        try {\n            const response = await fetch(url, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...options.headers\n                },\n                ...options\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API call failed for \".concat(endpoint, \":\"), error);\n            throw error;\n        }\n    }\n    // Books API methods\n    static async getBooks(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.query) searchParams.append('query', params.query);\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) searchParams.append('premium', params.premium.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        const endpoint = \"/books/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    static async getBookById(id) {\n        return this.fetchWithErrorHandling(\"/books/\".concat(id, \"/\"));\n    }\n    // Business Plans API methods\n    static async getBusinessPlans(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.size) searchParams.append('size', params.size);\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.query) searchParams.append('query', params.query);\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) searchParams.append('premium', params.premium.toString());\n        const endpoint = \"/business-plans/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    static async getBusinessPlanById(id) {\n        return this.fetchWithErrorHandling(\"/business-plans/\".concat(id, \"/\"));\n    }\n    // Authentication API methods\n    static async login(credentials) {\n        return this.fetchWithErrorHandling('/auth/login/', {\n            method: 'POST',\n            body: JSON.stringify(credentials)\n        });\n    }\n    static async register(userData) {\n        return this.fetchWithErrorHandling('/auth/register/', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    // User API methods\n    static async getUserProfile(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/\"));\n    }\n    static async updateUserProfile(userId, userData) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/\"), {\n            method: 'PATCH',\n            body: JSON.stringify(userData)\n        });\n    }\n    // Payment API methods\n    static async processPayment(paymentData) {\n        return this.fetchWithErrorHandling('/payments/', {\n            method: 'POST',\n            body: JSON.stringify(paymentData)\n        });\n    }\n    static async verifyPayment(transactionId) {\n        return this.fetchWithErrorHandling(\"/payments/verify/\".concat(transactionId, \"/\"));\n    }\n    static async getPaymentHistory(userId) {\n        return this.fetchWithErrorHandling(\"/payments/?userId=\".concat(userId));\n    }\n    // Admin API methods (for admin dashboard)\n    static async getAdminStats() {\n        return this.fetchWithErrorHandling('/admin/stats/');\n    }\n    static async getAdminUsers(params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.search) searchParams.append('search', params.search);\n        const endpoint = \"/admin/users/\".concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        return this.fetchWithErrorHandling(endpoint);\n    }\n    // Bookmarks API methods\n    static async getUserBookmarks(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\"));\n    }\n    static async addBookmark(userId, bookId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\"), {\n            method: 'POST',\n            body: JSON.stringify({\n                bookId\n            })\n        });\n    }\n    static async removeBookmark(userId, bookId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/bookmarks/\").concat(bookId, \"/\"), {\n            method: 'DELETE'\n        });\n    }\n    // Analytics API methods\n    static async getUserAnalytics(userId) {\n        return this.fetchWithErrorHandling(\"/user/\".concat(userId, \"/analytics/\"));\n    }\n}\nApiService.baseUrl = API_BASE_URL;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authTokenManager: () => (/* binding */ authTokenManager),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   isUsingMockApi: () => (/* binding */ isUsingMockApi),\n/* harmony export */   useApiCall: () => (/* binding */ useApiCall),\n/* harmony export */   userSessionManager: () => (/* binding */ userSessionManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api-service */ \"(app-pages-browser)/./lib/api-service.ts\");\n/* harmony import */ var _mock_api_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mock-api-service */ \"(app-pages-browser)/./lib/mock-api-service.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n\n// Environment configuration\nconst USE_MOCK_API = process.env.NEXT_PUBLIC_USE_MOCK_API === 'true' || \"development\" === 'development';\n// Export the appropriate service based on environment\nconst api = USE_MOCK_API ? _mock_api_service__WEBPACK_IMPORTED_MODULE_2__.MockApiService : _api_service__WEBPACK_IMPORTED_MODULE_1__.ApiService;\n// Utility function to check if we're using mock API\nconst isUsingMockApi = ()=>USE_MOCK_API;\n// Helper function for error handling in components\nconst handleApiError = (error)=>{\n    if (error instanceof Error) {\n        return error.message;\n    }\n    return 'An unexpected error occurred';\n};\n// Configuration constants\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',\n    TIMEOUT: 10000,\n    RETRY_ATTEMPTS: 3,\n    USE_MOCK: USE_MOCK_API\n};\n// Authentication token management\nconst authTokenManager = {\n    getToken: ()=>{\n        if (false) {}\n        return localStorage.getItem('auth_token');\n    },\n    setToken: (token)=>{\n        if (false) {}\n        localStorage.setItem('auth_token', token);\n    },\n    removeToken: ()=>{\n        if (false) {}\n        localStorage.removeItem('auth_token');\n    },\n    isAuthenticated: ()=>{\n        return !!authTokenManager.getToken();\n    }\n};\n// User session management\nconst userSessionManager = {\n    getCurrentUser: ()=>{\n        if (false) {}\n        const userStr = localStorage.getItem('current_user');\n        return userStr ? JSON.parse(userStr) : null;\n    },\n    setCurrentUser: (user)=>{\n        if (false) {}\n        localStorage.setItem('current_user', JSON.stringify(user));\n    },\n    removeCurrentUser: ()=>{\n        if (false) {}\n        localStorage.removeItem('current_user');\n    },\n    logout: ()=>{\n        authTokenManager.removeToken();\n        userSessionManager.removeCurrentUser();\n    }\n};\n// Custom hook for API calls with loading and error states\nconst useApiCall = function(apiCall) {\n    let dependencies = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await apiCall();\n            setData(result);\n        } catch (err) {\n            setError(handleApiError(err));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useApiCall.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"useApiCall.useEffect\"], dependencies);\n    return {\n        data,\n        loading,\n        error,\n        refetch: fetchData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/blog-data.ts":
/*!**************************!*\
  !*** ./lib/blog-data.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBlogPostBySlug: () => (/* binding */ getBlogPostBySlug),\n/* harmony export */   getRelatedPosts: () => (/* binding */ getRelatedPosts),\n/* harmony export */   mockBlogPosts: () => (/* binding */ mockBlogPosts)\n/* harmony export */ });\n// Blog post types and mock data for Astewai blog\n// Mock data for blog posts with full content\nconst mockBlogPosts = [\n    {\n        id: \"1\",\n        title: \"The Future of Digital Reading: Why Security Matters\",\n        excerpt: \"Explore how secure digital reading platforms are revolutionizing the way we consume books while protecting authors' intellectual property.\",\n        content: \"\\n      <p>In an era where digital transformation touches every aspect of our lives, the way we read and consume literature has undergone a remarkable evolution. Digital reading platforms have emerged as powerful tools that not only enhance the reading experience but also address critical concerns around intellectual property protection and content security.</p>\\n\\n      <h2>The Digital Reading Revolution</h2>\\n      \\n      <p>The shift from physical books to digital platforms represents more than just a change in medium—it's a fundamental transformation in how we interact with written content. Digital reading platforms offer unprecedented convenience, allowing readers to access vast libraries from anywhere in the world, adjust reading preferences for optimal comfort, and engage with content in ways that were previously impossible.</p>\\n\\n      <p>However, with this convenience comes responsibility. As more authors and publishers embrace digital distribution, the need for robust security measures has become paramount. The challenge lies in balancing accessibility with protection, ensuring that readers can enjoy seamless experiences while authors' rights are respected and preserved.</p>\\n\\n      <h2>Security Challenges in Digital Publishing</h2>\\n\\n      <p>Digital content faces unique vulnerabilities that physical books simply don't encounter. Piracy, unauthorized distribution, and content theft pose significant threats to authors' livelihoods and publishers' business models. Traditional DRM (Digital Rights Management) solutions, while effective to some degree, often create friction in the user experience, leading to frustration among legitimate readers.</p>\\n\\n      <p>Modern secure reading platforms are addressing these challenges through innovative approaches that prioritize both security and user experience. By implementing advanced encryption, secure authentication systems, and intelligent content protection mechanisms, these platforms create environments where authors can confidently share their work while readers enjoy uncompromised access.</p>\\n\\n      <h2>The Technology Behind Secure Reading</h2>\\n\\n      <p>Today's leading digital reading platforms employ sophisticated technologies to ensure content security without sacrificing usability. These include:</p>\\n\\n      <ul>\\n        <li><strong>End-to-end encryption:</strong> Protecting content during transmission and storage</li>\\n        <li><strong>Watermarking:</strong> Embedding invisible identifiers to track content distribution</li>\\n        <li><strong>Secure authentication:</strong> Ensuring only authorized users can access purchased content</li>\\n        <li><strong>Real-time monitoring:</strong> Detecting and preventing unauthorized sharing attempts</li>\\n      </ul>\\n\\n      <h2>Benefits for Authors and Publishers</h2>\\n\\n      <p>Secure digital reading platforms offer numerous advantages for content creators. Authors can reach global audiences instantly, receive detailed analytics about reader engagement, and maintain greater control over their intellectual property. Publishers benefit from reduced distribution costs, enhanced market reach, and improved ability to experiment with pricing and promotional strategies.</p>\\n\\n      <p>Moreover, these platforms enable new revenue models, such as subscription services and micro-transactions, that can provide more sustainable income streams for authors while offering readers greater flexibility in how they access content.</p>\\n\\n      <h2>The Reader Experience</h2>\\n\\n      <p>From a reader's perspective, secure digital platforms offer unparalleled convenience and customization. Features like adjustable fonts, background colors, and reading speeds cater to individual preferences and accessibility needs. Social features allow readers to share insights, participate in discussions, and connect with authors and fellow readers.</p>\\n\\n      <p>The integration of multimedia elements, interactive content, and real-time updates creates immersive reading experiences that extend beyond traditional text-based consumption. Readers can access supplementary materials, author interviews, and related content that enriches their understanding and engagement with the material.</p>\\n\\n      <h2>Looking Ahead</h2>\\n\\n      <p>As we look to the future, the evolution of secure digital reading platforms will likely incorporate emerging technologies such as artificial intelligence, blockchain, and advanced analytics. These innovations promise to further enhance security measures while creating even more personalized and engaging reading experiences.</p>\\n\\n      <p>The success of digital reading platforms ultimately depends on their ability to serve all stakeholders—authors, publishers, and readers—while maintaining the highest standards of security and user experience. As the industry continues to mature, we can expect to see increasingly sophisticated solutions that make digital reading not just secure, but truly transformative.</p>\\n\\n      <p>The future of reading is digital, and with the right security measures in place, it's a future that benefits everyone in the literary ecosystem.</p>\\n    \",\n        author: \"Sarah Johnson\",\n        publishedDate: \"2024-01-15\",\n        readTime: 5,\n        category: \"Technology\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"future-digital-reading-security\",\n        tags: [\n            \"Digital Reading\",\n            \"Security\",\n            \"Technology\",\n            \"Publishing\"\n        ]\n    },\n    {\n        id: \"2\",\n        title: \"Author Interview: Building a Sustainable Writing Career\",\n        excerpt: \"We sit down with bestselling author Michael Chen to discuss the challenges and opportunities in today's publishing landscape.\",\n        content: \"\\n      <p>In this exclusive interview, we speak with Michael Chen, bestselling author of \\\"The Digital Entrepreneur\\\" and \\\"Innovation Mindset,\\\" about his journey from aspiring writer to successful author and the strategies he's used to build a sustainable writing career in today's rapidly evolving publishing landscape.</p>\\n\\n      <h2>The Journey Begins</h2>\\n\\n      <p><strong>Astewai:</strong> Michael, thank you for joining us today. Can you tell us about your journey into writing?</p>\\n\\n      <p><strong>Michael Chen:</strong> Thank you for having me. My journey into writing wasn't traditional. I started as a software engineer, but I always had stories and ideas I wanted to share. The turning point came when I realized that my technical background gave me unique insights into the digital transformation happening across industries. I decided to write about it, and that became my first book.</p>\\n\\n      <h2>Finding Your Voice</h2>\\n\\n      <p><strong>Astewai:</strong> How did you develop your unique voice as an author?</p>\\n\\n      <p><strong>Michael:</strong> Finding your voice is really about finding the intersection between what you're passionate about and what you can offer that's different. For me, it was combining technical expertise with storytelling. I realized that complex concepts could be made accessible through narrative, and that became my signature approach.</p>\\n\\n      <p>The key is authenticity. Don't try to write like someone else—write like yourself, but the best version of yourself. Your unique perspective and experiences are your greatest assets as a writer.</p>\\n\\n      <h2>The Business of Writing</h2>\\n\\n      <p><strong>Astewai:</strong> What advice do you have for authors trying to build a sustainable career?</p>\\n\\n      <p><strong>Michael:</strong> Sustainability in writing requires thinking beyond just the books. You need to build a platform, engage with your audience, and diversify your income streams. This might include speaking engagements, consulting, online courses, or subscription content.</p>\\n\\n      <p>Also, understand your metrics. Know your audience, track your sales, and understand what resonates with readers. The publishing industry has become increasingly data-driven, and authors who embrace this have a significant advantage.</p>\\n\\n      <h2>Embracing Digital Platforms</h2>\\n\\n      <p><strong>Astewai:</strong> How has the rise of digital reading platforms changed the game for authors?</p>\\n\\n      <p><strong>Michael:</strong> Digital platforms have democratized publishing in incredible ways. Authors can now reach global audiences without traditional gatekeepers, experiment with different formats and pricing models, and get real-time feedback from readers.</p>\\n\\n      <p>The direct relationship with readers is particularly powerful. Through digital platforms, I can see which chapters resonate most, where readers tend to stop, and what topics generate the most discussion. This data helps me write better books and serve my audience more effectively.</p>\\n\\n      <h2>Overcoming Challenges</h2>\\n\\n      <p><strong>Astewai:</strong> What have been your biggest challenges as an author?</p>\\n\\n      <p><strong>Michael:</strong> The biggest challenge is probably the constant need to market yourself while also finding time to write. It's easy to get caught up in promotion and social media and lose sight of the craft itself.</p>\\n\\n      <p>Another challenge is dealing with the uncertainty. Writing income can be unpredictable, and it takes time to build a sustainable career. You need to be prepared for the long game and have strategies to manage the financial ups and downs.</p>\\n\\n      <h2>Advice for Aspiring Authors</h2>\\n\\n      <p><strong>Astewai:</strong> What would you tell someone just starting their writing journey?</p>\\n\\n      <p><strong>Michael:</strong> First, write consistently. Even if it's just 15 minutes a day, consistency beats intensity. Second, read voraciously in your genre and beyond—you can't be a good writer without being a good reader.</p>\\n\\n      <p>Third, don't wait for permission. Start publishing, even if it's just blog posts or social media content. Build your audience gradually and learn from their feedback. The barrier to entry has never been lower, so take advantage of that.</p>\\n\\n      <p>Finally, be patient with yourself. Building a writing career takes time, and every author's journey is different. Focus on improving your craft and serving your readers, and the rest will follow.</p>\\n\\n      <h2>The Future of Publishing</h2>\\n\\n      <p><strong>Astewai:</strong> Where do you see the publishing industry heading?</p>\\n\\n      <p><strong>Michael:</strong> I think we'll see continued growth in digital platforms, more interactive and multimedia content, and increased personalization. AI will play a bigger role in helping authors with research, editing, and even marketing.</p>\\n\\n      <p>But at the end of the day, good storytelling and valuable content will always be at the heart of successful publishing. Technology changes, but the human need for stories and knowledge remains constant.</p>\\n\\n      <p><strong>Astewai:</strong> Thank you, Michael, for sharing your insights with our readers.</p>\\n\\n      <p><strong>Michael:</strong> My pleasure. Keep writing, keep learning, and remember that every published author was once where you are now.</p>\\n    \",\n        author: \"Emma Davis\",\n        publishedDate: \"2024-01-12\",\n        readTime: 8,\n        category: \"Author Interview\",\n        coverImage: \"/placeholder.svg?height=200&width=400\",\n        slug: \"author-interview-michael-chen\",\n        tags: [\n            \"Author Interview\",\n            \"Writing Career\",\n            \"Publishing\",\n            \"Digital Platforms\"\n        ]\n    }\n];\n// Helper function to get blog post by slug\nfunction getBlogPostBySlug(slug) {\n    return mockBlogPosts.find((post)=>post.slug === slug);\n}\n// Helper function to get related posts\nfunction getRelatedPosts(currentSlug) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    const currentPost = getBlogPostBySlug(currentSlug);\n    if (!currentPost) return [];\n    return mockBlogPosts.filter((post)=>post.slug !== currentSlug && post.category === currentPost.category).slice(0, limit);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/blog-data.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/mock-api-service.ts":
/*!*********************************!*\
  !*** ./lib/mock-api-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockApiService: () => (/* binding */ MockApiService)\n/* harmony export */ });\n/* harmony import */ var _mock_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mock-data */ \"(app-pages-browser)/./lib/mock-data.ts\");\n/* harmony import */ var _blog_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blog-data */ \"(app-pages-browser)/./lib/blog-data.ts\");\n\n\n// Mock API service for development when Django backend is not available\nclass MockApiService {\n    // Simulate network delay\n    static delay() {\n        let ms = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 500;\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    // Books API methods\n    static async getBooks(params) {\n        await this.delay();\n        let filteredBooks = [\n            ..._mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"all\") {\n            filteredBooks = filteredBooks.filter((book)=>book.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredBooks = filteredBooks.filter((book)=>book.title.toLowerCase().includes(searchQuery) || book.author.toLowerCase().includes(searchQuery));\n        }\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) {\n            const fullBooks = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.filter((book)=>book.isPremium === params.premium);\n            const premiumBookIds = fullBooks.map((book)=>book.id);\n            filteredBooks = filteredBooks.filter((book)=>premiumBookIds.includes(book.id));\n        }\n        if (params === null || params === void 0 ? void 0 : params.limit) {\n            filteredBooks = filteredBooks.slice(0, params.limit);\n        }\n        return filteredBooks;\n    }\n    static async getBookById(id) {\n        await this.delay();\n        const book = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.find((book)=>book.id === id);\n        if (!book) {\n            throw new Error(\"Book with id \".concat(id, \" not found\"));\n        }\n        return book;\n    }\n    // Free Books API methods\n    static async getFreeBooks(params) {\n        await this.delay();\n        let freeBooks = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews.filter((book)=>{\n            const fullBook = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.find((b)=>b.id === book.id);\n            return (fullBook === null || fullBook === void 0 ? void 0 : fullBook.isFree) || (fullBook === null || fullBook === void 0 ? void 0 : fullBook.price) === 0;\n        });\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"All\") {\n            freeBooks = freeBooks.filter((book)=>book.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            freeBooks = freeBooks.filter((book)=>book.title.toLowerCase().includes(searchQuery) || book.author.toLowerCase().includes(searchQuery) || book.category.toLowerCase().includes(searchQuery));\n        }\n        if (params === null || params === void 0 ? void 0 : params.sortBy) {\n            freeBooks.sort((a, b)=>{\n                switch(params.sortBy){\n                    case \"rating\":\n                        return b.rating - a.rating;\n                    case \"author\":\n                        return a.author.localeCompare(b.author);\n                    case \"title\":\n                    default:\n                        return a.title.localeCompare(b.title);\n                }\n            });\n        }\n        if (params === null || params === void 0 ? void 0 : params.limit) {\n            freeBooks = freeBooks.slice(0, params.limit);\n        }\n        return freeBooks;\n    }\n    // Blog API methods\n    static async getBlogPosts(params) {\n        await this.delay();\n        let filteredPosts = [\n            ..._blog_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"All\") {\n            filteredPosts = filteredPosts.filter((post)=>post.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredPosts = filteredPosts.filter((post)=>post.title.toLowerCase().includes(searchQuery) || post.excerpt.toLowerCase().includes(searchQuery) || post.author.toLowerCase().includes(searchQuery));\n        }\n        if (params === null || params === void 0 ? void 0 : params.limit) {\n            filteredPosts = filteredPosts.slice(0, params.limit);\n        }\n        return filteredPosts;\n    }\n    static async getBlogPostBySlug(slug) {\n        await this.delay();\n        const post = _blog_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts.find((post)=>post.slug === slug);\n        if (!post) {\n            throw new Error(\"Blog post with slug \".concat(slug, \" not found\"));\n        }\n        return post;\n    }\n    static async getRelatedBlogPosts(currentSlug) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n        await this.delay();\n        const currentPost = _blog_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts.find((post)=>post.slug === currentSlug);\n        if (!currentPost) return [];\n        return _blog_data__WEBPACK_IMPORTED_MODULE_1__.mockBlogPosts.filter((post)=>post.slug !== currentSlug && post.category === currentPost.category).slice(0, limit);\n    }\n    // Business Plans API methods\n    static async getBusinessPlans(params) {\n        await this.delay();\n        let filteredPlans = [\n            ..._mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlanPreviews\n        ];\n        if ((params === null || params === void 0 ? void 0 : params.size) && params.size !== \"all\") {\n            filteredPlans = filteredPlans.filter((plan)=>plan.size === params.size);\n        }\n        if ((params === null || params === void 0 ? void 0 : params.category) && params.category !== \"all\") {\n            filteredPlans = filteredPlans.filter((plan)=>plan.category.toLowerCase() === params.category.toLowerCase());\n        }\n        if (params === null || params === void 0 ? void 0 : params.query) {\n            const searchQuery = params.query.toLowerCase();\n            filteredPlans = filteredPlans.filter((plan)=>plan.title.toLowerCase().includes(searchQuery) || plan.category.toLowerCase().includes(searchQuery));\n        }\n        if ((params === null || params === void 0 ? void 0 : params.premium) !== undefined) {\n            filteredPlans = filteredPlans.filter((plan)=>plan.isPremium === params.premium);\n        }\n        return filteredPlans;\n    }\n    static async getBusinessPlanById(id) {\n        await this.delay();\n        const plan = _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlans.find((plan)=>plan.id === id);\n        if (!plan) {\n            throw new Error(\"Business plan with id \".concat(id, \" not found\"));\n        }\n        return plan;\n    }\n    // Authentication API methods\n    static async login(credentials) {\n        await this.delay();\n        // Mock successful login for demo purposes\n        return {\n            id: \"user-1\",\n            firstName: \"John\",\n            lastName: \"Doe\",\n            email: credentials.email,\n            plan: \"medium\",\n            token: \"mock-jwt-token-123\"\n        };\n    }\n    static async register(userData) {\n        await this.delay();\n        // Mock successful registration\n        return {\n            id: \"user-\".concat(Date.now()),\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            email: userData.email,\n            plan: \"base\",\n            token: \"mock-jwt-token-456\"\n        };\n    }\n    // User API methods\n    static async getUserProfile(userId) {\n        await this.delay();\n        return {\n            user: {\n                id: userId,\n                firstName: \"John\",\n                lastName: \"Doe\",\n                email: \"<EMAIL>\",\n                plan: \"medium\"\n            },\n            activities: [\n                {\n                    id: \"activity-1\",\n                    userId: userId,\n                    type: \"book_read\",\n                    itemId: \"the-psychology-of-money\",\n                    itemTitle: \"The Psychology of Money\",\n                    timestamp: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: \"activity-2\",\n                    userId: userId,\n                    type: \"plan_viewed\",\n                    itemId: \"small-1\",\n                    itemTitle: \"Local Coffee Shop\",\n                    timestamp: new Date(Date.now() - 172800000).toISOString()\n                }\n            ]\n        };\n    }\n    static async updateUserProfile(userId, userData) {\n        await this.delay();\n        return {\n            id: userId,\n            firstName: userData.firstName || \"John\",\n            lastName: userData.lastName || \"Doe\",\n            email: userData.email || \"<EMAIL>\",\n            plan: \"medium\"\n        };\n    }\n    // Payment API methods\n    static async processPayment(paymentData) {\n        await this.delay(1000) // Longer delay for payment processing\n        ;\n        // Mock successful payment\n        return {\n            success: true,\n            transactionId: \"TRX-\".concat(Date.now(), \"-\").concat(Math.floor(Math.random() * 1000)),\n            message: \"Payment processed successfully\",\n            timestamp: new Date().toISOString(),\n            plan: paymentData.plan\n        };\n    }\n    static async verifyPayment(transactionId) {\n        await this.delay();\n        return {\n            success: true,\n            status: \"completed\",\n            message: \"Payment verified successfully\"\n        };\n    }\n    static async getPaymentHistory(userId) {\n        await this.delay();\n        return [\n            {\n                success: true,\n                transactionId: \"TRX-123456789\",\n                message: \"Payment processed successfully\",\n                timestamp: new Date(Date.now() - 2592000000).toISOString(),\n                plan: \"medium\"\n            }\n        ];\n    }\n    // Admin API methods\n    static async getAdminStats() {\n        await this.delay();\n        return {\n            totalUsers: 1250,\n            totalBooks: _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBooks.length,\n            totalBusinessPlans: _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBusinessPlans.length,\n            revenueThisMonth: 15750\n        };\n    }\n    static async getAdminUsers(params) {\n        await this.delay();\n        const mockUsers = [\n            {\n                id: \"user-1\",\n                firstName: \"John\",\n                lastName: \"Doe\",\n                email: \"<EMAIL>\",\n                plan: \"medium\",\n                createdAt: \"2023-01-15T00:00:00Z\"\n            },\n            {\n                id: \"user-2\",\n                firstName: \"Jane\",\n                lastName: \"Smith\",\n                email: \"<EMAIL>\",\n                plan: \"base\",\n                createdAt: \"2023-03-22T00:00:00Z\"\n            }\n        ];\n        return {\n            users: mockUsers,\n            total: mockUsers.length,\n            page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n            totalPages: 1\n        };\n    }\n    // Bookmarks API methods\n    static async getUserBookmarks(userId) {\n        await this.delay();\n        // Return first 2 books as bookmarked for demo\n        return _mock_data__WEBPACK_IMPORTED_MODULE_0__.mockBookPreviews.slice(0, 2);\n    }\n    static async addBookmark(userId, bookId) {\n        await this.delay();\n        return {\n            success: true\n        };\n    }\n    static async removeBookmark(userId, bookId) {\n        await this.delay();\n        return {\n            success: true\n        };\n    }\n    // Analytics API methods\n    static async getUserAnalytics(userId) {\n        await this.delay();\n        return {\n            readingStats: {\n                booksRead: 12,\n                totalReadingTime: 2400,\n                averageReadingSpeed: 250,\n                streakDays: 7\n            },\n            recentActivity: [\n                {\n                    id: \"activity-1\",\n                    type: \"book_read\",\n                    itemTitle: \"The Psychology of Money\",\n                    timestamp: new Date(Date.now() - 86400000).toISOString()\n                },\n                {\n                    id: \"activity-2\",\n                    type: \"plan_viewed\",\n                    itemTitle: \"Local Coffee Shop\",\n                    timestamp: new Date(Date.now() - 172800000).toISOString()\n                }\n            ]\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9tb2NrLWFwaS1zZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQVFvQjtBQUN1QjtBQUUzQyx3RUFBd0U7QUFDakUsTUFBTUs7SUFDWCx5QkFBeUI7SUFDekIsT0FBZUMsUUFBdUM7WUFBakNDLEtBQUFBLGlFQUFhO1FBQ2hDLE9BQU8sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBU0Y7SUFDcEQ7SUFFQSxvQkFBb0I7SUFDcEIsYUFBYUksU0FBU0MsTUFLckIsRUFBMEI7UUFDekIsTUFBTSxJQUFJLENBQUNOLEtBQUs7UUFFaEIsSUFBSU8sZ0JBQWdCO2VBQUlaLHdEQUFnQkE7U0FBQztRQUV6QyxJQUFJVyxDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFFLFFBQVEsS0FBSUYsT0FBT0UsUUFBUSxLQUFLLE9BQU87WUFDakRELGdCQUFnQkEsY0FBY0UsTUFBTSxDQUNsQ0MsQ0FBQUEsT0FBUUEsS0FBS0YsUUFBUSxDQUFDRyxXQUFXLE9BQU9MLE9BQU9FLFFBQVEsQ0FBRUcsV0FBVztRQUV4RTtRQUVBLElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFO1lBQ2pCLE1BQU1DLGNBQWNQLE9BQU9NLEtBQUssQ0FBQ0QsV0FBVztZQUM1Q0osZ0JBQWdCQSxjQUFjRSxNQUFNLENBQ2xDQyxDQUFBQSxPQUNFQSxLQUFLSSxLQUFLLENBQUNILFdBQVcsR0FBR0ksUUFBUSxDQUFDRixnQkFDbENILEtBQUtNLE1BQU0sQ0FBQ0wsV0FBVyxHQUFHSSxRQUFRLENBQUNGO1FBRXpDO1FBRUEsSUFBSVAsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRVyxPQUFPLE1BQUtDLFdBQVc7WUFDakMsTUFBTUMsWUFBWXpCLGlEQUFTQSxDQUFDZSxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtVLFNBQVMsS0FBS2QsT0FBT1csT0FBTztZQUM1RSxNQUFNSSxpQkFBaUJGLFVBQVVHLEdBQUcsQ0FBQ1osQ0FBQUEsT0FBUUEsS0FBS2EsRUFBRTtZQUNwRGhCLGdCQUFnQkEsY0FBY0UsTUFBTSxDQUFDQyxDQUFBQSxPQUFRVyxlQUFlTixRQUFRLENBQUNMLEtBQUthLEVBQUU7UUFDOUU7UUFFQSxJQUFJakIsbUJBQUFBLDZCQUFBQSxPQUFRa0IsS0FBSyxFQUFFO1lBQ2pCakIsZ0JBQWdCQSxjQUFja0IsS0FBSyxDQUFDLEdBQUduQixPQUFPa0IsS0FBSztRQUNyRDtRQUVBLE9BQU9qQjtJQUNUO0lBRUEsYUFBYW1CLFlBQVlILEVBQVUsRUFBaUI7UUFDbEQsTUFBTSxJQUFJLENBQUN2QixLQUFLO1FBRWhCLE1BQU1VLE9BQU9oQixpREFBU0EsQ0FBQ2lDLElBQUksQ0FBQ2pCLENBQUFBLE9BQVFBLEtBQUthLEVBQUUsS0FBS0E7UUFDaEQsSUFBSSxDQUFDYixNQUFNO1lBQ1QsTUFBTSxJQUFJa0IsTUFBTSxnQkFBbUIsT0FBSEwsSUFBRztRQUNyQztRQUVBLE9BQU9iO0lBQ1Q7SUFFQSx5QkFBeUI7SUFDekIsYUFBYW1CLGFBQWF2QixNQUt6QixFQUEwQjtRQUN6QixNQUFNLElBQUksQ0FBQ04sS0FBSztRQUVoQixJQUFJOEIsWUFBWW5DLHdEQUFnQkEsQ0FBQ2MsTUFBTSxDQUFDQyxDQUFBQTtZQUN0QyxNQUFNcUIsV0FBV3JDLGlEQUFTQSxDQUFDaUMsSUFBSSxDQUFDSyxDQUFBQSxJQUFLQSxFQUFFVCxFQUFFLEtBQUtiLEtBQUthLEVBQUU7WUFDckQsT0FBT1EsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVRSxNQUFNLEtBQUlGLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVUcsS0FBSyxNQUFLO1FBQ2pEO1FBRUEsSUFBSTVCLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUUUsUUFBUSxLQUFJRixPQUFPRSxRQUFRLEtBQUssT0FBTztZQUNqRHNCLFlBQVlBLFVBQVVyQixNQUFNLENBQzFCQyxDQUFBQSxPQUFRQSxLQUFLRixRQUFRLENBQUNHLFdBQVcsT0FBT0wsT0FBT0UsUUFBUSxDQUFFRyxXQUFXO1FBRXhFO1FBRUEsSUFBSUwsbUJBQUFBLDZCQUFBQSxPQUFRTSxLQUFLLEVBQUU7WUFDakIsTUFBTUMsY0FBY1AsT0FBT00sS0FBSyxDQUFDRCxXQUFXO1lBQzVDbUIsWUFBWUEsVUFBVXJCLE1BQU0sQ0FDMUJDLENBQUFBLE9BQ0VBLEtBQUtJLEtBQUssQ0FBQ0gsV0FBVyxHQUFHSSxRQUFRLENBQUNGLGdCQUNsQ0gsS0FBS00sTUFBTSxDQUFDTCxXQUFXLEdBQUdJLFFBQVEsQ0FBQ0YsZ0JBQ25DSCxLQUFLRixRQUFRLENBQUNHLFdBQVcsR0FBR0ksUUFBUSxDQUFDRjtRQUUzQztRQUVBLElBQUlQLG1CQUFBQSw2QkFBQUEsT0FBUTZCLE1BQU0sRUFBRTtZQUNsQkwsVUFBVU0sSUFBSSxDQUFDLENBQUNDLEdBQUdMO2dCQUNqQixPQUFRMUIsT0FBTzZCLE1BQU07b0JBQ25CLEtBQUs7d0JBQ0gsT0FBT0gsRUFBRU0sTUFBTSxHQUFHRCxFQUFFQyxNQUFNO29CQUM1QixLQUFLO3dCQUNILE9BQU9ELEVBQUVyQixNQUFNLENBQUN1QixhQUFhLENBQUNQLEVBQUVoQixNQUFNO29CQUN4QyxLQUFLO29CQUNMO3dCQUNFLE9BQU9xQixFQUFFdkIsS0FBSyxDQUFDeUIsYUFBYSxDQUFDUCxFQUFFbEIsS0FBSztnQkFDeEM7WUFDRjtRQUNGO1FBRUEsSUFBSVIsbUJBQUFBLDZCQUFBQSxPQUFRa0IsS0FBSyxFQUFFO1lBQ2pCTSxZQUFZQSxVQUFVTCxLQUFLLENBQUMsR0FBR25CLE9BQU9rQixLQUFLO1FBQzdDO1FBRUEsT0FBT007SUFDVDtJQUVBLG1CQUFtQjtJQUNuQixhQUFhVSxhQUFhbEMsTUFJekIsRUFBdUI7UUFDdEIsTUFBTSxJQUFJLENBQUNOLEtBQUs7UUFFaEIsSUFBSXlDLGdCQUFnQjtlQUFJM0MscURBQWFBO1NBQUM7UUFFdEMsSUFBSVEsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRRSxRQUFRLEtBQUlGLE9BQU9FLFFBQVEsS0FBSyxPQUFPO1lBQ2pEaUMsZ0JBQWdCQSxjQUFjaEMsTUFBTSxDQUNsQ2lDLENBQUFBLE9BQVFBLEtBQUtsQyxRQUFRLENBQUNHLFdBQVcsT0FBT0wsT0FBT0UsUUFBUSxDQUFFRyxXQUFXO1FBRXhFO1FBRUEsSUFBSUwsbUJBQUFBLDZCQUFBQSxPQUFRTSxLQUFLLEVBQUU7WUFDakIsTUFBTUMsY0FBY1AsT0FBT00sS0FBSyxDQUFDRCxXQUFXO1lBQzVDOEIsZ0JBQWdCQSxjQUFjaEMsTUFBTSxDQUNsQ2lDLENBQUFBLE9BQ0VBLEtBQUs1QixLQUFLLENBQUNILFdBQVcsR0FBR0ksUUFBUSxDQUFDRixnQkFDbEM2QixLQUFLQyxPQUFPLENBQUNoQyxXQUFXLEdBQUdJLFFBQVEsQ0FBQ0YsZ0JBQ3BDNkIsS0FBSzFCLE1BQU0sQ0FBQ0wsV0FBVyxHQUFHSSxRQUFRLENBQUNGO1FBRXpDO1FBRUEsSUFBSVAsbUJBQUFBLDZCQUFBQSxPQUFRa0IsS0FBSyxFQUFFO1lBQ2pCaUIsZ0JBQWdCQSxjQUFjaEIsS0FBSyxDQUFDLEdBQUduQixPQUFPa0IsS0FBSztRQUNyRDtRQUVBLE9BQU9pQjtJQUNUO0lBRUEsYUFBYUcsa0JBQWtCQyxJQUFZLEVBQXFCO1FBQzlELE1BQU0sSUFBSSxDQUFDN0MsS0FBSztRQUVoQixNQUFNMEMsT0FBTzVDLHFEQUFhQSxDQUFDNkIsSUFBSSxDQUFDZSxDQUFBQSxPQUFRQSxLQUFLRyxJQUFJLEtBQUtBO1FBQ3RELElBQUksQ0FBQ0gsTUFBTTtZQUNULE1BQU0sSUFBSWQsTUFBTSx1QkFBNEIsT0FBTGlCLE1BQUs7UUFDOUM7UUFFQSxPQUFPSDtJQUNUO0lBRUEsYUFBYUksb0JBQW9CQyxXQUFtQixFQUEwQztZQUF4Q3ZCLFFBQUFBLGlFQUFnQjtRQUNwRSxNQUFNLElBQUksQ0FBQ3hCLEtBQUs7UUFFaEIsTUFBTWdELGNBQWNsRCxxREFBYUEsQ0FBQzZCLElBQUksQ0FBQ2UsQ0FBQUEsT0FBUUEsS0FBS0csSUFBSSxLQUFLRTtRQUM3RCxJQUFJLENBQUNDLGFBQWEsT0FBTyxFQUFFO1FBRTNCLE9BQU9sRCxxREFBYUEsQ0FDakJXLE1BQU0sQ0FBQ2lDLENBQUFBLE9BQVFBLEtBQUtHLElBQUksS0FBS0UsZUFBZUwsS0FBS2xDLFFBQVEsS0FBS3dDLFlBQVl4QyxRQUFRLEVBQ2xGaUIsS0FBSyxDQUFDLEdBQUdEO0lBQ2Q7SUFFQSw2QkFBNkI7SUFDN0IsYUFBYXlCLGlCQUFpQjNDLE1BSzdCLEVBQWtDO1FBQ2pDLE1BQU0sSUFBSSxDQUFDTixLQUFLO1FBRWhCLElBQUlrRCxnQkFBZ0I7ZUFBSXJELGdFQUF3QkE7U0FBQztRQUVqRCxJQUFJUyxDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVE2QyxJQUFJLEtBQUk3QyxPQUFPNkMsSUFBSSxLQUFLLE9BQU87WUFDekNELGdCQUFnQkEsY0FBY3pDLE1BQU0sQ0FBQzJDLENBQUFBLE9BQVFBLEtBQUtELElBQUksS0FBSzdDLE9BQU82QyxJQUFJO1FBQ3hFO1FBRUEsSUFBSTdDLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUUUsUUFBUSxLQUFJRixPQUFPRSxRQUFRLEtBQUssT0FBTztZQUNqRDBDLGdCQUFnQkEsY0FBY3pDLE1BQU0sQ0FDbEMyQyxDQUFBQSxPQUFRQSxLQUFLNUMsUUFBUSxDQUFDRyxXQUFXLE9BQU9MLE9BQU9FLFFBQVEsQ0FBRUcsV0FBVztRQUV4RTtRQUVBLElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFO1lBQ2pCLE1BQU1DLGNBQWNQLE9BQU9NLEtBQUssQ0FBQ0QsV0FBVztZQUM1Q3VDLGdCQUFnQkEsY0FBY3pDLE1BQU0sQ0FDbEMyQyxDQUFBQSxPQUNFQSxLQUFLdEMsS0FBSyxDQUFDSCxXQUFXLEdBQUdJLFFBQVEsQ0FBQ0YsZ0JBQ2xDdUMsS0FBSzVDLFFBQVEsQ0FBQ0csV0FBVyxHQUFHSSxRQUFRLENBQUNGO1FBRTNDO1FBRUEsSUFBSVAsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRVyxPQUFPLE1BQUtDLFdBQVc7WUFDakNnQyxnQkFBZ0JBLGNBQWN6QyxNQUFNLENBQUMyQyxDQUFBQSxPQUFRQSxLQUFLaEMsU0FBUyxLQUFLZCxPQUFPVyxPQUFPO1FBQ2hGO1FBRUEsT0FBT2lDO0lBQ1Q7SUFFQSxhQUFhRyxvQkFBb0I5QixFQUFVLEVBQXlCO1FBQ2xFLE1BQU0sSUFBSSxDQUFDdkIsS0FBSztRQUVoQixNQUFNb0QsT0FBT3hELHlEQUFpQkEsQ0FBQytCLElBQUksQ0FBQ3lCLENBQUFBLE9BQVFBLEtBQUs3QixFQUFFLEtBQUtBO1FBQ3hELElBQUksQ0FBQzZCLE1BQU07WUFDVCxNQUFNLElBQUl4QixNQUFNLHlCQUE0QixPQUFITCxJQUFHO1FBQzlDO1FBRUEsT0FBTzZCO0lBQ1Q7SUFFQSw2QkFBNkI7SUFDN0IsYUFBYUUsTUFBTUMsV0FHbEIsRUFPRTtRQUNELE1BQU0sSUFBSSxDQUFDdkQsS0FBSztRQUVoQiwwQ0FBMEM7UUFDMUMsT0FBTztZQUNMdUIsSUFBSTtZQUNKaUMsV0FBVztZQUNYQyxVQUFVO1lBQ1ZDLE9BQU9ILFlBQVlHLEtBQUs7WUFDeEJOLE1BQU07WUFDTk8sT0FBTztRQUNUO0lBQ0Y7SUFFQSxhQUFhQyxTQUFTQyxRQUtyQixFQU9FO1FBQ0QsTUFBTSxJQUFJLENBQUM3RCxLQUFLO1FBRWhCLCtCQUErQjtRQUMvQixPQUFPO1lBQ0x1QixJQUFJLFFBQW1CLE9BQVh1QyxLQUFLQyxHQUFHO1lBQ3BCUCxXQUFXSyxTQUFTTCxTQUFTO1lBQzdCQyxVQUFVSSxTQUFTSixRQUFRO1lBQzNCQyxPQUFPRyxTQUFTSCxLQUFLO1lBQ3JCTixNQUFNO1lBQ05PLE9BQU87UUFDVDtJQUNGO0lBRUEsbUJBQW1CO0lBQ25CLGFBQWFLLGVBQWVDLE1BQWMsRUFnQnZDO1FBQ0QsTUFBTSxJQUFJLENBQUNqRSxLQUFLO1FBRWhCLE9BQU87WUFDTGtFLE1BQU07Z0JBQ0ozQyxJQUFJMEM7Z0JBQ0pULFdBQVc7Z0JBQ1hDLFVBQVU7Z0JBQ1ZDLE9BQU87Z0JBQ1BOLE1BQU07WUFDUjtZQUNBZSxZQUFZO2dCQUNWO29CQUNFNUMsSUFBSTtvQkFDSjBDLFFBQVFBO29CQUNSRyxNQUFNO29CQUNOQyxRQUFRO29CQUNSQyxXQUFXO29CQUNYQyxXQUFXLElBQUlULEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxVQUFVUyxXQUFXO2dCQUN4RDtnQkFDQTtvQkFDRWpELElBQUk7b0JBQ0owQyxRQUFRQTtvQkFDUkcsTUFBTTtvQkFDTkMsUUFBUTtvQkFDUkMsV0FBVztvQkFDWEMsV0FBVyxJQUFJVCxLQUFLQSxLQUFLQyxHQUFHLEtBQUssV0FBV1MsV0FBVztnQkFDekQ7YUFDRDtRQUNIO0lBQ0Y7SUFFQSxhQUFhQyxrQkFBa0JSLE1BQWMsRUFBRUosUUFJOUMsRUFNRTtRQUNELE1BQU0sSUFBSSxDQUFDN0QsS0FBSztRQUVoQixPQUFPO1lBQ0x1QixJQUFJMEM7WUFDSlQsV0FBV0ssU0FBU0wsU0FBUyxJQUFJO1lBQ2pDQyxVQUFVSSxTQUFTSixRQUFRLElBQUk7WUFDL0JDLE9BQU9HLFNBQVNILEtBQUssSUFBSTtZQUN6Qk4sTUFBTTtRQUNSO0lBQ0Y7SUFFQSxzQkFBc0I7SUFDdEIsYUFBYXNCLGVBQWVDLFdBVTNCLEVBTUU7UUFDRCxNQUFNLElBQUksQ0FBQzNFLEtBQUssQ0FBQyxNQUFNLHNDQUFzQzs7UUFFN0QsMEJBQTBCO1FBQzFCLE9BQU87WUFDTDRFLFNBQVM7WUFDVEMsZUFBZSxPQUFxQkMsT0FBZGhCLEtBQUtDLEdBQUcsSUFBRyxLQUFvQyxPQUFqQ2UsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUs7WUFDL0RDLFNBQVM7WUFDVFYsV0FBVyxJQUFJVCxPQUFPVSxXQUFXO1lBQ2pDcEIsTUFBTXVCLFlBQVl2QixJQUFJO1FBQ3hCO0lBQ0Y7SUFFQSxhQUFhOEIsY0FBY0wsYUFBcUIsRUFJN0M7UUFDRCxNQUFNLElBQUksQ0FBQzdFLEtBQUs7UUFFaEIsT0FBTztZQUNMNEUsU0FBUztZQUNUTyxRQUFRO1lBQ1JGLFNBQVM7UUFDWDtJQUNGO0lBRUEsYUFBYUcsa0JBQWtCbkIsTUFBYyxFQU16QztRQUNGLE1BQU0sSUFBSSxDQUFDakUsS0FBSztRQUVoQixPQUFPO1lBQ0w7Z0JBQ0U0RSxTQUFTO2dCQUNUQyxlQUFlO2dCQUNmSSxTQUFTO2dCQUNUVixXQUFXLElBQUlULEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxZQUFZUyxXQUFXO2dCQUN4RHBCLE1BQU07WUFDUjtTQUNEO0lBQ0g7SUFFQSxvQkFBb0I7SUFDcEIsYUFBYWlDLGdCQUtWO1FBQ0QsTUFBTSxJQUFJLENBQUNyRixLQUFLO1FBRWhCLE9BQU87WUFDTHNGLFlBQVk7WUFDWkMsWUFBWTdGLGlEQUFTQSxDQUFDOEYsTUFBTTtZQUM1QkMsb0JBQW9CN0YseURBQWlCQSxDQUFDNEYsTUFBTTtZQUM1Q0Usa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxhQUFhQyxjQUFjckYsTUFJMUIsRUFZRTtRQUNELE1BQU0sSUFBSSxDQUFDTixLQUFLO1FBRWhCLE1BQU00RixZQUFZO1lBQ2hCO2dCQUNFckUsSUFBSTtnQkFDSmlDLFdBQVc7Z0JBQ1hDLFVBQVU7Z0JBQ1ZDLE9BQU87Z0JBQ1BOLE1BQU07Z0JBQ055QyxXQUFXO1lBQ2I7WUFDQTtnQkFDRXRFLElBQUk7Z0JBQ0ppQyxXQUFXO2dCQUNYQyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQTixNQUFNO2dCQUNOeUMsV0FBVztZQUNiO1NBQ0Q7UUFFRCxPQUFPO1lBQ0xDLE9BQU9GO1lBQ1BHLE9BQU9ILFVBQVVKLE1BQU07WUFDdkJRLE1BQU0xRixDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVEwRixJQUFJLEtBQUk7WUFDdEJDLFlBQVk7UUFDZDtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCLGFBQWFDLGlCQUFpQmpDLE1BQWMsRUFBMEI7UUFDcEUsTUFBTSxJQUFJLENBQUNqRSxLQUFLO1FBRWhCLDhDQUE4QztRQUM5QyxPQUFPTCx3REFBZ0JBLENBQUM4QixLQUFLLENBQUMsR0FBRztJQUNuQztJQUVBLGFBQWEwRSxZQUFZbEMsTUFBYyxFQUFFbUMsTUFBYyxFQUFpQztRQUN0RixNQUFNLElBQUksQ0FBQ3BHLEtBQUs7UUFFaEIsT0FBTztZQUFFNEUsU0FBUztRQUFLO0lBQ3pCO0lBRUEsYUFBYXlCLGVBQWVwQyxNQUFjLEVBQUVtQyxNQUFjLEVBQWlDO1FBQ3pGLE1BQU0sSUFBSSxDQUFDcEcsS0FBSztRQUVoQixPQUFPO1lBQUU0RSxTQUFTO1FBQUs7SUFDekI7SUFFQSx3QkFBd0I7SUFDeEIsYUFBYTBCLGlCQUFpQnJDLE1BQWMsRUFhekM7UUFDRCxNQUFNLElBQUksQ0FBQ2pFLEtBQUs7UUFFaEIsT0FBTztZQUNMdUcsY0FBYztnQkFDWkMsV0FBVztnQkFDWEMsa0JBQWtCO2dCQUNsQkMscUJBQXFCO2dCQUNyQkMsWUFBWTtZQUNkO1lBQ0FDLGdCQUFnQjtnQkFDZDtvQkFDRXJGLElBQUk7b0JBQ0o2QyxNQUFNO29CQUNORSxXQUFXO29CQUNYQyxXQUFXLElBQUlULEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxVQUFVUyxXQUFXO2dCQUN4RDtnQkFDQTtvQkFDRWpELElBQUk7b0JBQ0o2QyxNQUFNO29CQUNORSxXQUFXO29CQUNYQyxXQUFXLElBQUlULEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxXQUFXUyxXQUFXO2dCQUN6RDthQUNEO1FBQ0g7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxEZXNrdG9wXFxQcm9qZWN0c1xcQXN0ZXdhaVxcVGlsa1RlYmViXFx0aWxrdGJlYiAoMSlcXGxpYlxcbW9jay1hcGktc2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEJvb2ssIEJvb2tQcmV2aWV3IH0gZnJvbSBcIkAvdHlwZXMvYm9va1wiXG5pbXBvcnQgdHlwZSB7IEJ1c2luZXNzUGxhbiwgQnVzaW5lc3NQbGFuUHJldmlldyB9IGZyb20gXCIuL21vY2stZGF0YVwiXG5pbXBvcnQgdHlwZSB7IEJsb2dQb3N0IH0gZnJvbSBcIi4vYmxvZy1kYXRhXCJcbmltcG9ydCB7XG4gIG1vY2tCb29rcyxcbiAgbW9ja0Jvb2tQcmV2aWV3cyxcbiAgbW9ja0J1c2luZXNzUGxhbnMsXG4gIG1vY2tCdXNpbmVzc1BsYW5QcmV2aWV3c1xufSBmcm9tIFwiLi9tb2NrLWRhdGFcIlxuaW1wb3J0IHsgbW9ja0Jsb2dQb3N0cyB9IGZyb20gXCIuL2Jsb2ctZGF0YVwiXG5cbi8vIE1vY2sgQVBJIHNlcnZpY2UgZm9yIGRldmVsb3BtZW50IHdoZW4gRGphbmdvIGJhY2tlbmQgaXMgbm90IGF2YWlsYWJsZVxuZXhwb3J0IGNsYXNzIE1vY2tBcGlTZXJ2aWNlIHtcbiAgLy8gU2ltdWxhdGUgbmV0d29yayBkZWxheVxuICBwcml2YXRlIHN0YXRpYyBkZWxheShtczogbnVtYmVyID0gNTAwKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCBtcykpXG4gIH1cblxuICAvLyBCb29rcyBBUEkgbWV0aG9kc1xuICBzdGF0aWMgYXN5bmMgZ2V0Qm9va3MocGFyYW1zPzoge1xuICAgIGNhdGVnb3J5Pzogc3RyaW5nXG4gICAgcXVlcnk/OiBzdHJpbmdcbiAgICBwcmVtaXVtPzogYm9vbGVhblxuICAgIGxpbWl0PzogbnVtYmVyXG4gIH0pOiBQcm9taXNlPEJvb2tQcmV2aWV3W10+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcbiAgICBcbiAgICBsZXQgZmlsdGVyZWRCb29rcyA9IFsuLi5tb2NrQm9va1ByZXZpZXdzXVxuXG4gICAgaWYgKHBhcmFtcz8uY2F0ZWdvcnkgJiYgcGFyYW1zLmNhdGVnb3J5ICE9PSBcImFsbFwiKSB7XG4gICAgICBmaWx0ZXJlZEJvb2tzID0gZmlsdGVyZWRCb29rcy5maWx0ZXIoXG4gICAgICAgIGJvb2sgPT4gYm9vay5jYXRlZ29yeS50b0xvd2VyQ2FzZSgpID09PSBwYXJhbXMuY2F0ZWdvcnkhLnRvTG93ZXJDYXNlKClcbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAocGFyYW1zPy5xdWVyeSkge1xuICAgICAgY29uc3Qgc2VhcmNoUXVlcnkgPSBwYXJhbXMucXVlcnkudG9Mb3dlckNhc2UoKVxuICAgICAgZmlsdGVyZWRCb29rcyA9IGZpbHRlcmVkQm9va3MuZmlsdGVyKFxuICAgICAgICBib29rID0+IFxuICAgICAgICAgIGJvb2sudGl0bGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeSkgfHwgXG4gICAgICAgICAgYm9vay5hdXRob3IudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeSlcbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAocGFyYW1zPy5wcmVtaXVtICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGNvbnN0IGZ1bGxCb29rcyA9IG1vY2tCb29rcy5maWx0ZXIoYm9vayA9PiBib29rLmlzUHJlbWl1bSA9PT0gcGFyYW1zLnByZW1pdW0pXG4gICAgICBjb25zdCBwcmVtaXVtQm9va0lkcyA9IGZ1bGxCb29rcy5tYXAoYm9vayA9PiBib29rLmlkKVxuICAgICAgZmlsdGVyZWRCb29rcyA9IGZpbHRlcmVkQm9va3MuZmlsdGVyKGJvb2sgPT4gcHJlbWl1bUJvb2tJZHMuaW5jbHVkZXMoYm9vay5pZCkpXG4gICAgfVxuXG4gICAgaWYgKHBhcmFtcz8ubGltaXQpIHtcbiAgICAgIGZpbHRlcmVkQm9va3MgPSBmaWx0ZXJlZEJvb2tzLnNsaWNlKDAsIHBhcmFtcy5saW1pdClcbiAgICB9XG5cbiAgICByZXR1cm4gZmlsdGVyZWRCb29rc1xuICB9XG5cbiAgc3RhdGljIGFzeW5jIGdldEJvb2tCeUlkKGlkOiBzdHJpbmcpOiBQcm9taXNlPEJvb2s+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcbiAgICBcbiAgICBjb25zdCBib29rID0gbW9ja0Jvb2tzLmZpbmQoYm9vayA9PiBib29rLmlkID09PSBpZClcbiAgICBpZiAoIWJvb2spIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgQm9vayB3aXRoIGlkICR7aWR9IG5vdCBmb3VuZGApXG4gICAgfVxuICAgIFxuICAgIHJldHVybiBib29rXG4gIH1cblxuICAvLyBGcmVlIEJvb2tzIEFQSSBtZXRob2RzXG4gIHN0YXRpYyBhc3luYyBnZXRGcmVlQm9va3MocGFyYW1zPzoge1xuICAgIGNhdGVnb3J5Pzogc3RyaW5nXG4gICAgcXVlcnk/OiBzdHJpbmdcbiAgICBzb3J0Qnk/OiBcInRpdGxlXCIgfCBcInJhdGluZ1wiIHwgXCJhdXRob3JcIlxuICAgIGxpbWl0PzogbnVtYmVyXG4gIH0pOiBQcm9taXNlPEJvb2tQcmV2aWV3W10+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcblxuICAgIGxldCBmcmVlQm9va3MgPSBtb2NrQm9va1ByZXZpZXdzLmZpbHRlcihib29rID0+IHtcbiAgICAgIGNvbnN0IGZ1bGxCb29rID0gbW9ja0Jvb2tzLmZpbmQoYiA9PiBiLmlkID09PSBib29rLmlkKVxuICAgICAgcmV0dXJuIGZ1bGxCb29rPy5pc0ZyZWUgfHwgZnVsbEJvb2s/LnByaWNlID09PSAwXG4gICAgfSlcblxuICAgIGlmIChwYXJhbXM/LmNhdGVnb3J5ICYmIHBhcmFtcy5jYXRlZ29yeSAhPT0gXCJBbGxcIikge1xuICAgICAgZnJlZUJvb2tzID0gZnJlZUJvb2tzLmZpbHRlcihcbiAgICAgICAgYm9vayA9PiBib29rLmNhdGVnb3J5LnRvTG93ZXJDYXNlKCkgPT09IHBhcmFtcy5jYXRlZ29yeSEudG9Mb3dlckNhc2UoKVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmIChwYXJhbXM/LnF1ZXJ5KSB7XG4gICAgICBjb25zdCBzZWFyY2hRdWVyeSA9IHBhcmFtcy5xdWVyeS50b0xvd2VyQ2FzZSgpXG4gICAgICBmcmVlQm9va3MgPSBmcmVlQm9va3MuZmlsdGVyKFxuICAgICAgICBib29rID0+XG4gICAgICAgICAgYm9vay50aXRsZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5KSB8fFxuICAgICAgICAgIGJvb2suYXV0aG9yLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkpIHx8XG4gICAgICAgICAgYm9vay5jYXRlZ29yeS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5KVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmIChwYXJhbXM/LnNvcnRCeSkge1xuICAgICAgZnJlZUJvb2tzLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgICAgc3dpdGNoIChwYXJhbXMuc29ydEJ5KSB7XG4gICAgICAgICAgY2FzZSBcInJhdGluZ1wiOlxuICAgICAgICAgICAgcmV0dXJuIGIucmF0aW5nIC0gYS5yYXRpbmdcbiAgICAgICAgICBjYXNlIFwiYXV0aG9yXCI6XG4gICAgICAgICAgICByZXR1cm4gYS5hdXRob3IubG9jYWxlQ29tcGFyZShiLmF1dGhvcilcbiAgICAgICAgICBjYXNlIFwidGl0bGVcIjpcbiAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgcmV0dXJuIGEudGl0bGUubG9jYWxlQ29tcGFyZShiLnRpdGxlKVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH1cblxuICAgIGlmIChwYXJhbXM/LmxpbWl0KSB7XG4gICAgICBmcmVlQm9va3MgPSBmcmVlQm9va3Muc2xpY2UoMCwgcGFyYW1zLmxpbWl0KVxuICAgIH1cblxuICAgIHJldHVybiBmcmVlQm9va3NcbiAgfVxuXG4gIC8vIEJsb2cgQVBJIG1ldGhvZHNcbiAgc3RhdGljIGFzeW5jIGdldEJsb2dQb3N0cyhwYXJhbXM/OiB7XG4gICAgY2F0ZWdvcnk/OiBzdHJpbmdcbiAgICBxdWVyeT86IHN0cmluZ1xuICAgIGxpbWl0PzogbnVtYmVyXG4gIH0pOiBQcm9taXNlPEJsb2dQb3N0W10+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcblxuICAgIGxldCBmaWx0ZXJlZFBvc3RzID0gWy4uLm1vY2tCbG9nUG9zdHNdXG5cbiAgICBpZiAocGFyYW1zPy5jYXRlZ29yeSAmJiBwYXJhbXMuY2F0ZWdvcnkgIT09IFwiQWxsXCIpIHtcbiAgICAgIGZpbHRlcmVkUG9zdHMgPSBmaWx0ZXJlZFBvc3RzLmZpbHRlcihcbiAgICAgICAgcG9zdCA9PiBwb3N0LmNhdGVnb3J5LnRvTG93ZXJDYXNlKCkgPT09IHBhcmFtcy5jYXRlZ29yeSEudG9Mb3dlckNhc2UoKVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmIChwYXJhbXM/LnF1ZXJ5KSB7XG4gICAgICBjb25zdCBzZWFyY2hRdWVyeSA9IHBhcmFtcy5xdWVyeS50b0xvd2VyQ2FzZSgpXG4gICAgICBmaWx0ZXJlZFBvc3RzID0gZmlsdGVyZWRQb3N0cy5maWx0ZXIoXG4gICAgICAgIHBvc3QgPT5cbiAgICAgICAgICBwb3N0LnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkpIHx8XG4gICAgICAgICAgcG9zdC5leGNlcnB0LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkpIHx8XG4gICAgICAgICAgcG9zdC5hdXRob3IudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeSlcbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAocGFyYW1zPy5saW1pdCkge1xuICAgICAgZmlsdGVyZWRQb3N0cyA9IGZpbHRlcmVkUG9zdHMuc2xpY2UoMCwgcGFyYW1zLmxpbWl0KVxuICAgIH1cblxuICAgIHJldHVybiBmaWx0ZXJlZFBvc3RzXG4gIH1cblxuICBzdGF0aWMgYXN5bmMgZ2V0QmxvZ1Bvc3RCeVNsdWcoc2x1Zzogc3RyaW5nKTogUHJvbWlzZTxCbG9nUG9zdD4ge1xuICAgIGF3YWl0IHRoaXMuZGVsYXkoKVxuXG4gICAgY29uc3QgcG9zdCA9IG1vY2tCbG9nUG9zdHMuZmluZChwb3N0ID0+IHBvc3Quc2x1ZyA9PT0gc2x1ZylcbiAgICBpZiAoIXBvc3QpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgQmxvZyBwb3N0IHdpdGggc2x1ZyAke3NsdWd9IG5vdCBmb3VuZGApXG4gICAgfVxuXG4gICAgcmV0dXJuIHBvc3RcbiAgfVxuXG4gIHN0YXRpYyBhc3luYyBnZXRSZWxhdGVkQmxvZ1Bvc3RzKGN1cnJlbnRTbHVnOiBzdHJpbmcsIGxpbWl0OiBudW1iZXIgPSAzKTogUHJvbWlzZTxCbG9nUG9zdFtdPiB7XG4gICAgYXdhaXQgdGhpcy5kZWxheSgpXG5cbiAgICBjb25zdCBjdXJyZW50UG9zdCA9IG1vY2tCbG9nUG9zdHMuZmluZChwb3N0ID0+IHBvc3Quc2x1ZyA9PT0gY3VycmVudFNsdWcpXG4gICAgaWYgKCFjdXJyZW50UG9zdCkgcmV0dXJuIFtdXG5cbiAgICByZXR1cm4gbW9ja0Jsb2dQb3N0c1xuICAgICAgLmZpbHRlcihwb3N0ID0+IHBvc3Quc2x1ZyAhPT0gY3VycmVudFNsdWcgJiYgcG9zdC5jYXRlZ29yeSA9PT0gY3VycmVudFBvc3QuY2F0ZWdvcnkpXG4gICAgICAuc2xpY2UoMCwgbGltaXQpXG4gIH1cblxuICAvLyBCdXNpbmVzcyBQbGFucyBBUEkgbWV0aG9kc1xuICBzdGF0aWMgYXN5bmMgZ2V0QnVzaW5lc3NQbGFucyhwYXJhbXM/OiB7XG4gICAgc2l6ZT86IHN0cmluZ1xuICAgIGNhdGVnb3J5Pzogc3RyaW5nXG4gICAgcXVlcnk/OiBzdHJpbmdcbiAgICBwcmVtaXVtPzogYm9vbGVhblxuICB9KTogUHJvbWlzZTxCdXNpbmVzc1BsYW5QcmV2aWV3W10+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcbiAgICBcbiAgICBsZXQgZmlsdGVyZWRQbGFucyA9IFsuLi5tb2NrQnVzaW5lc3NQbGFuUHJldmlld3NdXG5cbiAgICBpZiAocGFyYW1zPy5zaXplICYmIHBhcmFtcy5zaXplICE9PSBcImFsbFwiKSB7XG4gICAgICBmaWx0ZXJlZFBsYW5zID0gZmlsdGVyZWRQbGFucy5maWx0ZXIocGxhbiA9PiBwbGFuLnNpemUgPT09IHBhcmFtcy5zaXplKVxuICAgIH1cblxuICAgIGlmIChwYXJhbXM/LmNhdGVnb3J5ICYmIHBhcmFtcy5jYXRlZ29yeSAhPT0gXCJhbGxcIikge1xuICAgICAgZmlsdGVyZWRQbGFucyA9IGZpbHRlcmVkUGxhbnMuZmlsdGVyKFxuICAgICAgICBwbGFuID0+IHBsYW4uY2F0ZWdvcnkudG9Mb3dlckNhc2UoKSA9PT0gcGFyYW1zLmNhdGVnb3J5IS50b0xvd2VyQ2FzZSgpXG4gICAgICApXG4gICAgfVxuXG4gICAgaWYgKHBhcmFtcz8ucXVlcnkpIHtcbiAgICAgIGNvbnN0IHNlYXJjaFF1ZXJ5ID0gcGFyYW1zLnF1ZXJ5LnRvTG93ZXJDYXNlKClcbiAgICAgIGZpbHRlcmVkUGxhbnMgPSBmaWx0ZXJlZFBsYW5zLmZpbHRlcihcbiAgICAgICAgcGxhbiA9PiBcbiAgICAgICAgICBwbGFuLnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkpIHx8IFxuICAgICAgICAgIHBsYW4uY2F0ZWdvcnkudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeSlcbiAgICAgIClcbiAgICB9XG5cbiAgICBpZiAocGFyYW1zPy5wcmVtaXVtICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGZpbHRlcmVkUGxhbnMgPSBmaWx0ZXJlZFBsYW5zLmZpbHRlcihwbGFuID0+IHBsYW4uaXNQcmVtaXVtID09PSBwYXJhbXMucHJlbWl1bSlcbiAgICB9XG5cbiAgICByZXR1cm4gZmlsdGVyZWRQbGFuc1xuICB9XG5cbiAgc3RhdGljIGFzeW5jIGdldEJ1c2luZXNzUGxhbkJ5SWQoaWQ6IHN0cmluZyk6IFByb21pc2U8QnVzaW5lc3NQbGFuPiB7XG4gICAgYXdhaXQgdGhpcy5kZWxheSgpXG4gICAgXG4gICAgY29uc3QgcGxhbiA9IG1vY2tCdXNpbmVzc1BsYW5zLmZpbmQocGxhbiA9PiBwbGFuLmlkID09PSBpZClcbiAgICBpZiAoIXBsYW4pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgQnVzaW5lc3MgcGxhbiB3aXRoIGlkICR7aWR9IG5vdCBmb3VuZGApXG4gICAgfVxuICAgIFxuICAgIHJldHVybiBwbGFuXG4gIH1cblxuICAvLyBBdXRoZW50aWNhdGlvbiBBUEkgbWV0aG9kc1xuICBzdGF0aWMgYXN5bmMgbG9naW4oY3JlZGVudGlhbHM6IHtcbiAgICBlbWFpbDogc3RyaW5nXG4gICAgcGFzc3dvcmQ6IHN0cmluZ1xuICB9KTogUHJvbWlzZTx7XG4gICAgaWQ6IHN0cmluZ1xuICAgIGZpcnN0TmFtZTogc3RyaW5nXG4gICAgbGFzdE5hbWU6IHN0cmluZ1xuICAgIGVtYWlsOiBzdHJpbmdcbiAgICBwbGFuOiBzdHJpbmdcbiAgICB0b2tlbj86IHN0cmluZ1xuICB9PiB7XG4gICAgYXdhaXQgdGhpcy5kZWxheSgpXG4gICAgXG4gICAgLy8gTW9jayBzdWNjZXNzZnVsIGxvZ2luIGZvciBkZW1vIHB1cnBvc2VzXG4gICAgcmV0dXJuIHtcbiAgICAgIGlkOiBcInVzZXItMVwiLFxuICAgICAgZmlyc3ROYW1lOiBcIkpvaG5cIixcbiAgICAgIGxhc3ROYW1lOiBcIkRvZVwiLFxuICAgICAgZW1haWw6IGNyZWRlbnRpYWxzLmVtYWlsLFxuICAgICAgcGxhbjogXCJtZWRpdW1cIixcbiAgICAgIHRva2VuOiBcIm1vY2stand0LXRva2VuLTEyM1wiXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIGFzeW5jIHJlZ2lzdGVyKHVzZXJEYXRhOiB7XG4gICAgZmlyc3ROYW1lOiBzdHJpbmdcbiAgICBsYXN0TmFtZTogc3RyaW5nXG4gICAgZW1haWw6IHN0cmluZ1xuICAgIHBhc3N3b3JkOiBzdHJpbmdcbiAgfSk6IFByb21pc2U8e1xuICAgIGlkOiBzdHJpbmdcbiAgICBmaXJzdE5hbWU6IHN0cmluZ1xuICAgIGxhc3ROYW1lOiBzdHJpbmdcbiAgICBlbWFpbDogc3RyaW5nXG4gICAgcGxhbjogc3RyaW5nXG4gICAgdG9rZW4/OiBzdHJpbmdcbiAgfT4ge1xuICAgIGF3YWl0IHRoaXMuZGVsYXkoKVxuICAgIFxuICAgIC8vIE1vY2sgc3VjY2Vzc2Z1bCByZWdpc3RyYXRpb25cbiAgICByZXR1cm4ge1xuICAgICAgaWQ6IGB1c2VyLSR7RGF0ZS5ub3coKX1gLFxuICAgICAgZmlyc3ROYW1lOiB1c2VyRGF0YS5maXJzdE5hbWUsXG4gICAgICBsYXN0TmFtZTogdXNlckRhdGEubGFzdE5hbWUsXG4gICAgICBlbWFpbDogdXNlckRhdGEuZW1haWwsXG4gICAgICBwbGFuOiBcImJhc2VcIixcbiAgICAgIHRva2VuOiBcIm1vY2stand0LXRva2VuLTQ1NlwiXG4gICAgfVxuICB9XG5cbiAgLy8gVXNlciBBUEkgbWV0aG9kc1xuICBzdGF0aWMgYXN5bmMgZ2V0VXNlclByb2ZpbGUodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPHtcbiAgICB1c2VyOiB7XG4gICAgICBpZDogc3RyaW5nXG4gICAgICBmaXJzdE5hbWU6IHN0cmluZ1xuICAgICAgbGFzdE5hbWU6IHN0cmluZ1xuICAgICAgZW1haWw6IHN0cmluZ1xuICAgICAgcGxhbjogc3RyaW5nXG4gICAgfVxuICAgIGFjdGl2aXRpZXM6IEFycmF5PHtcbiAgICAgIGlkOiBzdHJpbmdcbiAgICAgIHVzZXJJZDogc3RyaW5nXG4gICAgICB0eXBlOiBzdHJpbmdcbiAgICAgIGl0ZW1JZDogc3RyaW5nXG4gICAgICBpdGVtVGl0bGU6IHN0cmluZ1xuICAgICAgdGltZXN0YW1wOiBzdHJpbmdcbiAgICB9PlxuICB9PiB7XG4gICAgYXdhaXQgdGhpcy5kZWxheSgpXG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIHVzZXI6IHtcbiAgICAgICAgaWQ6IHVzZXJJZCxcbiAgICAgICAgZmlyc3ROYW1lOiBcIkpvaG5cIixcbiAgICAgICAgbGFzdE5hbWU6IFwiRG9lXCIsXG4gICAgICAgIGVtYWlsOiBcImpvaG4uZG9lQGV4YW1wbGUuY29tXCIsXG4gICAgICAgIHBsYW46IFwibWVkaXVtXCJcbiAgICAgIH0sXG4gICAgICBhY3Rpdml0aWVzOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogXCJhY3Rpdml0eS0xXCIsXG4gICAgICAgICAgdXNlcklkOiB1c2VySWQsXG4gICAgICAgICAgdHlwZTogXCJib29rX3JlYWRcIixcbiAgICAgICAgICBpdGVtSWQ6IFwidGhlLXBzeWNob2xvZ3ktb2YtbW9uZXlcIixcbiAgICAgICAgICBpdGVtVGl0bGU6IFwiVGhlIFBzeWNob2xvZ3kgb2YgTW9uZXlcIixcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSA4NjQwMDAwMCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6IFwiYWN0aXZpdHktMlwiLFxuICAgICAgICAgIHVzZXJJZDogdXNlcklkLFxuICAgICAgICAgIHR5cGU6IFwicGxhbl92aWV3ZWRcIixcbiAgICAgICAgICBpdGVtSWQ6IFwic21hbGwtMVwiLFxuICAgICAgICAgIGl0ZW1UaXRsZTogXCJMb2NhbCBDb2ZmZWUgU2hvcFwiLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDE3MjgwMDAwMCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9XG4gICAgICBdXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIGFzeW5jIHVwZGF0ZVVzZXJQcm9maWxlKHVzZXJJZDogc3RyaW5nLCB1c2VyRGF0YToge1xuICAgIGZpcnN0TmFtZT86IHN0cmluZ1xuICAgIGxhc3ROYW1lPzogc3RyaW5nXG4gICAgZW1haWw/OiBzdHJpbmdcbiAgfSk6IFByb21pc2U8e1xuICAgIGlkOiBzdHJpbmdcbiAgICBmaXJzdE5hbWU6IHN0cmluZ1xuICAgIGxhc3ROYW1lOiBzdHJpbmdcbiAgICBlbWFpbDogc3RyaW5nXG4gICAgcGxhbjogc3RyaW5nXG4gIH0+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgaWQ6IHVzZXJJZCxcbiAgICAgIGZpcnN0TmFtZTogdXNlckRhdGEuZmlyc3ROYW1lIHx8IFwiSm9oblwiLFxuICAgICAgbGFzdE5hbWU6IHVzZXJEYXRhLmxhc3ROYW1lIHx8IFwiRG9lXCIsXG4gICAgICBlbWFpbDogdXNlckRhdGEuZW1haWwgfHwgXCJqb2huLmRvZUBleGFtcGxlLmNvbVwiLFxuICAgICAgcGxhbjogXCJtZWRpdW1cIlxuICAgIH1cbiAgfVxuXG4gIC8vIFBheW1lbnQgQVBJIG1ldGhvZHNcbiAgc3RhdGljIGFzeW5jIHByb2Nlc3NQYXltZW50KHBheW1lbnREYXRhOiB7XG4gICAgdXNlcklkOiBzdHJpbmdcbiAgICBhbW91bnQ6IG51bWJlclxuICAgIHBsYW46IHN0cmluZ1xuICAgIHBheW1lbnRNZXRob2Q6IHN0cmluZ1xuICAgIHBheW1lbnREZXRhaWxzOiB7XG4gICAgICBwaG9uZU51bWJlcj86IHN0cmluZ1xuICAgICAgYWNjb3VudE51bWJlcj86IHN0cmluZ1xuICAgICAgdHJhbnNhY3Rpb25JZD86IHN0cmluZ1xuICAgIH1cbiAgfSk6IFByb21pc2U8e1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW5cbiAgICB0cmFuc2FjdGlvbklkOiBzdHJpbmdcbiAgICBtZXNzYWdlOiBzdHJpbmdcbiAgICB0aW1lc3RhbXA6IHN0cmluZ1xuICAgIHBsYW46IHN0cmluZ1xuICB9PiB7XG4gICAgYXdhaXQgdGhpcy5kZWxheSgxMDAwKSAvLyBMb25nZXIgZGVsYXkgZm9yIHBheW1lbnQgcHJvY2Vzc2luZ1xuICAgIFxuICAgIC8vIE1vY2sgc3VjY2Vzc2Z1bCBwYXltZW50XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICB0cmFuc2FjdGlvbklkOiBgVFJYLSR7RGF0ZS5ub3coKX0tJHtNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMDAwKX1gLFxuICAgICAgbWVzc2FnZTogXCJQYXltZW50IHByb2Nlc3NlZCBzdWNjZXNzZnVsbHlcIixcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgcGxhbjogcGF5bWVudERhdGEucGxhblxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBhc3luYyB2ZXJpZnlQYXltZW50KHRyYW5zYWN0aW9uSWQ6IHN0cmluZyk6IFByb21pc2U8e1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW5cbiAgICBzdGF0dXM6IHN0cmluZ1xuICAgIG1lc3NhZ2U6IHN0cmluZ1xuICB9PiB7XG4gICAgYXdhaXQgdGhpcy5kZWxheSgpXG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBzdGF0dXM6IFwiY29tcGxldGVkXCIsXG4gICAgICBtZXNzYWdlOiBcIlBheW1lbnQgdmVyaWZpZWQgc3VjY2Vzc2Z1bGx5XCJcbiAgICB9XG4gIH1cblxuICBzdGF0aWMgYXN5bmMgZ2V0UGF5bWVudEhpc3RvcnkodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPEFycmF5PHtcbiAgICBzdWNjZXNzOiBib29sZWFuXG4gICAgdHJhbnNhY3Rpb25JZDogc3RyaW5nXG4gICAgbWVzc2FnZTogc3RyaW5nXG4gICAgdGltZXN0YW1wOiBzdHJpbmdcbiAgICBwbGFuOiBzdHJpbmdcbiAgfT4+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcbiAgICBcbiAgICByZXR1cm4gW1xuICAgICAge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICB0cmFuc2FjdGlvbklkOiBcIlRSWC0xMjM0NTY3ODlcIixcbiAgICAgICAgbWVzc2FnZTogXCJQYXltZW50IHByb2Nlc3NlZCBzdWNjZXNzZnVsbHlcIixcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMjU5MjAwMDAwMCkudG9JU09TdHJpbmcoKSwgLy8gMzAgZGF5cyBhZ29cbiAgICAgICAgcGxhbjogXCJtZWRpdW1cIlxuICAgICAgfVxuICAgIF1cbiAgfVxuXG4gIC8vIEFkbWluIEFQSSBtZXRob2RzXG4gIHN0YXRpYyBhc3luYyBnZXRBZG1pblN0YXRzKCk6IFByb21pc2U8e1xuICAgIHRvdGFsVXNlcnM6IG51bWJlclxuICAgIHRvdGFsQm9va3M6IG51bWJlclxuICAgIHRvdGFsQnVzaW5lc3NQbGFuczogbnVtYmVyXG4gICAgcmV2ZW51ZVRoaXNNb250aDogbnVtYmVyXG4gIH0+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgdG90YWxVc2VyczogMTI1MCxcbiAgICAgIHRvdGFsQm9va3M6IG1vY2tCb29rcy5sZW5ndGgsXG4gICAgICB0b3RhbEJ1c2luZXNzUGxhbnM6IG1vY2tCdXNpbmVzc1BsYW5zLmxlbmd0aCxcbiAgICAgIHJldmVudWVUaGlzTW9udGg6IDE1NzUwXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIGFzeW5jIGdldEFkbWluVXNlcnMocGFyYW1zPzoge1xuICAgIHBhZ2U/OiBudW1iZXJcbiAgICBsaW1pdD86IG51bWJlclxuICAgIHNlYXJjaD86IHN0cmluZ1xuICB9KTogUHJvbWlzZTx7XG4gICAgdXNlcnM6IEFycmF5PHtcbiAgICAgIGlkOiBzdHJpbmdcbiAgICAgIGZpcnN0TmFtZTogc3RyaW5nXG4gICAgICBsYXN0TmFtZTogc3RyaW5nXG4gICAgICBlbWFpbDogc3RyaW5nXG4gICAgICBwbGFuOiBzdHJpbmdcbiAgICAgIGNyZWF0ZWRBdDogc3RyaW5nXG4gICAgfT5cbiAgICB0b3RhbDogbnVtYmVyXG4gICAgcGFnZTogbnVtYmVyXG4gICAgdG90YWxQYWdlczogbnVtYmVyXG4gIH0+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcbiAgICBcbiAgICBjb25zdCBtb2NrVXNlcnMgPSBbXG4gICAgICB7XG4gICAgICAgIGlkOiBcInVzZXItMVwiLFxuICAgICAgICBmaXJzdE5hbWU6IFwiSm9oblwiLFxuICAgICAgICBsYXN0TmFtZTogXCJEb2VcIixcbiAgICAgICAgZW1haWw6IFwiam9obi5kb2VAZXhhbXBsZS5jb21cIixcbiAgICAgICAgcGxhbjogXCJtZWRpdW1cIixcbiAgICAgICAgY3JlYXRlZEF0OiBcIjIwMjMtMDEtMTVUMDA6MDA6MDBaXCJcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcInVzZXItMlwiLFxuICAgICAgICBmaXJzdE5hbWU6IFwiSmFuZVwiLFxuICAgICAgICBsYXN0TmFtZTogXCJTbWl0aFwiLFxuICAgICAgICBlbWFpbDogXCJqYW5lLnNtaXRoQGV4YW1wbGUuY29tXCIsXG4gICAgICAgIHBsYW46IFwiYmFzZVwiLFxuICAgICAgICBjcmVhdGVkQXQ6IFwiMjAyMy0wMy0yMlQwMDowMDowMFpcIlxuICAgICAgfVxuICAgIF1cblxuICAgIHJldHVybiB7XG4gICAgICB1c2VyczogbW9ja1VzZXJzLFxuICAgICAgdG90YWw6IG1vY2tVc2Vycy5sZW5ndGgsXG4gICAgICBwYWdlOiBwYXJhbXM/LnBhZ2UgfHwgMSxcbiAgICAgIHRvdGFsUGFnZXM6IDFcbiAgICB9XG4gIH1cblxuICAvLyBCb29rbWFya3MgQVBJIG1ldGhvZHNcbiAgc3RhdGljIGFzeW5jIGdldFVzZXJCb29rbWFya3ModXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPEJvb2tQcmV2aWV3W10+IHtcbiAgICBhd2FpdCB0aGlzLmRlbGF5KClcbiAgICBcbiAgICAvLyBSZXR1cm4gZmlyc3QgMiBib29rcyBhcyBib29rbWFya2VkIGZvciBkZW1vXG4gICAgcmV0dXJuIG1vY2tCb29rUHJldmlld3Muc2xpY2UoMCwgMilcbiAgfVxuXG4gIHN0YXRpYyBhc3luYyBhZGRCb29rbWFyayh1c2VySWQ6IHN0cmluZywgYm9va0lkOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbiB9PiB7XG4gICAgYXdhaXQgdGhpcy5kZWxheSgpXG4gICAgXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9XG4gIH1cblxuICBzdGF0aWMgYXN5bmMgcmVtb3ZlQm9va21hcmsodXNlcklkOiBzdHJpbmcsIGJvb2tJZDogc3RyaW5nKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW4gfT4ge1xuICAgIGF3YWl0IHRoaXMuZGVsYXkoKVxuICAgIFxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfVxuICB9XG5cbiAgLy8gQW5hbHl0aWNzIEFQSSBtZXRob2RzXG4gIHN0YXRpYyBhc3luYyBnZXRVc2VyQW5hbHl0aWNzKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTx7XG4gICAgcmVhZGluZ1N0YXRzOiB7XG4gICAgICBib29rc1JlYWQ6IG51bWJlclxuICAgICAgdG90YWxSZWFkaW5nVGltZTogbnVtYmVyXG4gICAgICBhdmVyYWdlUmVhZGluZ1NwZWVkOiBudW1iZXJcbiAgICAgIHN0cmVha0RheXM6IG51bWJlclxuICAgIH1cbiAgICByZWNlbnRBY3Rpdml0eTogQXJyYXk8e1xuICAgICAgaWQ6IHN0cmluZ1xuICAgICAgdHlwZTogc3RyaW5nXG4gICAgICBpdGVtVGl0bGU6IHN0cmluZ1xuICAgICAgdGltZXN0YW1wOiBzdHJpbmdcbiAgICB9PlxuICB9PiB7XG4gICAgYXdhaXQgdGhpcy5kZWxheSgpXG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIHJlYWRpbmdTdGF0czoge1xuICAgICAgICBib29rc1JlYWQ6IDEyLFxuICAgICAgICB0b3RhbFJlYWRpbmdUaW1lOiAyNDAwLCAvLyBtaW51dGVzXG4gICAgICAgIGF2ZXJhZ2VSZWFkaW5nU3BlZWQ6IDI1MCwgLy8gd29yZHMgcGVyIG1pbnV0ZVxuICAgICAgICBzdHJlYWtEYXlzOiA3XG4gICAgICB9LFxuICAgICAgcmVjZW50QWN0aXZpdHk6IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBcImFjdGl2aXR5LTFcIixcbiAgICAgICAgICB0eXBlOiBcImJvb2tfcmVhZFwiLFxuICAgICAgICAgIGl0ZW1UaXRsZTogXCJUaGUgUHN5Y2hvbG9neSBvZiBNb25leVwiLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDg2NDAwMDAwKS50b0lTT1N0cmluZygpXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogXCJhY3Rpdml0eS0yXCIsXG4gICAgICAgICAgdHlwZTogXCJwbGFuX3ZpZXdlZFwiLFxuICAgICAgICAgIGl0ZW1UaXRsZTogXCJMb2NhbCBDb2ZmZWUgU2hvcFwiLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDE3MjgwMDAwMCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9XG4gICAgICBdXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsibW9ja0Jvb2tzIiwibW9ja0Jvb2tQcmV2aWV3cyIsIm1vY2tCdXNpbmVzc1BsYW5zIiwibW9ja0J1c2luZXNzUGxhblByZXZpZXdzIiwibW9ja0Jsb2dQb3N0cyIsIk1vY2tBcGlTZXJ2aWNlIiwiZGVsYXkiLCJtcyIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImdldEJvb2tzIiwicGFyYW1zIiwiZmlsdGVyZWRCb29rcyIsImNhdGVnb3J5IiwiZmlsdGVyIiwiYm9vayIsInRvTG93ZXJDYXNlIiwicXVlcnkiLCJzZWFyY2hRdWVyeSIsInRpdGxlIiwiaW5jbHVkZXMiLCJhdXRob3IiLCJwcmVtaXVtIiwidW5kZWZpbmVkIiwiZnVsbEJvb2tzIiwiaXNQcmVtaXVtIiwicHJlbWl1bUJvb2tJZHMiLCJtYXAiLCJpZCIsImxpbWl0Iiwic2xpY2UiLCJnZXRCb29rQnlJZCIsImZpbmQiLCJFcnJvciIsImdldEZyZWVCb29rcyIsImZyZWVCb29rcyIsImZ1bGxCb29rIiwiYiIsImlzRnJlZSIsInByaWNlIiwic29ydEJ5Iiwic29ydCIsImEiLCJyYXRpbmciLCJsb2NhbGVDb21wYXJlIiwiZ2V0QmxvZ1Bvc3RzIiwiZmlsdGVyZWRQb3N0cyIsInBvc3QiLCJleGNlcnB0IiwiZ2V0QmxvZ1Bvc3RCeVNsdWciLCJzbHVnIiwiZ2V0UmVsYXRlZEJsb2dQb3N0cyIsImN1cnJlbnRTbHVnIiwiY3VycmVudFBvc3QiLCJnZXRCdXNpbmVzc1BsYW5zIiwiZmlsdGVyZWRQbGFucyIsInNpemUiLCJwbGFuIiwiZ2V0QnVzaW5lc3NQbGFuQnlJZCIsImxvZ2luIiwiY3JlZGVudGlhbHMiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsImVtYWlsIiwidG9rZW4iLCJyZWdpc3RlciIsInVzZXJEYXRhIiwiRGF0ZSIsIm5vdyIsImdldFVzZXJQcm9maWxlIiwidXNlcklkIiwidXNlciIsImFjdGl2aXRpZXMiLCJ0eXBlIiwiaXRlbUlkIiwiaXRlbVRpdGxlIiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJ1cGRhdGVVc2VyUHJvZmlsZSIsInByb2Nlc3NQYXltZW50IiwicGF5bWVudERhdGEiLCJzdWNjZXNzIiwidHJhbnNhY3Rpb25JZCIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsIm1lc3NhZ2UiLCJ2ZXJpZnlQYXltZW50Iiwic3RhdHVzIiwiZ2V0UGF5bWVudEhpc3RvcnkiLCJnZXRBZG1pblN0YXRzIiwidG90YWxVc2VycyIsInRvdGFsQm9va3MiLCJsZW5ndGgiLCJ0b3RhbEJ1c2luZXNzUGxhbnMiLCJyZXZlbnVlVGhpc01vbnRoIiwiZ2V0QWRtaW5Vc2VycyIsIm1vY2tVc2VycyIsImNyZWF0ZWRBdCIsInVzZXJzIiwidG90YWwiLCJwYWdlIiwidG90YWxQYWdlcyIsImdldFVzZXJCb29rbWFya3MiLCJhZGRCb29rbWFyayIsImJvb2tJZCIsInJlbW92ZUJvb2ttYXJrIiwiZ2V0VXNlckFuYWx5dGljcyIsInJlYWRpbmdTdGF0cyIsImJvb2tzUmVhZCIsInRvdGFsUmVhZGluZ1RpbWUiLCJhdmVyYWdlUmVhZGluZ1NwZWVkIiwic3RyZWFrRGF5cyIsInJlY2VudEFjdGl2aXR5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-api-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/mock-data.ts":
/*!**************************!*\
  !*** ./lib/mock-data.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockBookPreviews: () => (/* binding */ mockBookPreviews),\n/* harmony export */   mockBooks: () => (/* binding */ mockBooks),\n/* harmony export */   mockBusinessPlanPreviews: () => (/* binding */ mockBusinessPlanPreviews),\n/* harmony export */   mockBusinessPlans: () => (/* binding */ mockBusinessPlans)\n/* harmony export */ });\n// Mock data for books - this will be replaced by Django API calls\nconst mockBooks = [\n    {\n        id: \"the-psychology-of-money\",\n        title: \"The Psychology Of Money\",\n        author: \"Morgan Housel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Finance\",\n        rating: 4.4,\n        pages: 242,\n        language: \"English\",\n        price: 12.99,\n        summary: \"\\n      <p>The Psychology of Money explores how money moves around in an economy and how people behave with it. The author, Morgan Housel, provides timeless lessons on wealth, greed, and happiness.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. Financial success is not a hard science</strong></p>\\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\\n      \\n      <p><strong>2. No one is crazy with money</strong></p>\\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\\n      \\n      <p><strong>3. Luck and risk are siblings</strong></p>\\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\\n      \\n      <p><strong>4. Never enough</strong></p>\\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Financial success is not a hard science</strong></p>\\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\\n      \\n      <p><strong>2. No one is crazy with money</strong></p>\\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\\n      \\n      <p><strong>3. Luck and risk are siblings</strong></p>\\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\\n      \\n      <p><strong>4. Never enough</strong></p>\\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Personal Finance:</strong></p>\\n      <ul>\\n        <li>Save money without a specific goal in mind</li>\\n        <li>Gain control over your time</li>\\n        <li>Be reasonable rather than rational</li>\\n        <li>Aim for enough, not for maximum</li>\\n      </ul>\\n      \\n      <p><strong>For Investors:</strong></p>\\n      <ul>\\n        <li>Understand the role of luck and risk</li>\\n        <li>Know that getting wealthy and staying wealthy are different skills</li>\\n        <li>Long tails drive everything - a small number of events can account for the majority of outcomes</li>\\n        <li>Use room for error when investing - prepare for a range of outcomes</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"atomic-habits\",\n        title: \"Atomic Habits\",\n        author: \"James Clear\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Productivity\",\n        rating: 4.9,\n        pages: 320,\n        language: \"English\",\n        price: 13.99,\n        summary: \"\\n      <p>Atomic Habits offers a proven framework for improving every day. James Clear reveals practical strategies that will teach you exactly how to form good habits, break bad ones, and master the tiny behaviors that lead to remarkable results.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\\n      \\n      <p><strong>2. Focus on systems instead of goals</strong></p>\\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\\n      \\n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\\n      \\n      <p><strong>4. Identity-based habits</strong></p>\\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\\n      \\n      <p><strong>2. Focus on systems instead of goals</strong></p>\\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\\n      \\n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\\n      \\n      <p><strong>4. Identity-based habits</strong></p>\\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Personal Development:</strong></p>\\n      <ul>\\n        <li>Start with an incredibly small habit</li>\\n        <li>Increase your habit in very small ways</li>\\n        <li>Break habits into chunks</li>\\n        <li>When you slip, get back on track quickly</li>\\n      </ul>\\n      \\n      <p><strong>For Business:</strong></p>\\n      <ul>\\n        <li>Create an environment where doing the right thing is as easy as possible</li>\\n        <li>Make good habits obvious in your environment</li>\\n        <li>Reduce friction for good habits</li>\\n        <li>Increase friction for bad habits</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"sapiens\",\n        title: \"Sapiens\",\n        author: \"Yuval Noah Harari\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"History\",\n        rating: 4.7,\n        pages: 464,\n        language: \"English\",\n        price: 14.99,\n        summary: '\\n      <p>Sapiens: A Brief History of Humankind is a book by Yuval Noah Harari that explores the history and impact of Homo sapiens on the world. It traces the evolution of our species from the emergence of Homo sapiens in Africa to our current status as the dominant force on Earth.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. The Cognitive Revolution</strong></p>\\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\\n      \\n      <p><strong>2. The Agricultural Revolution</strong></p>\\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history\\'s biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\\n      \\n      <p><strong>3. The Unification of Humankind</strong></p>\\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\\n      \\n      <p><strong>4. The Scientific Revolution</strong></p>\\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\\n    ',\n        keyInsights: '\\n      <p><strong>1. The Cognitive Revolution</strong></p>\\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\\n      \\n      <p><strong>2. The Agricultural Revolution</strong></p>\\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history\\'s biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\\n      \\n      <p><strong>3. The Unification of Humankind</strong></p>\\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\\n      \\n      <p><strong>4. The Scientific Revolution</strong></p>\\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\\n    ',\n        applications: '\\n      <p><strong>For Understanding Society:</strong></p>\\n      <ul>\\n        <li>Recognize how shared myths and stories shape our world</li>\\n        <li>Understand the historical context of current social structures</li>\\n        <li>Question whether \"progress\" always means improvement</li>\\n        <li>Consider the ethical implications of technological advancement</li>\\n      </ul>\\n      \\n      <p><strong>For Business and Leadership:</strong></p>\\n      <ul>\\n        <li>Appreciate how shared narratives create cohesion in organizations</li>\\n        <li>Understand how money and corporations are social constructs that depend on trust</li>\\n        <li>Consider the long-term implications of short-term decisions</li>\\n        <li>Recognize patterns of human behavior that persist across time</li>\\n      </ul>\\n    ',\n        isPremium: true\n    },\n    {\n        id: \"zero-to-one\",\n        title: \"Zero to One\",\n        author: \"Peter Thiel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Entrepreneurship\",\n        rating: 4.8,\n        pages: 224,\n        language: \"English\",\n        price: 12.99,\n        summary: \"\\n      <p>Zero to One presents at once an optimistic view of the future of progress in America and a new way of thinking about innovation: it starts by learning to ask the questions that lead you to find value in unexpected places.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\\n\\n      <p><strong>2. Monopolies vs. Competition</strong></p>\\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\\n\\n      <p><strong>3. The Power Law</strong></p>\\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\\n\\n      <p><strong>4. Secrets</strong></p>\\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\\n\\n      <p><strong>2. Monopolies vs. Competition</strong></p>\\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\\n\\n      <p><strong>3. The Power Law</strong></p>\\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\\n\\n      <p><strong>4. Secrets</strong></p>\\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\\n    \",\n        applications: '\\n      <p><strong>For Entrepreneurs:</strong></p>\\n      <ul>\\n        <li>Focus on creating something new rather than improving existing products</li>\\n        <li>Aim to create a monopoly through unique technology, network effects, economies of scale, and branding</li>\\n        <li>Start small and monopolize a niche market before expanding</li>\\n        <li>Build a great team with a strong, unified vision</li>\\n      </ul>\\n\\n      <p><strong>For Investors:</strong></p>\\n      <ul>\\n        <li>Understand that returns follow a power law—a few investments will outperform all others</li>\\n        <li>Look for companies with proprietary technology, network effects, economies of scale, and strong branding</li>\\n        <li>Evaluate the founding team\\'s dynamics and vision</li>\\n        <li>Consider whether the company has discovered a unique \"secret\" about the market</li>\\n      </ul>\\n    ',\n        isPremium: true\n    },\n    {\n        id: \"good-to-great\",\n        title: \"Good to Great\",\n        author: \"Jim Collins\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Leadership\",\n        rating: 4.7,\n        pages: 320,\n        language: \"English\",\n        price: 11.99,\n        summary: \"\\n      <p>Good to Great presents the findings of a five-year study by Jim Collins and his research team. The team identified a set of companies that made the leap from good results to great results and sustained those results for at least fifteen years.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Level 5 Leadership</strong></p>\\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\\n\\n      <p><strong>2. First Who, Then What</strong></p>\\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\\n\\n      <p><strong>3. Confront the Brutal Facts</strong></p>\\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\\n\\n      <p><strong>4. The Hedgehog Concept</strong></p>\\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Level 5 Leadership</strong></p>\\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\\n\\n      <p><strong>2. First Who, Then What</strong></p>\\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\\n\\n      <p><strong>3. Confront the Brutal Facts</strong></p>\\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\\n\\n      <p><strong>4. The Hedgehog Concept</strong></p>\\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Business Leaders:</strong></p>\\n      <ul>\\n        <li>Develop Level 5 Leadership qualities: ambition for the company over self</li>\\n        <li>Focus on getting the right team in place before determining strategy</li>\\n        <li>Create a culture of disciplined people, thought, and action</li>\\n        <li>Apply the Hedgehog Concept to focus resources and efforts</li>\\n      </ul>\\n\\n      <p><strong>For Organizations:</strong></p>\\n      <ul>\\n        <li>Use technology as an accelerator, not a creator of momentum</li>\\n        <li>Build momentum gradually until breakthrough occurs (the flywheel effect)</li>\\n        <li>Maintain discipline to stick with what you can be best at</li>\\n        <li>Confront reality while maintaining faith in ultimate success</li>\\n      </ul>\\n    \",\n        isPremium: true\n    },\n    {\n        id: \"the-lean-startup\",\n        title: \"The Lean Startup\",\n        author: \"Eric Ries\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Startup\",\n        rating: 4.6,\n        pages: 336,\n        language: \"English\",\n        price: 10.99,\n        summary: \"\\n      <p>The Lean Startup introduces a methodology for developing businesses and products that aims to shorten product development cycles and rapidly discover if a proposed business model is viable.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Build-Measure-Learn</strong></p>\\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\\n\\n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\\n\\n      <p><strong>3. Validated Learning</strong></p>\\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\\n\\n      <p><strong>4. Innovation Accounting</strong></p>\\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Build-Measure-Learn</strong></p>\\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\\n\\n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\\n\\n      <p><strong>3. Validated Learning</strong></p>\\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\\n\\n      <p><strong>4. Innovation Accounting</strong></p>\\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Entrepreneurs:</strong></p>\\n      <ul>\\n        <li>Start with a minimum viable product to test assumptions quickly</li>\\n        <li>Use actionable metrics that demonstrate clear cause and effect</li>\\n        <li>Practice continuous deployment and small batch sizes</li>\\n        <li>Be willing to pivot when necessary based on validated learning</li>\\n      </ul>\\n\\n      <p><strong>For Established Companies:</strong></p>\\n      <ul>\\n        <li>Create innovation teams with appropriate structures and metrics</li>\\n        <li>Allocate resources using innovation accounting</li>\\n        <li>Develop internal entrepreneurship through dedicated teams</li>\\n        <li>Apply lean principles to accelerate product development cycles</li>\\n      </ul>\\n    \",\n        isPremium: true\n    },\n    // Free books\n    {\n        id: \"public-domain-classic-1\",\n        title: \"The Art of War\",\n        author: \"Sun Tzu\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Strategy\",\n        rating: 4.8,\n        pages: 96,\n        language: \"English\",\n        price: 0,\n        summary: \"\\n      <p>The Art of War is an ancient Chinese military treatise dating from the Late Spring and Autumn Period. The work, which is attributed to the ancient Chinese military strategist Sun Tzu, is composed of 13 chapters.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Know Yourself and Your Enemy</strong></p>\\n      <p>If you know the enemy and know yourself, you need not fear the result of a hundred battles.</p>\\n\\n      <p><strong>2. Win Without Fighting</strong></p>\\n      <p>The supreme excellence is to subdue the enemy without fighting.</p>\\n\\n      <p><strong>3. Speed and Timing</strong></p>\\n      <p>Rapidity is the essence of war: take advantage of the enemy's unreadiness, make your way by unexpected routes, and attack unguarded spots.</p>\\n\\n      <p><strong>4. Adaptability</strong></p>\\n      <p>Water shapes its course according to the nature of the ground; the soldier works out his victory in relation to the foe he is facing.</p>\\n    \",\n        isPremium: false,\n        isFree: true\n    },\n    {\n        id: \"public-domain-classic-2\",\n        title: \"Think and Grow Rich\",\n        author: \"Napoleon Hill\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Self-Help\",\n        rating: 4.7,\n        pages: 320,\n        language: \"English\",\n        price: 0,\n        summary: \"\\n      <p>Think and Grow Rich is a personal development and self-help book by Napoleon Hill. The book was inspired by a suggestion from Scottish-American businessman Andrew Carnegie.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Desire</strong></p>\\n      <p>The starting point of all achievement is desire. Keep this constantly in mind. Weak desire brings weak results.</p>\\n\\n      <p><strong>2. Faith</strong></p>\\n      <p>Faith is the head chemist of the mind. When faith is blended with the vibration of thought, the subconscious mind instantly picks up the vibration.</p>\\n\\n      <p><strong>3. Persistence</strong></p>\\n      <p>Persistence is to the character of man as carbon is to steel.</p>\\n\\n      <p><strong>4. The Master Mind</strong></p>\\n      <p>The coordination of knowledge and effort of two or more people, who work toward a definite purpose, in the spirit of harmony.</p>\\n    \",\n        isPremium: false,\n        isFree: true\n    },\n    {\n        id: \"free-business-guide\",\n        title: \"Starting Your First Business\",\n        author: \"Astewai Team\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Entrepreneurship\",\n        rating: 4.5,\n        pages: 150,\n        language: \"English\",\n        price: 0,\n        summary: \"\\n      <p>A comprehensive guide for first-time entrepreneurs, covering everything from idea validation to launching your business.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Idea Validation</strong></p>\\n      <p>Before investing time and money, validate your business idea with potential customers.</p>\\n\\n      <p><strong>2. Market Research</strong></p>\\n      <p>Understanding your target market is crucial for business success.</p>\\n\\n      <p><strong>3. Financial Planning</strong></p>\\n      <p>Create realistic financial projections and understand your funding needs.</p>\\n\\n      <p><strong>4. Legal Structure</strong></p>\\n      <p>Choose the right business structure for your specific situation.</p>\\n    \",\n        isPremium: false,\n        isFree: true\n    }\n];\n// Convert to BookPreview format for listing pages\nconst mockBookPreviews = mockBooks.map((book)=>({\n        id: book.id,\n        title: book.title,\n        author: book.author,\n        coverUrl: book.coverUrl,\n        category: book.category,\n        rating: book.rating\n    }));\nconst mockBusinessPlans = [\n    {\n        id: \"small-1\",\n        title: \"Local Coffee Shop\",\n        category: \"Food & Beverage\",\n        size: \"small\",\n        description: \"A comprehensive business plan for starting and operating a successful local coffee shop.\",\n        overview: \"\\n      <h3>Executive Summary</h3>\\n      <p>This business plan outlines the establishment of a cozy, community-focused coffee shop that serves premium coffee and light food items. The shop will be located in a high-traffic area with significant foot traffic and limited direct competition.</p>\\n\\n      <h3>Business Concept</h3>\\n      <ul>\\n        <li>Premium coffee and espresso drinks</li>\\n        <li>Fresh pastries and light meals</li>\\n        <li>Comfortable seating and free Wi-Fi</li>\\n        <li>Focus on sustainable practices</li>\\n      </ul>\\n\\n      <h3>Target Market</h3>\\n      <ul>\\n        <li>Young professionals (25-40)</li>\\n        <li>College students</li>\\n        <li>Remote workers</li>\\n        <li>Local residents</li>\\n      </ul>\\n    \",\n        marketAnalysis: \"\\n      <h3>Market Overview</h3>\\n      <p>The coffee shop industry continues to grow, with increasing demand for premium coffee experiences. Key market trends include:</p>\\n      <ul>\\n        <li>Growing preference for specialty coffee</li>\\n        <li>Increased focus on sustainability</li>\\n        <li>Rising demand for plant-based options</li>\\n        <li>Need for comfortable workspaces</li>\\n      </ul>\\n\\n      <h3>Competitive Analysis</h3>\\n      <p>Local competition includes:</p>\\n      <ul>\\n        <li>Chain coffee shops (2 within 1km)</li>\\n        <li>Independent cafes (1 within 1km)</li>\\n        <li>Restaurants serving coffee</li>\\n      </ul>\\n\\n      <h3>Competitive Advantage</h3>\\n      <ul>\\n        <li>Premium quality coffee</li>\\n        <li>Comfortable atmosphere</li>\\n        <li>Excellent customer service</li>\\n        <li>Strategic location</li>\\n      </ul>\\n    \",\n        financials: \"\\n      <h3>Startup Costs</h3>\\n      <ul>\\n        <li>Lease deposit and improvements: $25,000</li>\\n        <li>Equipment: $35,000</li>\\n        <li>Initial inventory: $5,000</li>\\n        <li>Licenses and permits: $2,000</li>\\n        <li>Working capital: $20,000</li>\\n      </ul>\\n\\n      <h3>Financial Projections</h3>\\n      <p>Year 1:</p>\\n      <ul>\\n        <li>Revenue: $300,000</li>\\n        <li>Expenses: $270,000</li>\\n        <li>Net profit: $30,000</li>\\n      </ul>\\n\\n      <p>Year 2:</p>\\n      <ul>\\n        <li>Revenue: $400,000</li>\\n        <li>Expenses: $340,000</li>\\n        <li>Net profit: $60,000</li>\\n      </ul>\\n    \",\n        implementation: \"\\n      <h3>Timeline</h3>\\n      <ul>\\n        <li>Month 1-2: Location selection and lease signing</li>\\n        <li>Month 2-3: Design and permits</li>\\n        <li>Month 3-4: Construction and equipment installation</li>\\n        <li>Month 4: Staff hiring and training</li>\\n        <li>Month 5: Soft opening and marketing</li>\\n        <li>Month 6: Grand opening</li>\\n      </ul>\\n\\n      <h3>Marketing Strategy</h3>\\n      <ul>\\n        <li>Social media presence</li>\\n        <li>Local partnerships</li>\\n        <li>Loyalty program</li>\\n        <li>Community events</li>\\n      </ul>\\n\\n      <h3>Risk Mitigation</h3>\\n      <ul>\\n        <li>Comprehensive insurance coverage</li>\\n        <li>Diverse supplier relationships</li>\\n        <li>Staff training programs</li>\\n        <li>Cash flow management</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"small-2\",\n        title: \"Freelance Web Development\",\n        category: \"Technology\",\n        size: \"small\",\n        description: \"A detailed business plan for starting and growing a freelance web development business.\",\n        overview: \"\\n      <h3>Executive Summary</h3>\\n      <p>This business plan outlines the establishment of a freelance web development business focused on creating custom websites and web applications for small to medium-sized businesses. The business will operate remotely with minimal overhead costs.</p>\\n\\n      <h3>Business Concept</h3>\\n      <ul>\\n        <li>Custom website development</li>\\n        <li>Web application development</li>\\n        <li>Website maintenance and support</li>\\n        <li>SEO and digital marketing services</li>\\n      </ul>\\n\\n      <h3>Target Market</h3>\\n      <ul>\\n        <li>Small businesses needing online presence</li>\\n        <li>Medium-sized companies requiring web applications</li>\\n        <li>Startups with limited budgets</li>\\n        <li>Non-profit organizations</li>\\n      </ul>\\n    \",\n        marketAnalysis: \"\\n      <h3>Market Overview</h3>\\n      <p>The web development industry continues to grow as businesses increasingly recognize the importance of online presence. Key market trends include:</p>\\n      <ul>\\n        <li>Increasing demand for mobile-responsive websites</li>\\n        <li>Growing need for e-commerce functionality</li>\\n        <li>Rising importance of user experience (UX) design</li>\\n        <li>Shift toward progressive web applications</li>\\n      </ul>\\n\\n      <h3>Competitive Analysis</h3>\\n      <p>Competition includes:</p>\\n      <ul>\\n        <li>Other freelance developers</li>\\n        <li>Web development agencies</li>\\n        <li>DIY website builders (Wix, Squarespace)</li>\\n      </ul>\\n\\n      <h3>Competitive Advantage</h3>\\n      <ul>\\n        <li>Personalized service and direct client communication</li>\\n        <li>Lower overhead costs than agencies</li>\\n        <li>Specialized expertise in modern frameworks</li>\\n        <li>Flexible pricing models</li>\\n      </ul>\\n    \",\n        financials: \"\\n      <h3>Startup Costs</h3>\\n      <ul>\\n        <li>Computer equipment: $3,000</li>\\n        <li>Software subscriptions: $1,200/year</li>\\n        <li>Website and hosting: $500</li>\\n        <li>Business registration: $300</li>\\n        <li>Initial marketing: $1,000</li>\\n      </ul>\\n\\n      <h3>Financial Projections</h3>\\n      <p>Year 1:</p>\\n      <ul>\\n        <li>Revenue: $60,000</li>\\n        <li>Expenses: $15,000</li>\\n        <li>Net profit: $45,000</li>\\n      </ul>\\n\\n      <p>Year 2:</p>\\n      <ul>\\n        <li>Revenue: $90,000</li>\\n        <li>Expenses: $20,000</li>\\n        <li>Net profit: $70,000</li>\\n      </ul>\\n    \",\n        implementation: \"\\n      <h3>Timeline</h3>\\n      <ul>\\n        <li>Month 1: Business registration and website setup</li>\\n        <li>Month 2: Portfolio development</li>\\n        <li>Month 3: Initial marketing and networking</li>\\n        <li>Month 4-6: Secure first clients and build reputation</li>\\n      </ul>\\n\\n      <h3>Marketing Strategy</h3>\\n      <ul>\\n        <li>Portfolio website showcasing work</li>\\n        <li>Social media presence on LinkedIn and Twitter</li>\\n        <li>Content marketing through blog posts</li>\\n        <li>Networking at local business events</li>\\n        <li>Referral program for existing clients</li>\\n      </ul>\\n\\n      <h3>Risk Mitigation</h3>\\n      <ul>\\n        <li>Diversify client base to avoid dependency</li>\\n        <li>Maintain emergency fund for slow periods</li>\\n        <li>Continuous skill development</li>\\n        <li>Clear contracts and scope definitions</li>\\n      </ul>\\n    \",\n        isPremium: false\n    }\n];\nconst mockBusinessPlanPreviews = mockBusinessPlans.map((plan)=>({\n        id: plan.id,\n        title: plan.title,\n        category: plan.category,\n        size: plan.size,\n        description: plan.description,\n        isPremium: plan.isPremium\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-data.ts\n"));

/***/ })

});