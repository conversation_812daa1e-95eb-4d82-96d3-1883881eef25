{"c": ["app/layout", "app/blog/[slug]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/blog/[slug]/page.tsx", "(app-pages-browser)/./components/ui/separator.tsx", "(app-pages-browser)/./node_modules/@radix-ui/react-separator/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5CDesktop%5C%5CProjects%5C%5CAstewai%5C%5CTilkTebeb%5C%5Ctilktbeb%20(1)%5C%5Capp%5C%5Cblog%5C%5C%5Bslug%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}