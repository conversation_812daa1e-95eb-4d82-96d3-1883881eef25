"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/page",{

/***/ "(app-pages-browser)/./app/books/page.tsx":
/*!****************************!*\
  !*** ./app/books/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BooksPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_book_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/book-card */ \"(app-pages-browser)/./components/book-card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction BooksPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [books, setBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredBooks, setFilteredBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch books from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BooksPage.useEffect\": ()=>{\n            const fetchBooks = {\n                \"BooksPage.useEffect.fetchBooks\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const data = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.api.getBooks();\n                        setBooks(data);\n                        setFilteredBooks(data);\n                    } catch (err) {\n                        setError((0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.handleApiError)(err));\n                        console.error(\"Error fetching books:\", err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BooksPage.useEffect.fetchBooks\"];\n            fetchBooks();\n        }\n    }[\"BooksPage.useEffect\"], []);\n    // Filter books based on search query and active category\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BooksPage.useEffect\": ()=>{\n            let result = [\n                ...books\n            ];\n            if (activeCategory !== \"all\") {\n                result = result.filter({\n                    \"BooksPage.useEffect\": (book)=>book.category.toLowerCase() === activeCategory.toLowerCase()\n                }[\"BooksPage.useEffect\"]);\n            }\n            if (searchQuery) {\n                const query = searchQuery.toLowerCase();\n                result = result.filter({\n                    \"BooksPage.useEffect\": (book)=>book.title.toLowerCase().includes(query) || book.author.toLowerCase().includes(query)\n                }[\"BooksPage.useEffect\"]);\n            }\n            setFilteredBooks(result);\n        }\n    }[\"BooksPage.useEffect\"], [\n        books,\n        searchQuery,\n        activeCategory\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-8 md:py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold tracking-tight mb-2\",\n                                children: \"Digital Books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Discover and purchase premium digital books - secure reading, no downloads\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full md:w-auto flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full sm:w-64\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"search\",\n                                        placeholder: \"Search books...\",\n                                        className: \"w-full pl-8 rounded-full bg-card border-none\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                defaultValue: \"all\",\n                                value: activeCategory,\n                                onValueChange: setActiveCategory,\n                                className: \"w-full sm:w-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                    className: \"bg-muted/50 p-1 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"all\",\n                                            className: \"rounded-full\",\n                                            children: \"All\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"finance\",\n                                            className: \"rounded-full\",\n                                            children: \"Finance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"productivity\",\n                                            className: \"rounded-full\",\n                                            children: \"Productivity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                            value: \"entrepreneurship\",\n                                            className: \"rounded-full\",\n                                            children: \"Entrepreneurship\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                children: [\n                    ...Array(8)\n                ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-[3/4] bg-muted rounded-lg mb-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-3/4 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-muted rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-destructive\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>window.location.reload(),\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                        children: filteredBooks.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_book_card__WEBPACK_IMPORTED_MODULE_5__.BookCard, {\n                                title: book.title,\n                                author: book.author,\n                                coverUrl: book.coverUrl,\n                                category: book.category,\n                                rating: book.rating,\n                                id: book.id,\n                                price: book.price || 9.99,\n                                isPurchased: book.isPurchased || false\n                            }, book.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    filteredBooks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"No books found matching your search criteria.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this),\n                    filteredBooks.length > 0 && filteredBooks.length < books.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"rounded-full\",\n                            onClick: ()=>{\n                                setSearchQuery(\"\");\n                                setActiveCategory(\"all\");\n                            },\n                            children: \"Clear Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this),\n                    filteredBooks.length === books.length && books.length >= 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"rounded-full\",\n                            children: \"Load More\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\page.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(BooksPage, \"GulXTH9Ezd1ngBcfdK0dI60Fwws=\");\n_c = BooksPage;\nvar _c;\n$RefreshReg$(_c, \"BooksPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/books/page.tsx\n"));

/***/ })

});