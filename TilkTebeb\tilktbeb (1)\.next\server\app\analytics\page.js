(()=>{var e={};e.id=745,e.ids=[745],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33811:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>f,pages:()=>u,routeModule:()=>d,tree:()=>s});var n=r(70260),i=r(28203),a=r(25155),o=r.n(a),l=r(67292),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);let s=["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38307)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],u=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\analytics\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},52826:(e,t,r)=>{Promise.resolve().then(r.bind(r,38307))},21282:(e,t,r)=>{Promise.resolve().then(r.bind(r,30581))},30581:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>bp});var n={};r.r(n),r.d(n,{scaleBand:()=>ib,scaleDiverging:()=>function e(){var t=aC(lY()(ay));return t.copy=function(){return lH(t,e())},ih.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=aU(lY()).domain([.1,1,10]);return t.copy=function(){return lH(t,e()).base(t.base())},ih.apply(t,arguments)},scaleDivergingPow:()=>lG,scaleDivergingSqrt:()=>lZ,scaleDivergingSymlog:()=>function e(){var t=aq(lY());return t.copy=function(){return lH(t,e()).constant(t.constant())},ih.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e=+e)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,ah),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,ah):[0,1],aC(n)},scaleImplicit:()=>ig,scaleLinear:()=>function e(){var t=aw();return t.copy=function(){return ab(t,e())},id.apply(t,arguments),aC(t)},scaleLog:()=>function e(){let t=aU(ax()).domain([1,10]);return t.copy=()=>ab(t,e()).base(t.base()),id.apply(t,arguments),t},scaleOrdinal:()=>im,scalePoint:()=>ix,scalePow:()=>aG,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=iN){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(+r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e=+e)?t:n[iD(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t=+t)||r.push(t);return r.sort(iM),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},id.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[iD(a,e,0,i)]:t}function c(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r=+r,n=+n,c()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,c()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},id.apply(aC(l),arguments)},scaleRadial:()=>function e(){var t,r=aw(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(aX(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,ah)).map(aX)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},id.apply(a,arguments),aC(a)},scaleSequential:()=>function e(){var t=aC(lq()(ay));return t.copy=function(){return lH(t,e())},ih.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=aU(lq()).domain([1,10]);return t.copy=function(){return lH(t,e()).base(t.base())},ih.apply(t,arguments)},scaleSequentialPow:()=>lW,scaleSequentialQuantile:()=>function e(){var t=[],r=ay;function n(e){if(null!=e&&!isNaN(e=+e))return r((iD(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r=+r)||t.push(r);return t.sort(iM),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t=+t))){if(t<=0||n<2)return aQ(e);if(t>=1)return aJ(e);var n,i=(n-1)*t,a=Math.floor(i),o=aJ((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?a0:function(e=iM){if(e===iM)return a0;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,c=Math.log(o),s=.5*Math.exp(2*c/3),u=.5*Math.sqrt(c*s*(o-s)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*s/o+u)),d=Math.min(i,Math.floor(r+(o-l)*s/o+u));e(t,r,f,d,a)}let o=t[r],l=n,c=i;for(a1(t,n,r),a(t[i],o)>0&&a1(t,n,i);l<c;){for(a1(t,l,c),++l,--c;0>a(t[l],o);)++l;for(;a(t[c],o)>0;)--c}0===a(t[n],o)?a1(t,n,c):a1(t,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return t})(e,a).subarray(0,a+1));return o+(aQ(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},ih.apply(n,arguments)},scaleSequentialSqrt:()=>lV,scaleSequentialSymlog:()=>function e(){var t=aq(lq());return t.copy=function(){return lH(t,e()).constant(t.constant())},ih.apply(t,arguments)},scaleSqrt:()=>aZ,scaleSymlog:()=>function e(){var t=aq(ax());return t.copy=function(){return ab(t,e()).constant(t.constant())},id.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[iD(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},id.apply(a,arguments)},scaleTime:()=>lF,scaleUtc:()=>lK,tickFormat:()=>aN});var i=r(45512),a=r(58009),o=r(97643),l=r(69193),c=r(87021),s=r(6004),u=r(30830),f="Progress",[d,h]=(0,s.A)(f),[p,y]=d(f),v=a.forwardRef((e,t)=>{var r,n;let{__scopeProgress:a,value:o=null,max:l,getValueLabel:c=b,...s}=e;(l||0===l)&&!O(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let f=O(l)?l:100;null===o||j(o,f)||console.error((n=`${o}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let d=j(o,f)?o:null,h=w(d)?c(d,f):void 0;return(0,i.jsx)(p,{scope:a,value:d,max:f,children:(0,i.jsx)(u.sG.div,{"aria-valuemax":f,"aria-valuemin":0,"aria-valuenow":w(d)?d:void 0,"aria-valuetext":h,role:"progressbar","data-state":x(d,f),"data-value":d??void 0,"data-max":f,...s,ref:t})})});v.displayName=f;var g="ProgressIndicator",m=a.forwardRef((e,t)=>{let{__scopeProgress:r,...n}=e,a=y(g,r);return(0,i.jsx)(u.sG.div,{"data-state":x(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...n,ref:t})});function b(e,t){return`${Math.round(e/t*100)}%`}function x(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function w(e){return"number"==typeof e}function O(e){return w(e)&&!isNaN(e)&&e>0}function j(e,t){return w(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=g;var P=r(59462);let A=a.forwardRef(({className:e,value:t,...r},n)=>(0,i.jsx)(v,{ref:n,className:(0,P.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary/20",e),...r,children:(0,i.jsx)(m,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));A.displayName=v.displayName;var S=r(4269),k=r(41680);let M=(0,k.A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var E=r(4643);let T=(0,k.A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),_=(0,k.A)("ChartNoAxesColumn",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]),N=(0,k.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var C=r(45723);async function D(e,t,r,n=5){try{let i=localStorage.getItem(`user-activities-${e}`)||"[]",a=JSON.parse(i),o=new Set(a.map(e=>e.contentId)),l=new Set(a.filter(e=>"bookmark"===e.action).map(e=>e.contentId)),c=new Map;a.forEach(e=>{let t=e.metadata?.category;t&&c.set(t,(c.get(t)||0)+1)});let s=t.map(e=>{let t=0,r=[];if(o.has(e.id)&&(t-=10,r.push("Already viewed")),l.has(e.id)&&(t+=5,r.push("Similar to bookmarked content")),e.categories&&e.categories.forEach(e=>{let n=c.get(e)||0;t+=n,n>0&&r.push(`Matches interest in ${e}`)}),e.publishedDate){let n=new Date(e.publishedDate),i=new Date;(i.getFullYear()-n.getFullYear())*12+i.getMonth()-n.getMonth()<3&&(t+=3,r.push("Recently published"))}return e.rating&&e.rating>4&&(t+=2,r.push("Highly rated")),{content:e,score:t,reasons:r,type:"book"}}),u=r.map(e=>{let t=0,r=[];if(o.has(e.id)&&(t-=10,r.push("Already viewed")),l.has(e.id)&&(t+=5,r.push("Similar to bookmarked content")),e.categories&&e.categories.forEach(e=>{let n=c.get(e)||0;t+=n,n>0&&r.push(`Matches interest in ${e}`)}),e.createdAt){let n=new Date(e.createdAt),i=new Date;(i.getFullYear()-n.getFullYear())*12+i.getMonth()-n.getMonth()<3&&(t+=3,r.push("Recently created"))}return{content:e,score:t,reasons:r,type:"businessPlan"}});return[...s,...u].sort((e,t)=>t.score-e.score).slice(0,n).map(e=>e.content)}catch(e){return console.error("Error generating recommendations:",e),[]}}function I({userId:e}){let[t,r]=(0,a.useState)(null),[n,l]=(0,a.useState)(!0);return n?(0,i.jsxs)(o.Zp,{className:"w-full",children:[(0,i.jsxs)(o.aR,{className:"pb-2",children:[(0,i.jsxs)(o.ZB,{className:"text-lg flex items-center gap-2",children:[(0,i.jsx)(S.A,{className:"h-5 w-5 text-primary"}),"Reading Statistics"]}),(0,i.jsx)(o.BT,{children:"Loading your reading activity..."})]}),(0,i.jsx)(o.Wu,{className:"pb-2",children:(0,i.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)("div",{className:"w-10 h-10 rounded-full bg-muted animate-pulse"}),(0,i.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,i.jsx)("div",{className:"h-4 bg-muted rounded animate-pulse w-1/3"}),(0,i.jsx)("div",{className:"h-3 bg-muted rounded animate-pulse w-1/2"})]})]},e))})})]}):t?(0,i.jsxs)(o.Zp,{className:"w-full",children:[(0,i.jsxs)(o.aR,{className:"pb-2",children:[(0,i.jsxs)(o.ZB,{className:"text-lg flex items-center gap-2",children:[(0,i.jsx)(S.A,{className:"h-5 w-5 text-primary"}),"Reading Statistics"]}),(0,i.jsx)(o.BT,{children:"Your reading activity and progress"})]}),(0,i.jsxs)(o.Wu,{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("h4",{className:"text-sm font-medium flex items-center gap-1",children:[(0,i.jsx)(M,{className:"h-4 w-4 text-primary"}),"Reading Streak"]}),(0,i.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last read: ",(e=>{if(!e)return"Never";let t=new Date(e),r=new Date,n=new Date(r);return(n.setDate(n.getDate()-1),t.toDateString()===r.toDateString())?"Today":t.toDateString()===n.toDateString()?"Yesterday":t.toLocaleDateString()})(t.lastReadAt)]})]}),(0,i.jsxs)("div",{className:"bg-muted/40 p-4 rounded-lg flex items-center gap-4",children:[(0,i.jsx)("div",{className:"bg-primary/10 rounded-full h-12 w-12 flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-xl font-bold text-primary",children:t.readingStreak})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:0===t.readingStreak?"Start your streak today!":`${t.readingStreak} day${1===t.readingStreak?"":"s"} streak!`}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:0===t.readingStreak?"Read today to begin your streak":"Keep reading daily to maintain your streak"})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"bg-muted/40 p-3 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,i.jsx)(E.A,{className:"h-4 w-4 text-primary"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:"Reading Time"})]}),(0,i.jsx)("p",{className:"text-2xl font-bold",children:(e=>{if(e<60)return`${e} min`;let t=Math.floor(e/60);return`${t}h ${e%60}m`})(t.totalTimeSpent)}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total time spent reading"})]}),(0,i.jsxs)("div",{className:"bg-muted/40 p-3 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,i.jsx)(T,{className:"h-4 w-4 text-primary"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:"Reading Speed"})]}),(0,i.jsx)("p",{className:"text-2xl font-bold",children:t.averageReadingSpeed>0?`${t.averageReadingSpeed} WPM`:"N/A"}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"Average words per minute"})]}),(0,i.jsxs)("div",{className:"bg-muted/40 p-3 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,i.jsx)(S.A,{className:"h-4 w-4 text-primary"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:"Books"})]}),(0,i.jsx)("p",{className:"text-2xl font-bold",children:t.booksStarted}),(0,i.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t.booksCompleted," completed"]})]}),(0,i.jsxs)("div",{className:"bg-muted/40 p-3 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,i.jsx)(_,{className:"h-4 w-4 text-primary"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:"Business Plans"})]}),(0,i.jsx)("p",{className:"text-2xl font-bold",children:t.businessPlansViewed}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"Viewed"})]})]}),t.topCategories.length>0&&(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("h4",{className:"text-sm font-medium flex items-center gap-1",children:[(0,i.jsx)(N,{className:"h-4 w-4 text-primary"}),"Top Categories"]}),(0,i.jsx)("div",{className:"space-y-2",children:t.topCategories.map((e,r)=>(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{children:e.category}),(0,i.jsxs)("span",{className:"text-muted-foreground",children:[e.count," items"]})]}),(0,i.jsx)(A,{value:e.count/t.topCategories[0].count*100,className:"h-2"})]},r))})]})]}),(0,i.jsx)(o.wL,{children:(0,i.jsxs)(c.$,{variant:"outline",className:"w-full",size:"sm",children:[(0,i.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"View Detailed Analytics"]})})]}):(0,i.jsxs)(o.Zp,{className:"w-full",children:[(0,i.jsxs)(o.aR,{className:"pb-2",children:[(0,i.jsxs)(o.ZB,{className:"text-lg flex items-center gap-2",children:[(0,i.jsx)(S.A,{className:"h-5 w-5 text-primary"}),"Reading Statistics"]}),(0,i.jsx)(o.BT,{children:"No reading data available yet"})]}),(0,i.jsx)(o.Wu,{children:(0,i.jsxs)("div",{className:"text-center py-6",children:[(0,i.jsx)(S.A,{className:"h-12 w-12 mx-auto text-muted-foreground mb-3"}),(0,i.jsx)("p",{className:"text-muted-foreground mb-4",children:"Start reading to see your statistics here"}),(0,i.jsx)(c.$,{variant:"outline",size:"sm",children:"Browse Books"})]})})]})}var R=r(77252),L=r(61075);let z=(0,k.A)("Bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]]);var $=r(19473),B=r(46583);let U=(0,k.A)("FlaskConical",[["path",{d:"M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2",key:"pzvekw"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M7 16h10",key:"wp8him"}]]),F=(0,k.A)("BookMarked",[["path",{d:"M10 2v8l3-3 3 3V2",key:"sqw3rj"}],["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]),K=(0,k.A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);var q=r(86235);function H({userId:e}){let[t,r]=(0,a.useState)([]),[n,c]=(0,a.useState)(!0),[s,u]=(0,a.useState)("all"),f=t.filter(e=>"all"===s||("books"===s?"book"===e.contentType:"business-plans"!==s||"businessPlan"===e.contentType)),d=e=>{let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/1e3),n=Math.floor(r/60),i=Math.floor(n/60),a=Math.floor(i/24);return r<60?"just now":n<60?`${n}m ago`:i<24?`${i}h ago`:a<7?`${a}d ago`:t.toLocaleDateString()},h=e=>{switch(e.action){case"view":return"book"===e.contentType?(0,i.jsx)(S.A,{className:"h-4 w-4"}):(0,i.jsx)(L.A,{className:"h-4 w-4"});case"read":default:return(0,i.jsx)(S.A,{className:"h-4 w-4"});case"bookmark":return(0,i.jsx)(z,{className:"h-4 w-4"});case"download":return(0,i.jsx)($.A,{className:"h-4 w-4"});case"complete":return(0,i.jsx)(B.A,{className:"h-4 w-4"});case"quiz_attempt":return(0,i.jsx)(U,{className:"h-4 w-4"});case"flashcard_create":return(0,i.jsx)(F,{className:"h-4 w-4"});case"note_create":return(0,i.jsx)(K,{className:"h-4 w-4"})}},p=e=>{let t="book"===e.contentType?"book":"business plan",r=e.metadata?.title||`a ${t}`;switch(e.action){case"view":return`Viewed ${r}`;case"read":return`Read ${r}`;case"bookmark":return`Bookmarked ${r}`;case"download":return`Downloaded ${r}`;case"complete":return`Completed ${r}`;case"quiz_attempt":return`Attempted quiz for ${r}`;case"flashcard_create":return`Created flashcards for ${r}`;case"note_create":return`Added notes to ${r}`;default:return`Interacted with ${r}`}},y=e=>"book"===e.contentType?(0,i.jsx)(R.E,{variant:"outline",className:"text-xs",children:"Book"}):(0,i.jsx)(R.E,{variant:"outline",className:"text-xs",children:"Business Plan"});if(n)return(0,i.jsxs)(o.Zp,{className:"w-full",children:[(0,i.jsxs)(o.aR,{className:"pb-2",children:[(0,i.jsx)(o.ZB,{className:"text-lg",children:"Activity Timeline"}),(0,i.jsx)(o.BT,{children:"Your recent activity"})]}),(0,i.jsx)(o.Wu,{children:(0,i.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,i.jsx)(q.A,{className:"h-6 w-6 animate-spin text-primary mr-2"}),(0,i.jsx)("span",{children:"Loading activity..."})]})})]});return(0,i.jsxs)(o.Zp,{className:"w-full",children:[(0,i.jsxs)(o.aR,{className:"pb-2",children:[(0,i.jsx)(o.ZB,{className:"text-lg",children:"Activity Timeline"}),(0,i.jsx)(o.BT,{children:"Your recent activity"})]}),(0,i.jsxs)(o.Wu,{children:[(0,i.jsxs)(l.Tabs,{defaultValue:"all",className:"w-full",onValueChange:e=>u(e),children:[(0,i.jsxs)(l.TabsList,{className:"mb-4 grid w-full grid-cols-3",children:[(0,i.jsx)(l.TabsTrigger,{value:"all",children:"All"}),(0,i.jsx)(l.TabsTrigger,{value:"books",children:"Books"}),(0,i.jsx)(l.TabsTrigger,{value:"business-plans",children:"Business Plans"})]}),(0,i.jsx)(l.TabsContent,{value:"all",className:"mt-0",children:v(f)}),(0,i.jsx)(l.TabsContent,{value:"books",className:"mt-0",children:v(f)}),(0,i.jsx)(l.TabsContent,{value:"business-plans",className:"mt-0",children:v(f)})]}),0===t.length&&(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)(S.A,{className:"h-12 w-12 mx-auto text-muted-foreground mb-3"}),(0,i.jsx)("p",{className:"font-medium mb-1",children:"No activity yet"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Your reading and interaction activity will appear here"})]})]})]});function v(e){return 0===e.length?(0,i.jsx)("div",{className:"text-center py-6",children:(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"No activities found"})}):(0,i.jsx)("div",{className:"space-y-4 max-h-[400px] overflow-y-auto pr-2 custom-scrollbar",children:e.map((e,t)=>(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("div",{className:"mt-0.5 h-8 w-8 rounded-full bg-muted flex items-center justify-center",children:h(e)}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"font-medium",children:p(e)}),y(e)]}),(0,i.jsx)("span",{className:"text-xs text-muted-foreground",children:d(e.timestamp)}),e.metadata?.progress&&(0,i.jsxs)("span",{className:"text-xs text-muted-foreground mt-1",children:["Progress: ",e.metadata.progress,"%"]})]})]},t))})}}let W=(0,k.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var V=r(28531),Y=r.n(V);function G({userId:e}){let[t,r]=(0,a.useState)([]),[n,s]=(0,a.useState)(!0),[u,f]=(0,a.useState)("all"),d=async()=>{try{s(!0);let t=await fetch("/api/books"),n=await fetch("/api/business-plans");if(!t.ok||!n.ok)throw Error("Failed to fetch content");let i=await t.json(),a=await n.json(),o=await D(e,i,a,6);r(o)}catch(e){console.error("Error loading recommendations:",e)}finally{s(!1)}},h=t.filter(e=>"all"===u||("books"===u?"author"in e:"business-plans"!==u||"industry"in e)),p=e=>"author"in e,y=e=>p(e)?`/books/${e.id}`:`/business-plans/${e.size}/${e.id}`;if(n)return(0,i.jsxs)(o.Zp,{className:"w-full",children:[(0,i.jsxs)(o.aR,{className:"pb-2",children:[(0,i.jsx)(o.ZB,{className:"text-lg",children:"Recommended for You"}),(0,i.jsx)(o.BT,{children:"Personalized recommendations based on your activity"})]}),(0,i.jsx)(o.Wu,{children:(0,i.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,i.jsx)(q.A,{className:"h-6 w-6 animate-spin text-primary mr-2"}),(0,i.jsx)("span",{children:"Finding recommendations for you..."})]})})]});return(0,i.jsxs)(o.Zp,{className:"w-full",children:[(0,i.jsx)(o.aR,{className:"pb-2",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(o.ZB,{className:"text-lg",children:"Recommended for You"}),(0,i.jsx)(o.BT,{children:"Personalized recommendations based on your activity"})]}),(0,i.jsx)(c.$,{variant:"ghost",size:"icon",onClick:d,title:"Refresh recommendations",children:(0,i.jsx)(W,{className:"h-4 w-4"})})]})}),(0,i.jsxs)(o.Wu,{children:[(0,i.jsxs)(l.Tabs,{defaultValue:"all",className:"w-full",onValueChange:e=>f(e),children:[(0,i.jsxs)(l.TabsList,{className:"mb-4 grid w-full grid-cols-3",children:[(0,i.jsx)(l.TabsTrigger,{value:"all",children:"All"}),(0,i.jsx)(l.TabsTrigger,{value:"books",children:"Books"}),(0,i.jsx)(l.TabsTrigger,{value:"business-plans",children:"Business Plans"})]}),(0,i.jsx)(l.TabsContent,{value:"all",className:"mt-0",children:v(h)}),(0,i.jsx)(l.TabsContent,{value:"books",className:"mt-0",children:v(h)}),(0,i.jsx)(l.TabsContent,{value:"business-plans",className:"mt-0",children:v(h)})]}),0===t.length&&(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)(S.A,{className:"h-12 w-12 mx-auto text-muted-foreground mb-3"}),(0,i.jsx)("p",{className:"font-medium mb-1",children:"No recommendations yet"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Continue reading and interacting with content to get personalized recommendations"})]})]})]});function v(e){return 0===e.length?(0,i.jsx)("div",{className:"text-center py-6",children:(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"No recommendations found"})}):(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.map((e,t)=>(0,i.jsx)(Y(),{href:y(e),className:"block group",children:(0,i.jsx)("div",{className:"border rounded-lg p-3 h-full transition-colors hover:bg-muted/50",children:(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:p(e)?(0,i.jsx)("div",{className:"h-16 w-12 bg-muted rounded flex items-center justify-center",children:(0,i.jsx)(S.A,{className:"h-6 w-6 text-muted-foreground"})}):(0,i.jsx)("div",{className:"h-16 w-12 bg-muted rounded flex items-center justify-center",children:(0,i.jsx)(L.A,{className:"h-6 w-6 text-muted-foreground"})})}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsx)("h4",{className:"font-medium text-sm line-clamp-1 group-hover:text-primary transition-colors",children:e.title}),(0,i.jsx)("div",{className:"text-xs text-muted-foreground mt-1",children:p(e)?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("span",{children:["By ",e.author]}),e.rating&&(0,i.jsxs)("span",{className:"ml-2",children:["★ ",e.rating]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{children:e.industry}),(0,i.jsxs)("span",{className:"ml-2",children:[e.size," size"]})]})}),(0,i.jsx)("p",{className:"text-xs line-clamp-2 mt-1",children:p(e)?e.summary?.replace(/<[^>]*>/g,"").substring(0,100)+"...":e.description.substring(0,100)+"..."})]})]})})},t))})}}var Z=r(87103),X=r(82281),J=r(63085),Q=r.n(J),ee=r(70085),et=r.n(ee),er=e=>0===e?0:e>0?1:-1,en=e=>"number"==typeof e&&e!=+e,ei=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,ea=e=>("number"==typeof e||e instanceof Number)&&!en(e),eo=e=>ea(e)||"string"==typeof e,el=0,ec=e=>{var t=++el;return"".concat(e||"").concat(t)},es=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!ea(e)&&"string"!=typeof e)return n;if(ei(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return en(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},eu=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},ef=(e,t)=>ea(e)&&ea(t)?r=>e+r*(t-e):()=>t,ed=e=>null==e,eh=e=>ed(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),ep=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function ey(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ev(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ey(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ey(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eg=(0,a.forwardRef)((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:o="100%",minWidth:l=0,minHeight:c,maxHeight:s,children:u,debounce:f=0,id:d,className:h,onResize:p,style:y={}}=e,v=(0,a.useRef)(null),g=(0,a.useRef)();g.current=p,(0,a.useImperativeHandle)(t,()=>v.current);var[m,b]=(0,a.useState)({containerWidth:n.width,containerHeight:n.height}),x=(0,a.useCallback)((e,t)=>{b(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,a.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;x(r,n),null===(t=g.current)||void 0===t||t.call(g,r,n)};f>0&&(e=Q()(e,f,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=v.current.getBoundingClientRect();return x(r,n),t.observe(v.current),()=>{t.disconnect()}},[x,f]);var w=(0,a.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=m;if(e<0||t<0)return null;ep(ei(i)||ei(o),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",i,o),ep(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=ei(i)?e:i,f=ei(o)?t:o;return r&&r>0&&(n?f=n/r:f&&(n=f*r),s&&f>s&&(f=s)),ep(n>0||f>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,f,i,o,l,c,r),a.Children.map(u,e=>(0,a.cloneElement)(e,{width:n,height:f,style:ev({height:"100%",width:"100%",maxHeight:f,maxWidth:n},e.props.style)}))},[r,u,o,s,c,l,m,i]);return a.createElement("div",{id:d?"".concat(d):void 0,className:(0,X.$)("recharts-responsive-container",h),style:ev(ev({},y),{},{width:i,height:o,minWidth:l,minHeight:c,maxHeight:s}),ref:v},w)});function em(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var eb="function"==typeof Symbol&&Symbol.observable||"@@observable",ex=()=>Math.random().toString(36).substring(7).split("").join("."),ew={INIT:`@@redux/INIT${ex()}`,REPLACE:`@@redux/REPLACE${ex()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${ex()}`};function eO(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function ej(e){let t;let r=Object.keys(e),n={};for(let t=0;t<r.length;t++){let i=r[t];"function"==typeof e[i]&&(n[i]=e[i])}let i=Object.keys(n);try{!function(e){Object.keys(e).forEach(t=>{let r=e[t];if(void 0===r(void 0,{type:ew.INIT}))throw Error(em(12));if(void 0===r(void 0,{type:ew.PROBE_UNKNOWN_ACTION()}))throw Error(em(13))})}(n)}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,o={};for(let t=0;t<i.length;t++){let l=i[t],c=n[l],s=e[l],u=c(s,r);if(void 0===u)throw r&&r.type,Error(em(14));o[l]=u,a=a||u!==s}return(a=a||i.length!==Object.keys(e).length)?o:e}}function eP(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function eA(e){return eO(e)&&"type"in e&&"string"==typeof e.type}function eS(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var ek=eS(),eM=Symbol.for("immer-nothing"),eE=Symbol.for("immer-draftable"),eT=Symbol.for("immer-state");function e_(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var eN=Object.getPrototypeOf;function eC(e){return!!e&&!!e[eT]}function eD(e){return!!e&&(eR(e)||Array.isArray(e)||!!e[eE]||!!e.constructor?.[eE]||eU(e)||eF(e))}var eI=Object.prototype.constructor.toString();function eR(e){if(!e||"object"!=typeof e)return!1;let t=eN(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===eI}function eL(e,t){0===ez(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function ez(e){let t=e[eT];return t?t.type_:Array.isArray(e)?1:eU(e)?2:eF(e)?3:0}function e$(e,t){return 2===ez(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function eB(e,t,r){let n=ez(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function eU(e){return e instanceof Map}function eF(e){return e instanceof Set}function eK(e){return e.copy_||e.base_}function eq(e,t){if(eU(e))return new Map(e);if(eF(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=eR(e);if(!0!==t&&("class_only"!==t||r)){let t=eN(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[eT];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(eN(e),t)}}function eH(e,t=!1){return eV(e)||eC(e)||!eD(e)||(ez(e)>1&&(e.set=e.add=e.clear=e.delete=eW),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>eH(t,!0))),e}function eW(){e_(2)}function eV(e){return Object.isFrozen(e)}var eY={};function eG(e){let t=eY[e];return t||e_(0,e),t}function eZ(e,t){t&&(eG("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function eX(e){eJ(e),e.drafts_.forEach(e0),e.drafts_=null}function eJ(e){e===l5&&(l5=e.parent_)}function eQ(e){return l5={drafts_:[],parent_:l5,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function e0(e){let t=e[eT];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function e1(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[eT].modified_&&(eX(t),e_(4)),eD(e)&&(e=e2(t,e),t.parent_||e5(t,e)),t.patches_&&eG("Patches").generateReplacementPatches_(r[eT].base_,e,t.patches_,t.inversePatches_)):e=e2(t,r,[]),eX(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==eM?e:void 0}function e2(e,t,r){if(eV(t))return t;let n=t[eT];if(!n)return eL(t,(i,a)=>e3(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return e5(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),eL(i,(i,o)=>e3(e,n,t,i,o,r,a)),e5(e,t,!1),r&&e.patches_&&eG("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function e3(e,t,r,n,i,a,o){if(eC(i)){let o=e2(e,i,a&&t&&3!==t.type_&&!e$(t.assigned_,n)?a.concat(n):void 0);if(eB(r,n,o),!eC(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(eD(i)&&!eV(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;e2(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&e5(e,i)}}function e5(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&eH(t,r)}var e4={get(e,t){if(t===eT)return e;let r=eK(e);if(!e$(r,t))return function(e,t,r){let n=e7(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);let n=r[t];return e.finalized_||!eD(n)?n:n===e8(e.base_,t)?(te(e),e.copy_[t]=tt(n,e)):n},has:(e,t)=>t in eK(e),ownKeys:e=>Reflect.ownKeys(eK(e)),set(e,t,r){let n=e7(eK(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=e8(eK(e),t),i=n?.[eT];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||e$(e.base_,t)))return!0;te(e),e9(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==e8(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,te(e),e9(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=eK(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){e_(11)},getPrototypeOf:e=>eN(e.base_),setPrototypeOf(){e_(12)}},e6={};function e8(e,t){let r=e[eT];return(r?eK(r):e)[t]}function e7(e,t){if(!(t in e))return;let r=eN(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=eN(r)}}function e9(e){!e.modified_&&(e.modified_=!0,e.parent_&&e9(e.parent_))}function te(e){e.copy_||(e.copy_=eq(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function tt(e,t){let r=eU(e)?eG("MapSet").proxyMap_(e,t):eF(e)?eG("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:l5,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=n,a=e4;r&&(i=[n],a=e6);let{revoke:o,proxy:l}=Proxy.revocable(i,a);return n.draft_=l,n.revoke_=o,l}(e,t);return(t?t.scope_:l5).drafts_.push(r),r}function tr(e){return eC(e)||e_(10,e),function e(t){let r;if(!eD(t)||eV(t))return t;let n=t[eT];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=eq(t,n.scope_.immer_.useStrictShallowCopy_)}else r=eq(t,!0);return eL(r,(t,n)=>{eB(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}eL(e4,(e,t)=>{e6[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),e6.deleteProperty=function(e,t){return e6.set.call(this,e,t,void 0)},e6.set=function(e,t,r){return e4.set.call(this,e[0],t,r,e[0])};var tn=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&e_(6),void 0!==r&&"function"!=typeof r&&e_(7),eD(e)){let i=eQ(this),a=tt(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?eX(i):eJ(i)}return eZ(i,r),e1(n,i)}if(e&&"object"==typeof e)e_(1,e);else{if(void 0===(n=t(e))&&(n=e),n===eM&&(n=void 0),this.autoFreeze_&&eH(n,!0),r){let t=[],i=[];eG("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){eD(e)||e_(8),eC(e)&&(e=tr(e));let t=eQ(this),r=tt(e,void 0);return r[eT].isManual_=!0,eJ(t),r}finishDraft(e,t){let r=e&&e[eT];r&&r.isManual_||e_(9);let{scope_:n}=r;return eZ(n,t),e1(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=eG("Patches").applyPatches_;return eC(e)?n(e,t):this.produce(e,e=>n(e,t))}},ti=tn.produce;tn.produceWithPatches.bind(tn),tn.setAutoFreeze.bind(tn),tn.setUseStrictShallowCopy.bind(tn),tn.applyPatches.bind(tn),tn.createDraft.bind(tn),tn.finishDraft.bind(tn);var ta="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?eP:eP.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var to=e=>e&&"function"==typeof e.match;function tl(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(t4(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>eA(t)&&t.type===e,r}function tc(e){return["type","payload","error","meta"].indexOf(e)>-1}var ts=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function tu(e){return eD(e)?ti(e,()=>{}):e}function tf(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var td=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:i=!0}=e??{},a=new ts;return t&&("boolean"==typeof t?a.push(ek):a.push(eS(t.extraArgument))),a},th=e=>t=>{setTimeout(t,e)},tp=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:th(10):"callback"===e.type?e.queueNotification:th(e.timeout),s=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,c(s)),n.dispatch(e)}finally{i=!0}}})},ty=e=>function(t){let{autoBatch:r=!0}=t??{},n=new ts(e);return r&&n.push(tp("object"==typeof r?r:void 0)),n};function tv(e){let t;let r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(t4(28));if(n in r)throw Error(t4(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var tg=(e,t)=>to(e)?e.match(t):e(t),tm=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},tb=["name","message","stack","code"],tx=Symbol.for("rtk-slice-createasyncthunk"),tw=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(tw||{}),tO=function({creators:e}={}){let t=e?.asyncThunk?.[tx];return function(e){let r;let{name:n,reducerPath:i=n}=e;if(!n)throw Error(t4(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},o=Object.keys(a),l={},c={},s={},u=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(t4(12));if(r in c)throw Error(t4(13));return c[r]=t,f},addMatcher:(e,t)=>(u.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(s[e]=t,f),exposeCaseReducer:(e,t)=>(l[e]=t,f)};function d(){let[t={},r=[],n]="function"==typeof e.extraReducers?tv(e.extraReducers):[e.extraReducers],i={...t,...c};return function(e,t){let r;let[n,i,a]=tv(t);if("function"==typeof e)r=()=>tu(e());else{let t=tu(e);r=()=>t}function o(e=r(),t){let l=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===l.filter(e=>!!e).length&&(l=[a]),l.reduce((e,r)=>{if(r){if(eC(e)){let n=r(e,t);return void 0===n?e:n}if(eD(e))return ti(e,e=>r(e,t));{let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return e},e)}return o.getInitialState=r,o}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of u)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}o.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(t4(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:c,settled:s,options:u}=r,f=i(e,a,u);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),c&&n.addCase(f.rejected,c),s&&n.addMatcher(f.settled,s),n.exposeCaseReducer(t,{fulfilled:o||tj,pending:l||tj,rejected:c||tj,settled:s||tj})}(o,i,f,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(t4(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?tl(e,o):tl(e))}(o,i,f)});let h=e=>e,p=new Map,y=new WeakMap;function v(e,t){return r||(r=d()),r(e,t)}function g(){return r||(r=d()),r.getInitialState()}function m(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=tf(y,n,g)),i}function i(t=h){let n=tf(p,r,()=>new WeakMap);return tf(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>tf(y,t,g),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let b={name:n,reducer:v,actions:s,caseReducers:l,getInitialState:g,...m(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:v},r),{...b,...m(n,!0)}}};return b}}();function tj(){}var tP="listener",tA="completed",tS="cancelled",tk=`task-${tS}`,tM=`task-${tA}`,tE=`${tP}-${tS}`,tT=`${tP}-${tA}`,t_=class{constructor(e){this.code=e,this.message=`task ${tS} (reason: ${e})`}name="TaskAbortError";message},tN=(e,t)=>{if("function"!=typeof e)throw TypeError(t4(32))},tC=()=>{},tD=(e,t=tC)=>(e.catch(t),e),tI=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),tR=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},tL=e=>{if(e.aborted){let{reason:t}=e;throw new t_(t)}};function tz(e,t){let r=tC;return new Promise((n,i)=>{let a=()=>i(new t_(e.reason));if(e.aborted){a();return}r=tI(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=tC})}var t$=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof t_?"cancelled":"rejected",error:e}}finally{t?.()}},tB=e=>t=>tD(tz(e,t).then(t=>(tL(e),t))),tU=e=>{let t=tB(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:tF}=Object,tK={},tq="listenerMiddleware",tH=(e,t)=>{let r=t=>tI(e,()=>tR(t,e.reason));return(n,i)=>{tN(n,"taskExecutor");let a=new AbortController;r(a);let o=t$(async()=>{tL(e),tL(a.signal);let t=await n({pause:tB(a.signal),delay:tU(a.signal),signal:a.signal});return tL(a.signal),t},()=>tR(a,tM));return i?.autoJoin&&t.push(o.catch(tC)),{result:tB(e)(o),cancel(){tR(a,tk)}}}},tW=(e,t)=>{let r=async(r,n)=>{tL(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await tz(t,Promise.race(a));return tL(t),e}finally{i()}};return(e,t)=>tD(r(e,t))},tV=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=tl(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(t4(21));return tN(a,"options.listener"),{predicate:i,type:t,effect:a}},tY=tF(e=>{let{type:t,predicate:r,effect:n}=tV(e);return{id:tm(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(t4(22))}}},{withTypes:()=>tY}),tG=(e,t)=>{let{type:r,effect:n,predicate:i}=tV(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},tZ=e=>{e.pending.forEach(e=>{tR(e,tE)})},tX=e=>()=>{e.forEach(tZ),e.clear()},tJ=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},tQ=tF(tl(`${tq}/add`),{withTypes:()=>tQ}),t0=tl(`${tq}/removeAll`),t1=tF(tl(`${tq}/remove`),{withTypes:()=>t1}),t2=(...e)=>{console.error(`${tq}/error`,...e)},t3=(e={})=>{let t=new Map,{extra:r,onError:n=t2}=e;tN(n,"onError");let i=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&tZ(e)}),a=e=>i(tG(t,e)??tY(e));tF(a,{withTypes:()=>a});let o=e=>{let r=tG(t,e);return r&&(r.unsubscribe(),e.cancelActive&&tZ(r)),!!r};tF(o,{withTypes:()=>o});let l=async(e,i,o,l)=>{let c=new AbortController,s=tW(a,c.signal),u=[];try{e.pending.add(c),await Promise.resolve(e.effect(i,tF({},o,{getOriginalState:l,condition:(e,t)=>s(e,t).then(Boolean),take:s,delay:tU(c.signal),pause:tB(c.signal),extra:r,signal:c.signal,fork:tH(c.signal,u),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==c&&(tR(e,tE),r.delete(e))})},cancel:()=>{tR(c,tE),e.pending.delete(c)},throwIfCancelled:()=>{tL(c.signal)}})))}catch(e){e instanceof t_||tJ(n,e,{raisedBy:"effect"})}finally{await Promise.all(u),tR(c,tT),e.pending.delete(c)}},c=tX(t);return{middleware:e=>r=>i=>{let s;if(!eA(i))return r(i);if(tQ.match(i))return a(i.payload);if(t0.match(i)){c();return}if(t1.match(i))return o(i.payload);let u=e.getState(),f=()=>{if(u===tK)throw Error(t4(23));return u};try{if(s=r(i),t.size>0){let r=e.getState();for(let a of Array.from(t.values())){let t=!1;try{t=a.predicate(i,r,u)}catch(e){t=!1,tJ(n,e,{raisedBy:"predicate"})}t&&l(a,i,e,f)}}}finally{u=tK}return s},startListening:a,stopListening:o,clearListeners:c}},t5=Symbol.for("rtk-state-proxy-original");function t4(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}function t6(e,t){if(t){var r=Number.parseInt(t,10);if(!en(r))return null==e?void 0:e[r]}}var t8=tO({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),t7=t8.reducer,{createEventEmitter:t9}=t8.actions;r(14202);var re=Symbol.for("react.forward_ref"),rt=Symbol.for("react.memo"),rr={notify(){},get:()=>[]},rn=!!("undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement),ri="undefined"!=typeof navigator&&"ReactNative"===navigator.product,ra=rn||ri?a.useLayoutEffect:a.useEffect,ro={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},rl={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},rc={[re]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[rt]:rl};Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var rs=Symbol.for("react-redux-context"),ru="undefined"!=typeof globalThis?globalThis:{},rf=function(){if(!a.createContext)return{};let e=ru[rs]??=new Map,t=e.get(a.createContext);return t||(t=a.createContext(null),e.set(a.createContext,t)),t}(),rd=function(e){let{children:t,context:r,serverState:n,store:i}=e,o=a.useMemo(()=>{let e=function(e,t){let r;let n=rr,i=0,a=!1;function o(){s.onStateChange&&s.onStateChange()}function l(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){(()=>{let e=t;for(;e;)e.callback(),e=e.next})()},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=rr)}let s={addNestedSub:function(e){l();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return s}(i);return{store:i,subscription:e,getServerState:n?()=>n:void 0}},[i,n]),l=a.useMemo(()=>i.getState(),[i]);return ra(()=>{let{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,l]),a.createElement((r||rf).Provider,{value:o},t)},rh={active:!1,index:null,dataKey:void 0,coordinate:void 0},rp=tO({name:"tooltip",initialState:{itemInteraction:{click:rh,hover:rh},axisInteraction:{click:rh,hover:rh},keyboardInteraction:rh,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=tr(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:ry,removeTooltipEntrySettings:rv,setTooltipSettingsState:rg,setActiveMouseOverItemIndex:rm,mouseLeaveItem:rb,mouseLeaveChart:rx,setActiveClickItemIndex:rw,setMouseOverAxisIndex:rO,setMouseClickAxisIndex:rj,setSyncInteraction:rP,setKeyboardInteraction:rA}=rp.actions,rS=rp.reducer,rk=tO({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:rM,setDataStartEndIndexes:rE,setComputedData:rT}=rk.actions,r_=rk.reducer,rN=tO({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:rC,setLayout:rD,setChartSize:rI,setScale:rR}=rN.actions,rL=rN.reducer,rz=e=>Array.isArray(e)?e:[e],r$=0,rB=class{revision=r$;_value;_lastValue;_isEqual=rU;constructor(e,t=rU){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++r$)}};function rU(e,t){return e===t}function rF(e){return e instanceof rB||console.warn("Not a valid cell! ",e),e.value}var rK=(e,t)=>!1;function rq(){return function(e,t=rU){return new rB(null,t)}(0,rK)}var rH=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=rq()),rF(t)};Symbol();var rW=0,rV=Object.getPrototypeOf({}),rY=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,rG);tag=rq();tags={};children={};collectionTag=null;id=rW++},rG={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in rV)return n;if("object"==typeof n&&null!==n){var i;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(i=n)?new rZ(i):new rY(i)),r.tag&&rF(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=rq()).value=n),rF(r),n}})(),ownKeys:e=>(rH(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},rZ=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],rX);tag=rq();tags={};children={};collectionTag=null;id=rW++},rX={get:([e],t)=>("length"===t&&rH(e),rG.get(e,t)),ownKeys:([e])=>rG.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>rG.getOwnPropertyDescriptor(e,t),has:([e],t)=>rG.has(e,t)},rJ="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function rQ(){return{s:0,v:void 0,o:null,p:null}}function r0(e,t={}){let r,n=rQ(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=rQ(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=rQ(),e.set(t,o)):o=r}}let c=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new rJ(t):t}return c.s=1,c.v=t,t}return o.clearCache=()=>{n=rQ(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var r1=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,i=0,a={},o=e.pop();"object"==typeof o&&(a=o,o=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);let{memoize:l,memoizeOptions:c=[],argsMemoize:s=r0,argsMemoizeOptions:u=[],devModeChecks:f={}}={...r,...a},d=rz(c),h=rz(u),p=function(e){let t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=l(function(){return n++,o.apply(null,arguments)},...d);return Object.assign(s(function(){i++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(p,arguments);return t=y.apply(null,e)},...h),{resultFunc:o,memoizedResultFunc:y,dependencies:p,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:l,argsMemoize:s})};return Object.assign(n,{withTypes:()=>n}),n}(r0),r2=Object.assign((e,t=r1)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>r2}),r3=r(73450),r5=(0,a.createContext)(null),r4=e=>e,r6=()=>{var e=(0,a.useContext)(r5);return e?e.store.dispatch:r4},r8=()=>{},r7=()=>r8,r9=(e,t)=>e===t;function ne(e){var t=(0,a.useContext)(r5);return(0,r3.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:r7,t?t.store.getState:r8,t?t.store.getState:r8,t?e:r8,r9)}var nt=r(17476),nr=r.n(nt),nn=e=>e.legend.settings,ni=r1([e=>e.legend.payload,nn],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?nr()(n,r):n});function na(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function no(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function nl(e){return function(){return e}}function nc(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function ns(e,t){return e[t]}function nu(e){let t=[];return t.key=e,t}function nf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nd(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nf(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nf(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Array.prototype.slice;var nh=Math.PI/180,np=e=>180*e/Math.PI,ny=(e,t,r,n)=>({x:e+Math.cos(-nh*n)*r,y:t+Math.sin(-nh*n)*r}),nv=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},ng=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},nm=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=ng({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-i)/o);return n>a&&(l=2*Math.PI-l),{radius:o,angle:np(l),angleInRadian:l}},nb=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},nx=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},nw=(e,t)=>{var r,{x:n,y:i}=e,{radius:a,angle:o}=nm({x:n,y:i},t),{innerRadius:l,outerRadius:c}=t;if(a<l||a>c||0===a)return null;var{startAngle:s,endAngle:u}=nb(t),f=o;if(s<=u){for(;f>u;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=u}else{for(;f>s;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=s}return r?nd(nd({},t),{},{radius:a,angle:nx(f,t)}):null};function nO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nO(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nP(e,t,r){return ed(e)||ed(t)?r:eo(t)?et()(e,t,r):"function"==typeof t?t(e):r}var nA=(e,t,r,n,i)=>{var a,o=-1,l=null!==(a=null==t?void 0:t.length)&&void 0!==a?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var c=0;c<l;c++){var s=c>0?r[c-1].coordinate:r[l-1].coordinate,u=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,d=void 0;if(er(u-s)!==er(f-u)){var h=[];if(er(f-u)===er(i[1]-i[0])){d=f;var p=u+i[1]-i[0];h[0]=Math.min(p,(p+s)/2),h[1]=Math.max(p,(p+s)/2)}else{d=s;var y=f+i[1]-i[0];h[0]=Math.min(u,(y+u)/2),h[1]=Math.max(u,(y+u)/2)}var v=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>v[0]&&e<=v[1]||e>=h[0]&&e<=h[1]){({index:o}=r[c]);break}}else{var g=Math.min(s,f),m=Math.max(s,f);if(e>(g+u)/2&&e<=(m+u)/2){({index:o}=r[c]);break}}}else if(t){for(var b=0;b<l;b++)if(0===b&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b>0&&b<l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b===l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2){({index:o}=t[b]);break}}return o},nS=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&ea(e[a]))return nj(nj({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&ea(e[o]))return nj(nj({},e),{},{[o]:e[o]+(i||0)})}return e},nk=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,nM=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},nE=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:s,tickCount:u,ticks:f,niceTicks:d,axisType:h}=e;if(!o)return null;var p="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,y=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/p:0;return(y="angleAxis"===h&&a&&a.length>=2?2*er(a[0]-a[1])*y:y,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!en(e.coordinate)):c&&s?s.map((e,t)=>({coordinate:o(e)+y,value:e,index:t,offset:y})):o.ticks&&!r&&null!=u?o.ticks(u).map((e,t)=>({coordinate:o(e)+y,value:e,offset:y,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+y,value:n?n[e]:e,index:t,offset:y}))},nT=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},n_=(e,t)=>{if(!t||2!==t.length||!ea(t[0])||!ea(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!ea(e[0])||e[0]<r)&&(i[0]=r),(!ea(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},nN={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=en(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}na(e,t)}},none:na,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}na(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,c=0,s=0;l<i;++l){for(var u=e[t[l]],f=u[o][1]||0,d=(f-(u[o-1][1]||0))/2,h=0;h<l;++h){var p=e[t[h]];d+=(p[o][1]||0)-(p[o-1][1]||0)}c+=f,s+=d*f}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,na(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=en(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},nC=(e,t,r)=>{var n=nN[r];return(function(){var e=nl([]),t=nc,r=na,n=ns;function i(i){var a,o,l=Array.from(e.apply(this,arguments),nu),c=l.length,s=-1;for(let e of i)for(a=0,++s;a<c;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=no(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:nl(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:nl(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?nc:"function"==typeof e?e:nl(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?na:e,i):r},i})().keys(t).value((e,t)=>+nP(e,t,0)).order(nc).offset(n)(e)},nD=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=nP(a,t.dataKey,t.scale.domain()[o]);return ed(l)?null:t.scale(l)-i/2+n},nI=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},nR=e=>{var t=e.flat(2).filter(ea);return[Math.min(...t),Math.max(...t)]},nL=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],nz=(e,t,r)=>{if(null!=e)return nL(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=nR(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},n$=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,nB=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,nU=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=nr()(t,e=>e.coordinate),a=1/0,o=1,l=i.length;o<l;o++){var c=i[o],s=i[o-1];a=Math.min((c.coordinate||0)-(s.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function nF(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return nj(nj({},t),{},{dataKey:r,payload:n,value:i,name:a})}function nK(e,t){return e?String(e):"string"==typeof t?t:void 0}var nq=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return nj(nj(nj({},n),ny(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return nj(nj(nj({},n),ny(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}},nH=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius,nW=e=>e.layout.width,nV=e=>e.layout.height,nY=e=>e.layout.scale,nG=e=>e.layout.margin,nZ=r1(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),nX=r1(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),nJ="data-recharts-item-index",nQ="data-recharts-item-data-key";function n0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function n1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n0(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n0(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var n2=r1([nW,nV,nG,e=>e.brush.height,nZ,nX,nn,e=>e.legend.size],(e,t,r,n,i,a,o,l)=>{var c=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return n1(n1({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),s=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:n1(n1({},e),{},{[r]:et()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),u=n1(n1({},s),c),f=u.bottom;u.bottom+=n;var d=e-(u=nS(u,o,l)).left-u.right,h=t-u.top-u.bottom;return n1(n1({brushBottom:f},u),{},{width:Math.max(d,0),height:Math.max(h,0)})}),n3=r1(n2,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),n5=r1(nW,nV,(e,t)=>({x:0,y:0,width:e,height:t})),n4=(0,a.createContext)(null),n6=()=>null!=(0,a.useContext)(n4),n8=e=>e.brush,n7=r1([n8,n2,nG],(e,t,r)=>({height:e.height,x:ea(e.x)?e.x:t.left,y:ea(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:ea(e.width)?e.width:t.width})),n9=()=>{var e,t=n6(),r=ne(n3),n=ne(n7),i=null===(e=ne(n8))||void 0===e?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},ie={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},it=()=>{var e;return null!==(e=ne(n2))&&void 0!==e?e:ie},ir=()=>ne(nW),ii=()=>ne(nV),ia={top:0,right:0,bottom:0,left:0},io=()=>{var e;return null!==(e=ne(e=>e.layout.margin))&&void 0!==e?e:ia},il=e=>e.layout.layoutType,ic=()=>ne(il),is=r(41192),iu=r.n(is);function id(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function ih(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class ip extends Map{constructor(e,t=iv){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(iy(this,e))}has(e){return super.has(iy(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function iy({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function iv(e){return null!==e&&"object"==typeof e?e.valueOf():e}let ig=Symbol("implicit");function im(){var e=new ip,t=[],r=[],n=ig;function i(i){let a=e.get(i);if(void 0===a){if(n!==ig)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new ip,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return im(t,r).unknown(n)},id.apply(i,arguments),i}function ib(){var e,t,r=im().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,c=0,s=0,u=.5;function f(){var r=n().length,f=o<a,d=f?o:a,h=f?a:o;e=(h-d)/Math.max(1,r-c+2*s),l&&(e=Math.floor(e)),d+=(h-d-e*(r-c))*u,t=e*(1-c),l&&(d=Math.round(d),t=Math.round(t));var p=(function(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a=+a,o=+o,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a=+a,o=+o,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,s=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return ib(n(),[a,o]).round(l).paddingInner(c).paddingOuter(s).align(u)},id.apply(f(),arguments)}function ix(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(ib.apply(null,arguments).paddingInner(1))}let iw=Math.sqrt(50),iO=Math.sqrt(10),ij=Math.sqrt(2);function iP(e,t,r){let n,i,a;let o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),c=o/Math.pow(10,l),s=c>=iw?10:c>=iO?5:c>=ij?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/s)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*s)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?iP(e,t,2*r):[n,i,a]}function iA(e,t,r){if(t=+t,e=+e,!((r=+r)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?iP(t,e,r):iP(e,t,r);if(!(a>=i))return[];let l=a-i+1,c=Array(l);if(n){if(o<0)for(let e=0;e<l;++e)c[e]=-((a-e)/o);else for(let e=0;e<l;++e)c[e]=(a-e)*o}else if(o<0)for(let e=0;e<l;++e)c[e]=-((i+e)/o);else for(let e=0;e<l;++e)c[e]=(i+e)*o;return c}function iS(e,t,r){return iP(e=+e,t=+t,r=+r)[2]}function ik(e,t,r){t=+t,e=+e,r=+r;let n=t<e,i=n?iS(t,e,r):iS(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function iM(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function iE(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function iT(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=iM,r=(t,r)=>iM(e(t),r),n=(t,r)=>e(t)-r):(t=e===iM||e===iE?e:i_,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function i_(){return 0}function iN(e){return null===e?NaN:+e}let iC=iT(iM),iD=iC.right;function iI(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function iR(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function iL(){}iC.left,iT(iN).center;var iz="\\s*([+-]?\\d+)\\s*",i$="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",iB="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iU=/^#([0-9a-f]{3,8})$/,iF=RegExp(`^rgb\\(${iz},${iz},${iz}\\)$`),iK=RegExp(`^rgb\\(${iB},${iB},${iB}\\)$`),iq=RegExp(`^rgba\\(${iz},${iz},${iz},${i$}\\)$`),iH=RegExp(`^rgba\\(${iB},${iB},${iB},${i$}\\)$`),iW=RegExp(`^hsl\\(${i$},${iB},${iB}\\)$`),iV=RegExp(`^hsla\\(${i$},${iB},${iB},${i$}\\)$`),iY={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iG(){return this.rgb().formatHex()}function iZ(){return this.rgb().formatRgb()}function iX(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=iU.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?iJ(t):3===r?new i1(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?iQ(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?iQ(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=iF.exec(e))?new i1(t[1],t[2],t[3],1):(t=iK.exec(e))?new i1(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=iq.exec(e))?iQ(t[1],t[2],t[3],t[4]):(t=iH.exec(e))?iQ(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=iW.exec(e))?i8(t[1],t[2]/100,t[3]/100,1):(t=iV.exec(e))?i8(t[1],t[2]/100,t[3]/100,t[4]):iY.hasOwnProperty(e)?iJ(iY[e]):"transparent"===e?new i1(NaN,NaN,NaN,0):null}function iJ(e){return new i1(e>>16&255,e>>8&255,255&e,1)}function iQ(e,t,r,n){return n<=0&&(e=t=r=NaN),new i1(e,t,r,n)}function i0(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof iL||(i=iX(i)),i)?new i1((i=i.rgb()).r,i.g,i.b,i.opacity):new i1:new i1(e,t,r,null==n?1:n)}function i1(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function i2(){return`#${i6(this.r)}${i6(this.g)}${i6(this.b)}`}function i3(){let e=i5(this.opacity);return`${1===e?"rgb(":"rgba("}${i4(this.r)}, ${i4(this.g)}, ${i4(this.b)}${1===e?")":`, ${e})`}`}function i5(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function i4(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function i6(e){return((e=i4(e))<16?"0":"")+e.toString(16)}function i8(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new i9(e,t,r,n)}function i7(e){if(e instanceof i9)return new i9(e.h,e.s,e.l,e.opacity);if(e instanceof iL||(e=iX(e)),!e)return new i9;if(e instanceof i9)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new i9(o,l,c,e.opacity)}function i9(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ae(e){return(e=(e||0)%360)<0?e+360:e}function at(e){return Math.max(0,Math.min(1,e||0))}function ar(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function an(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}iI(iL,iX,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:iG,formatHex:iG,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return i7(this).formatHsl()},formatRgb:iZ,toString:iZ}),iI(i1,i0,iR(iL,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new i1(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new i1(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new i1(i4(this.r),i4(this.g),i4(this.b),i5(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:i2,formatHex:i2,formatHex8:function(){return`#${i6(this.r)}${i6(this.g)}${i6(this.b)}${i6((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:i3,toString:i3})),iI(i9,function(e,t,r,n){return 1==arguments.length?i7(e):new i9(e,t,r,null==n?1:n)},iR(iL,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new i9(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new i9(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new i1(ar(e>=240?e-240:e+120,i,n),ar(e,i,n),ar(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new i9(ae(this.h),at(this.s),at(this.l),i5(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=i5(this.opacity);return`${1===e?"hsl(":"hsla("}${ae(this.h)}, ${100*at(this.s)}%, ${100*at(this.l)}%${1===e?")":`, ${e})`}`}}));let ai=e=>()=>e;function aa(e,t){var r=t-e;return r?function(t){return e+t*r}:ai(isNaN(e)?t:e)}let ao=function e(t){var r,n=1==(r=+(r=t))?aa:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):ai(isNaN(e)?t:e)};function i(e,t){var r=n((e=i0(e)).r,(t=i0(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=aa(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function al(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=i0(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}function ac(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}al(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return an((r-n/t)*t,o,i,a,l)}}),al(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return an((r-n/t)*t,i,a,o,l)}});var as=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,au=RegExp(as.source,"g");function af(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ai(t):("number"===i?ac:"string"===i?(n=iX(t))?(t=n,ao):function(e,t){var r,n,i,a,o,l=as.lastIndex=au.lastIndex=0,c=-1,s=[],u=[];for(e+="",t+="";(i=as.exec(e))&&(a=au.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),s[c]?s[c]+=o:s[++c]=o),(i=i[0])===(a=a[0])?s[c]?s[c]+=a:s[++c]=a:(s[++c]=null,u.push({i:c,x:ac(i,a)})),l=au.lastIndex;return l<t.length&&(o=t.slice(l),s[c]?s[c]+=o:s[++c]=o),s.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)s[(r=u[n]).i]=r.x(e);return s.join("")})}:t instanceof iX?ao:t instanceof Date?function(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=af(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=af(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ac:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function ad(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function ah(e){return+e}var ap=[0,1];function ay(e){return e}function av(e,t){var r;return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function ag(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=av(i,n),a=r(o,a)):(n=av(n,i),a=r(a,o)),function(e){return a(n(e))}}function am(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=av(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=iD(e,t,1,n)-1;return a[r](i[r](t))}}function ab(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function ax(){var e,t,r,n,i,a,o=ap,l=ap,c=af,s=ay;function u(){var e,t,r,c=Math.min(o.length,l.length);return s!==ay&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),s=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?am:ag,i=a=null,f}function f(t){return null==t||isNaN(t=+t)?r:(i||(i=n(o.map(e),l,c)))(e(s(t)))}return f.invert=function(r){return s(t((a||(a=n(l,o.map(e),ac)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,ah),u()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),u()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=ad,u()},f.clamp=function(e){return arguments.length?(s=!!e||ay,u()):s!==ay},f.interpolate=function(e){return arguments.length?(c=e,u()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function aw(){return ax()(ay,ay)}var aO=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function aj(e){var t;if(!(t=aO.exec(e)))throw Error("invalid format: "+e);return new aP({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function aP(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function aA(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function aS(e){return(e=aA(Math.abs(e)))?e[1]:NaN}function ak(e,t){var r=aA(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}aj.prototype=aP.prototype,aP.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let aM={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>ak(100*e,t),r:ak,s:function(e,t){var r=aA(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(l4=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+Array(1-a).join("0")+aA(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function aE(e){return e}var aT=Array.prototype.map,a_=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function aN(e,t,r,n){var i,a,o=ik(e,t,r);switch((n=aj(null==n?",f":n)).type){case"s":var l=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(aS(l)/3)))-aS(Math.abs(o))))||(n.precision=a),l7(n,l);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,aS(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=o)))-aS(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-aS(Math.abs(o))))||(n.precision=a-("%"===n.type)*2)}return l8(n)}function aC(e){var t=e.domain;return e.ticks=function(e){var r=t();return iA(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return aN(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],s=a[l],u=10;for(s<c&&(i=c,c=s,s=i,i=o,o=l,l=i);u-- >0;){if((i=iS(c,s,r))===n)return a[o]=c,a[l]=s,t(a);if(i>0)c=Math.floor(c/i)*i,s=Math.ceil(s/i)*i;else if(i<0)c=Math.ceil(c*i)/i,s=Math.floor(s*i)/i;else break;n=i}return e},e}function aD(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function aI(e){return Math.log(e)}function aR(e){return Math.exp(e)}function aL(e){return-Math.log(-e)}function az(e){return-Math.exp(-e)}function a$(e){return isFinite(e)?+("1e"+e):e<0?0:e}function aB(e){return(t,r)=>-e(-t,r)}function aU(e){let t,r;let n=e(aI,aR),i=n.domain,a=10;function o(){var o,l;return t=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=a)?a$:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=aB(t),r=aB(r),e(aL,az)):e(aI,aR),n}return n.base=function(e){return arguments.length?(a=+e,o()):a},n.domain=function(e){return arguments.length?(i(e),o()):i()},n.ticks=e=>{let n,o;let l=i(),c=l[0],s=l[l.length-1],u=s<c;u&&([c,s]=[s,c]);let f=t(c),d=t(s),h=null==e?10:+e,p=[];if(!(a%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),c>0){for(;f<=d;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<c)){if(o>s)break;p.push(o)}}else for(;f<=d;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<c)){if(o>s)break;p.push(o)}2*p.length<h&&(p=iA(c,s,h))}else p=iA(f,d,Math.min(d-f,h)).map(r);return u?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=aj(i)).precision||(i.trim=!0),i=l8(i)),e===1/0)return i;let o=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=o?i(e):""}},n.nice=()=>i(aD(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function aF(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function aK(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function aq(e){var t=1,r=e(aF(1),aK(t));return r.constant=function(r){return arguments.length?e(aF(t=+r),aK(t)):t},aC(r)}function aH(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function aW(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function aV(e){return e<0?-e*e:e*e}function aY(e){var t=e(ay,ay),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(ay,ay):.5===r?e(aW,aV):e(aH(r),aH(1/r)):r},aC(t)}function aG(){var e=aY(ax());return e.copy=function(){return ab(e,aG()).exponent(e.exponent())},id.apply(e,arguments),e}function aZ(){return aG.apply(null,arguments).exponent(.5)}function aX(e){return Math.sign(e)*e*e}function aJ(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function aQ(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function a0(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function a1(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}l8=(l6=function(e){var t,r,n,i=void 0===e.grouping||void 0===e.thousands?aE:(t=aT.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",o=void 0===e.currency?"":e.currency[1]+"",l=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?aE:(n=aT.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),s=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=aj(e)).fill,r=e.align,n=e.sign,d=e.symbol,h=e.zero,p=e.width,y=e.comma,v=e.precision,g=e.trim,m=e.type;"n"===m?(y=!0,m="g"):aM[m]||(void 0===v&&(v=12),g=!0,m="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var b="$"===d?a:"#"===d&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",x="$"===d?o:/[%p]/.test(m)?s:"",w=aM[m],O=/[defgprs%]/.test(m);function j(e){var a,o,s,d=b,j=x;if("c"===m)j=w(e)+j,e="";else{var P=(e=+e)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),v),g&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),P&&0==+e&&"+"!==n&&(P=!1),d=(P?"("===n?n:u:"-"===n||"("===n?"":n)+d,j=("s"===m?a_[8+l4/3]:"")+j+(P&&"("===n?")":""),O){for(a=-1,o=e.length;++a<o;)if(48>(s=e.charCodeAt(a))||s>57){j=(46===s?l+e.slice(a+1):e.slice(a))+j,e=e.slice(0,a);break}}}y&&!h&&(e=i(e,1/0));var A=d.length+e.length+j.length,S=A<p?Array(p-A+1).join(t):"";switch(y&&h&&(e=i(S+e,S.length?p-j.length:1/0),S=""),r){case"<":e=d+e+j+S;break;case"=":e=d+S+e+j;break;case"^":e=S.slice(0,A=S.length>>1)+d+e+j+S.slice(A);break;default:e=S+d+e+j}return c(e)}return v=void 0===v?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:d,formatPrefix:function(e,t){var r=d(((e=aj(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(aS(t)/3))),i=Math.pow(10,-n),a=a_[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,l7=l6.formatPrefix;let a2=new Date,a3=new Date;function a5(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o;let l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>a5(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(i.count=(t,n)=>(a2.setTime(+t),a3.setTime(+n),e(a2),e(a3),Math.floor(r(a2,a3))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let a4=a5(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);a4.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?a5(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):a4:null,a4.range;let a6=a5(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());a6.range;let a8=a5(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());a8.range;let a7=a5(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());a7.range;let a9=a5(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());a9.range;let oe=a5(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());oe.range;let ot=a5(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);ot.range;let or=a5(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);or.range;let on=a5(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function oi(e){return a5(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}on.range;let oa=oi(0),oo=oi(1),ol=oi(2),oc=oi(3),os=oi(4),ou=oi(5),of=oi(6);function od(e){return a5(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}oa.range,oo.range,ol.range,oc.range,os.range,ou.range,of.range;let oh=od(0),op=od(1),oy=od(2),ov=od(3),og=od(4),om=od(5),ob=od(6);oh.range,op.range,oy.range,ov.range,og.range,om.range,ob.range;let ox=a5(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());ox.range;let ow=a5(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());ow.range;let oO=a5(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());oO.every=e=>isFinite(e=Math.floor(e))&&e>0?a5(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,oO.range;let oj=a5(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function oP(e,t,r,n,i,a){let o=[[a6,1,1e3],[a6,5,5e3],[a6,15,15e3],[a6,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=iT(([,,e])=>e).right(o,i);if(a===o.length)return e.every(ik(t/31536e6,r/31536e6,n));if(0===a)return a4.every(Math.max(ik(t,r,n),1));let[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}oj.every=e=>isFinite(e=Math.floor(e))&&e>0?a5(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,oj.range;let[oA,oS]=oP(oj,ow,oh,on,oe,a7),[ok,oM]=oP(oO,ox,oa,ot,a9,a8);function oE(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function oT(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function o_(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var oN={"-":"",_:" ",0:"0"},oC=/^\s*\d+/,oD=/^%/,oI=/[\\^$*+?|[\]().{}]/g;function oR(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function oL(e){return e.replace(oI,"\\$&")}function oz(e){return RegExp("^(?:"+e.map(oL).join("|")+")","i")}function o$(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function oB(e,t,r){var n=oC.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function oU(e,t,r){var n=oC.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function oF(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function oK(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function oq(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function oH(e,t,r){var n=oC.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function oW(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function oV(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function oY(e,t,r){var n=oC.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function oG(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function oZ(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function oX(e,t,r){var n=oC.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function oJ(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function oQ(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function o0(e,t,r){var n=oC.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function o1(e,t,r){var n=oC.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function o2(e,t,r){var n=oC.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function o3(e,t,r){var n=oD.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function o5(e,t,r){var n=oC.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function o4(e,t,r){var n=oC.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function o6(e,t){return oR(e.getDate(),t,2)}function o8(e,t){return oR(e.getHours(),t,2)}function o7(e,t){return oR(e.getHours()%12||12,t,2)}function o9(e,t){return oR(1+ot.count(oO(e),e),t,3)}function le(e,t){return oR(e.getMilliseconds(),t,3)}function lt(e,t){return le(e,t)+"000"}function lr(e,t){return oR(e.getMonth()+1,t,2)}function ln(e,t){return oR(e.getMinutes(),t,2)}function li(e,t){return oR(e.getSeconds(),t,2)}function la(e){var t=e.getDay();return 0===t?7:t}function lo(e,t){return oR(oa.count(oO(e)-1,e),t,2)}function ll(e){var t=e.getDay();return t>=4||0===t?os(e):os.ceil(e)}function lc(e,t){return e=ll(e),oR(os.count(oO(e),e)+(4===oO(e).getDay()),t,2)}function ls(e){return e.getDay()}function lu(e,t){return oR(oo.count(oO(e)-1,e),t,2)}function lf(e,t){return oR(e.getFullYear()%100,t,2)}function ld(e,t){return oR((e=ll(e)).getFullYear()%100,t,2)}function lh(e,t){return oR(e.getFullYear()%1e4,t,4)}function lp(e,t){var r=e.getDay();return oR((e=r>=4||0===r?os(e):os.ceil(e)).getFullYear()%1e4,t,4)}function ly(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+oR(t/60|0,"0",2)+oR(t%60,"0",2)}function lv(e,t){return oR(e.getUTCDate(),t,2)}function lg(e,t){return oR(e.getUTCHours(),t,2)}function lm(e,t){return oR(e.getUTCHours()%12||12,t,2)}function lb(e,t){return oR(1+or.count(oj(e),e),t,3)}function lx(e,t){return oR(e.getUTCMilliseconds(),t,3)}function lw(e,t){return lx(e,t)+"000"}function lO(e,t){return oR(e.getUTCMonth()+1,t,2)}function lj(e,t){return oR(e.getUTCMinutes(),t,2)}function lP(e,t){return oR(e.getUTCSeconds(),t,2)}function lA(e){var t=e.getUTCDay();return 0===t?7:t}function lS(e,t){return oR(oh.count(oj(e)-1,e),t,2)}function lk(e){var t=e.getUTCDay();return t>=4||0===t?og(e):og.ceil(e)}function lM(e,t){return e=lk(e),oR(og.count(oj(e),e)+(4===oj(e).getUTCDay()),t,2)}function lE(e){return e.getUTCDay()}function lT(e,t){return oR(op.count(oj(e)-1,e),t,2)}function l_(e,t){return oR(e.getUTCFullYear()%100,t,2)}function lN(e,t){return oR((e=lk(e)).getUTCFullYear()%100,t,2)}function lC(e,t){return oR(e.getUTCFullYear()%1e4,t,4)}function lD(e,t){var r=e.getUTCDay();return oR((e=r>=4||0===r?og(e):og.ceil(e)).getUTCFullYear()%1e4,t,4)}function lI(){return"+0000"}function lR(){return"%"}function lL(e){return+e}function lz(e){return Math.floor(+e/1e3)}function l$(e){return new Date(e)}function lB(e){return e instanceof Date?+e:+new Date(+e)}function lU(e,t,r,n,i,a,o,l,c,s){var u=aw(),f=u.invert,d=u.domain,h=s(".%L"),p=s(":%S"),y=s("%I:%M"),v=s("%I %p"),g=s("%a %d"),m=s("%b %d"),b=s("%B"),x=s("%Y");function w(e){return(c(e)<e?h:l(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,lB)):d().map(l$)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:s(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(aD(r,e)):u},u.copy=function(){return ab(u,lU(e,t,r,n,i,a,o,l,c,s))},u}function lF(){return id.apply(lU(ok,oM,oO,ox,oa,ot,a9,a8,a6,ce).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function lK(){return id.apply(lU(oA,oS,oj,ow,oh,or,oe,a7,a6,ct).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function lq(){var e,t,r,n,i,a=0,o=1,l=ay,c=!1;function s(t){return null==t||isNaN(t=+t)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),s):[l(0),l(1)]}}return s.domain=function(i){return arguments.length?([a,o]=i,e=n(a=+a),t=n(o=+o),r=e===t?0:1/(t-e),s):[a,o]},s.clamp=function(e){return arguments.length?(c=!!e,s):c},s.interpolator=function(e){return arguments.length?(l=e,s):l},s.range=u(af),s.rangeRound=u(ad),s.unknown=function(e){return arguments.length?(i=e,s):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),s}}function lH(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function lW(){var e=aY(lq());return e.copy=function(){return lH(e,lW()).exponent(e.exponent())},ih.apply(e,arguments)}function lV(){return lW.apply(null,arguments).exponent(.5)}function lY(){var e,t,r,n,i,a,o,l=0,c=.5,s=1,u=1,f=ay,d=!1;function h(e){return isNaN(e=+e)?o:(e=.5+((e=+a(e))-t)*(u*e<u*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=af);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(o){return arguments.length?([l,c,s]=o,e=a(l=+l),t=a(c=+c),r=a(s=+s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,h):[l,c,s]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(af),h.rangeRound=p(ad),h.unknown=function(e){return arguments.length?(o=e,h):o},function(o){return a=o,e=o(l),t=o(c),r=o(s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,h}}function lG(){var e=aY(lY());return e.copy=function(){return lH(e,lG()).exponent(e.exponent())},ih.apply(e,arguments)}function lZ(){return lG.apply(null,arguments).exponent(.5)}ce=(l9=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,s=oz(i),u=o$(i),f=oz(a),d=o$(a),h=oz(o),p=o$(o),y=oz(l),v=o$(l),g=oz(c),m=o$(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:o6,e:o6,f:lt,g:ld,G:lp,H:o8,I:o7,j:o9,L:le,m:lr,M:ln,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:lL,s:lz,S:li,u:la,U:lo,V:lc,w:ls,W:lu,x:null,X:null,y:lf,Y:lh,Z:ly,"%":lR},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:lv,e:lv,f:lw,g:lN,G:lD,H:lg,I:lm,j:lb,L:lx,m:lO,M:lj,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:lL,s:lz,S:lP,u:lA,U:lS,V:lM,w:lE,W:lT,x:null,X:null,y:l_,Y:lC,Z:lI,"%":lR},w={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:oZ,e:oZ,f:o2,g:oW,G:oH,H:oJ,I:oJ,j:oX,L:o1,m:oG,M:oQ,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:oY,Q:o5,s:o4,S:o0,u:oU,U:oF,V:oK,w:oB,W:oq,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:oW,Y:oH,Z:oV,"%":o3};function O(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++l<s;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=oN[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=o_(1900,void 0,1);if(P(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=oT(o_(a.y,0,1))).getUTCDay())>4||0===i?op.ceil(n):op(n),n=or.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=oE(o_(a.y,0,1))).getDay())>4||0===i?oo.ceil(n):oo(n),n=ot.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?oT(o_(a.y,0,1)).getUTCDay():oE(o_(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,oT(a)):oE(a)}}function P(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in oN?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l9.parse,ct=l9.utcFormat,l9.utcParse;var lX=e=>e.chartData,lJ=r1([lX],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),lQ=(e,t,r,n)=>n?lJ(e):lX(e);function l0(e){return Number.isFinite(e)}function l1(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function l2(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(l0(t)&&l0(r))return!0}return!1}function l3(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var l5,l4,l6,l8,l7,l9,ce,ct,cr,cn,ci=!0,ca="[DecimalError] ",co=ca+"Invalid argument: ",cl=ca+"Exponent out of range: ",cc=Math.floor,cs=Math.pow,cu=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,cf=cc(1286742750677284.5),cd={};function ch(e,t){var r,n,i,a,o,l,c,s,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),ci?cj(t,f):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,l=s.length):(n=s,i=o,l=c.length),a>(l=(o=Math.ceil(f/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=c.length)-(a=s.length)<0&&(a=l,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),l=c.length;0==c[--l];)c.pop();return t.d=c,t.e=i,ci?cj(t,f):t}function cp(e,t,r){if(e!==~~e||e<t||e>r)throw Error(co+e)}function cy(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=cx(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=cx(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}cd.absoluteValue=cd.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},cd.comparedTo=cd.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},cd.decimalPlaces=cd.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},cd.dividedBy=cd.div=function(e){return cv(this,new this.constructor(e))},cd.dividedToIntegerBy=cd.idiv=function(e){var t=this.constructor;return cj(cv(this,new t(e),0,1),t.precision)},cd.equals=cd.eq=function(e){return!this.cmp(e)},cd.exponent=function(){return cm(this)},cd.greaterThan=cd.gt=function(e){return this.cmp(e)>0},cd.greaterThanOrEqualTo=cd.gte=function(e){return this.cmp(e)>=0},cd.isInteger=cd.isint=function(){return this.e>this.d.length-2},cd.isNegative=cd.isneg=function(){return this.s<0},cd.isPositive=cd.ispos=function(){return this.s>0},cd.isZero=function(){return 0===this.s},cd.lessThan=cd.lt=function(e){return 0>this.cmp(e)},cd.lessThanOrEqualTo=cd.lte=function(e){return 1>this.cmp(e)},cd.logarithm=cd.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(cn))throw Error(ca+"NaN");if(this.s<1)throw Error(ca+(this.s?"NaN":"-Infinity"));return this.eq(cn)?new r(0):(ci=!1,t=cv(cw(this,i),cw(e,i),i),ci=!0,cj(t,n))},cd.minus=cd.sub=function(e){return e=new this.constructor(e),this.s==e.s?cP(this,e):ch(this,(e.s=-e.s,e))},cd.modulo=cd.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(ca+"NaN");return this.s?(ci=!1,t=cv(this,e,0,1).times(e),ci=!0,this.minus(t)):cj(new r(this),n)},cd.naturalExponential=cd.exp=function(){return cg(this)},cd.naturalLogarithm=cd.ln=function(){return cw(this)},cd.negated=cd.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},cd.plus=cd.add=function(e){return e=new this.constructor(e),this.s==e.s?ch(this,e):cP(this,(e.s=-e.s,e))},cd.precision=cd.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(co+e);if(t=cm(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},cd.squareRoot=cd.sqrt=function(){var e,t,r,n,i,a,o,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(ca+"NaN")}for(e=cm(this),ci=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=cy(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=cc((e+1)/2)-(e<0||e%2),n=new l(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new l(i.toString()),i=o=(r=l.precision)+3;;)if(n=(a=n).plus(cv(this,a,o+2)).times(.5),cy(a.d).slice(0,o)===(t=cy(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(cj(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return ci=!0,cj(n,r)},cd.times=cd.mul=function(e){var t,r,n,i,a,o,l,c,s,u=this.constructor,f=this.d,d=(e=new u(e)).d;if(!this.s||!e.s)return new u(0);for(e.s*=this.s,r=this.e+e.e,(c=f.length)<(s=d.length)&&(a=f,f=d,d=a,o=c,c=s,s=o),a=[],n=o=c+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)l=a[i]+d[n]*f[i-n-1]+t,a[i--]=l%1e7|0,t=l/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,ci?cj(e,u.precision):e},cd.toDecimalPlaces=cd.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(cp(e,0,1e9),void 0===t?t=n.rounding:cp(t,0,8),cj(r,e+cm(r)+1,t))},cd.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=cA(n,!0):(cp(e,0,1e9),void 0===t?t=i.rounding:cp(t,0,8),r=cA(n=cj(new i(n),e+1,t),!0,e+1)),r},cd.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?cA(this):(cp(e,0,1e9),void 0===t?t=i.rounding:cp(t,0,8),r=cA((n=cj(new i(this),e+cm(this)+1,t)).abs(),!1,e+cm(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},cd.toInteger=cd.toint=function(){var e=this.constructor;return cj(new e(this),cm(this)+1,e.rounding)},cd.toNumber=function(){return+this},cd.toPower=cd.pow=function(e){var t,r,n,i,a,o,l=this,c=l.constructor,s=+(e=new c(e));if(!e.s)return new c(cn);if(!(l=new c(l)).s){if(e.s<1)throw Error(ca+"Infinity");return l}if(l.eq(cn))return l;if(n=c.precision,e.eq(cn))return cj(l,n);if(o=(t=e.e)>=(r=e.d.length-1),a=l.s,o){if((r=s<0?-s:s)<=0x1fffffffffffff){for(i=new c(cn),t=Math.ceil(n/7+4),ci=!1;r%2&&cS((i=i.times(l)).d,t),0!==(r=cc(r/2));)cS((l=l.times(l)).d,t);return ci=!0,e.s<0?new c(cn).div(i):cj(i,n)}}else if(a<0)throw Error(ca+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,l.s=1,ci=!1,i=e.times(cw(l,n+12)),ci=!0,(i=cg(i)).s=a,i},cd.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=cm(i),n=cA(i,r<=a.toExpNeg||r>=a.toExpPos)):(cp(e,1,1e9),void 0===t?t=a.rounding:cp(t,0,8),r=cm(i=cj(new a(i),e,t)),n=cA(i,e<=r||r<=a.toExpNeg,e)),n},cd.toSignificantDigits=cd.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(cp(e,1,1e9),void 0===t?t=r.rounding:cp(t,0,8)),cj(new r(this),e,t)},cd.toString=cd.valueOf=cd.val=cd.toJSON=cd[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=cm(this),t=this.constructor;return cA(this,e<=t.toExpNeg||e>=t.toExpPos)};var cv=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,c,s,u,f,d,h,p,y,v,g,m,b,x,w,O,j,P,A=n.constructor,S=n.s==i.s?1:-1,k=n.d,M=i.d;if(!n.s)return new A(n);if(!i.s)throw Error(ca+"Division by zero");for(s=0,c=n.e-i.e,j=M.length,w=k.length,p=(h=new A(S)).d=[];M[s]==(k[s]||0);)++s;if(M[s]>(k[s]||0)&&--c,(m=null==a?a=A.precision:o?a+(cm(n)-cm(i))+1:a)<0)return new A(0);if(m=m/7+2|0,s=0,1==j)for(u=0,M=M[0],m++;(s<w||u)&&m--;s++)b=1e7*u+(k[s]||0),p[s]=b/M|0,u=b%M|0;else{for((u=1e7/(M[0]+1)|0)>1&&(M=e(M,u),k=e(k,u),j=M.length,w=k.length),x=j,v=(y=k.slice(0,j)).length;v<j;)y[v++]=0;(P=M.slice()).unshift(0),O=M[0],M[1]>=1e7/2&&++O;do u=0,(l=t(M,y,j,v))<0?(g=y[0],j!=v&&(g=1e7*g+(y[1]||0)),(u=g/O|0)>1?(u>=1e7&&(u=1e7-1),d=(f=e(M,u)).length,v=y.length,1==(l=t(f,y,d,v))&&(u--,r(f,j<d?P:M,d))):(0==u&&(l=u=1),f=M.slice()),(d=f.length)<v&&f.unshift(0),r(y,f,v),-1==l&&(v=y.length,(l=t(M,y,j,v))<1&&(u++,r(y,j<v?P:M,v))),v=y.length):0===l&&(u++,y=[0]),p[s++]=u,l&&y[0]?y[v++]=k[x]||0:(y=[k[x]],v=1);while((x++<w||void 0!==y[0])&&m--)}return p[0]||p.shift(),h.e=c,cj(h,o?a+cm(h)+1:a)}}();function cg(e,t){var r,n,i,a,o,l=0,c=0,s=e.constructor,u=s.precision;if(cm(e)>16)throw Error(cl+cm(e));if(!e.s)return new s(cn);for(null==t?(ci=!1,o=u):o=t,a=new s(.03125);e.abs().gte(.1);)e=e.times(a),c+=5;for(o+=Math.log(cs(2,c))/Math.LN10*2+5|0,r=n=i=new s(cn),s.precision=o;;){if(n=cj(n.times(e),o),r=r.times(++l),cy((a=i.plus(cv(n,r,o))).d).slice(0,o)===cy(i.d).slice(0,o)){for(;c--;)i=cj(i.times(i),o);return s.precision=u,null==t?(ci=!0,cj(i,u)):i}i=a}}function cm(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function cb(e,t,r){if(t>e.LN10.sd())throw ci=!0,r&&(e.precision=r),Error(ca+"LN10 precision limit exceeded");return cj(new e(e.LN10),t)}function cx(e){for(var t="";e--;)t+="0";return t}function cw(e,t){var r,n,i,a,o,l,c,s,u,f=1,d=e,h=d.d,p=d.constructor,y=p.precision;if(d.s<1)throw Error(ca+(d.s?"NaN":"-Infinity"));if(d.eq(cn))return new p(0);if(null==t?(ci=!1,s=y):s=t,d.eq(10))return null==t&&(ci=!0),cb(p,s);if(s+=10,p.precision=s,n=(r=cy(h)).charAt(0),!(15e14>Math.abs(a=cm(d))))return c=cb(p,s+2,y).times(a+""),d=cw(new p(n+"."+r.slice(1)),s-10).plus(c),p.precision=y,null==t?(ci=!0,cj(d,y)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=cy((d=d.times(e)).d)).charAt(0),f++;for(a=cm(d),n>1?(d=new p("0."+r),a++):d=new p(n+"."+r.slice(1)),l=o=d=cv(d.minus(cn),d.plus(cn),s),u=cj(d.times(d),s),i=3;;){if(o=cj(o.times(u),s),cy((c=l.plus(cv(o,new p(i),s))).d).slice(0,s)===cy(l.d).slice(0,s))return l=l.times(2),0!==a&&(l=l.plus(cb(p,s+2,y).times(a+""))),l=cv(l,new p(f),s),p.precision=y,null==t?(ci=!0,cj(l,y)):l;l=c,i+=2}}function cO(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,r=r-n-1,e.e=cc(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),ci&&(e.e>cf||e.e<-cf))throw Error(cl+r)}else e.s=0,e.e=0,e.d=[0];return e}function cj(e,t,r){var n,i,a,o,l,c,s,u,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,s=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(a=f.length))return e;for(o=1,s=a=f[u];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(l=s/(a=cs(10,o-i-1))%10|0,c=t<0||void 0!==f[u+1]||s%a,c=r<4?(l||c)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||c||6==r&&(n>0?i>0?s/cs(10,o-i):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return c?(a=cm(e),f.length=1,t=t-a-1,f[0]=cs(10,(7-t%7)%7),e.e=cc(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,a=1,u--):(f.length=u+1,a=cs(10,7-n),f[u]=i>0?(s/cs(10,o-i)%cs(10,i)|0)*a:0),c)for(;;){if(0==u){1e7==(f[0]+=a)&&(f[0]=1,++e.e);break}if(f[u]+=a,1e7!=f[u])break;f[u--]=0,a=1}for(n=f.length;0===f[--n];)f.pop();if(ci&&(e.e>cf||e.e<-cf))throw Error(cl+cm(e));return e}function cP(e,t){var r,n,i,a,o,l,c,s,u,f,d=e.constructor,h=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),ci?cj(t,h):t;if(c=e.d,f=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n){for((u=o<0)?(r=c,o=-o,l=f.length):(r=f,n=s,l=c.length),o>(i=Math.max(Math.ceil(h/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((u=(i=c.length)<(l=f.length))&&(l=i),i=0;i<l;i++)if(c[i]!=f[i]){u=c[i]<f[i];break}o=0}for(u&&(r=c,c=f,f=r,t.s=-t.s),l=c.length,i=f.length-l;i>0;--i)c[l++]=0;for(i=f.length;i>o;){if(c[--i]<f[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=f[i]}for(;0===c[--l];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,ci?cj(t,h):t):new d(0)}function cA(e,t,r){var n,i=cm(e),a=cy(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+cx(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+cx(-i-1)+a,r&&(n=r-o)>0&&(a+=cx(n))):i>=o?(a+=cx(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+cx(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=cx(n))),e.s<0?"-"+a:a}function cS(e,t){if(e.length>t)return e.length=t,!0}function ck(e){if(!e||"object"!=typeof e)throw Error(ca+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]])){if(cc(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(co+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(co+r+": "+n)}return this}var cr=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(co+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return cO(this,e.toString())}if("string"!=typeof e)throw Error(co+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,cu.test(e))cO(this,e);else throw Error(co+e)}if(a.prototype=cd,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=ck,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cn=new cr(1);let cM=cr;var cE=e=>e,cT={},c_=e=>e===cT,cN=e=>function t(){return 0==arguments.length||1==arguments.length&&c_(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},cC=(e,t)=>1===e?t:cN(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==cT).length;return a>=e?t(...n):cC(e-a,cN(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>c_(e)?r.shift():e),...r)}))}),cD=e=>cC(e.length,e),cI=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},cR=cD((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),cL=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return cE;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},cz=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),c$=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null===(n=t)||void 0===n?void 0:n[r])})?r:(t=i,r=e(...i))}};function cB(e){return 0===e?1:Math.floor(new cM(e).abs().log(10).toNumber())+1}function cU(e,t,r){for(var n=new cM(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}cD((e,t,r)=>{var n=+e;return n+r*(+t-n)}),cD((e,t,r)=>{var n=t-+e;return(r-e)/(n=n||1/0)}),cD((e,t,r)=>{var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var cF=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},cK=(e,t,r)=>{if(e.lte(0))return new cM(0);var n=cB(e.toNumber()),i=new cM(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new cM(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new cM(t?l.toNumber():Math.ceil(l.toNumber()))},cq=(e,t,r)=>{var n=new cM(1),i=new cM(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new cM(10).pow(cB(e)-1),i=new cM(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new cM(Math.floor(e)))}else 0===e?i=new cM(Math.floor((t-1)/2)):r||(i=new cM(Math.floor(e)));var o=Math.floor((t-1)/2);return cL(cR(e=>i.add(new cM(e-o).mul(n)).toNumber()),cI)(0,t)},cH=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new cM(0),tickMin:new cM(0),tickMax:new cM(0)};var o=cK(new cM(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new cM(0):(i=new cM(e).add(t).div(2)).sub(new cM(i).mod(o))).sub(e).div(o).toNumber()),c=Math.ceil(new cM(t).sub(i).div(o).toNumber()),s=l+c+1;return s>r?cH(e,t,r,n,a+1):(s<r&&(c=t>0?c+(r-s):c,l=t>0?l:l+(r-s)),{step:o,tickMin:i.sub(new cM(l).mul(o)),tickMax:i.add(new cM(c).mul(o))})},cW=c$(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=cF([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...cI(0,n-1).map(()=>1/0)]:[...cI(0,n-1).map(()=>-1/0),l];return t>r?cz(c):c}if(o===l)return cq(o,n,i);var{step:s,tickMin:u,tickMax:f}=cH(o,l,a,i,0),d=cU(u,f.add(new cM(.1).mul(s)),s);return t>r?cz(d):d}),cV=c$(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=cF([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=cK(new cM(o).sub(a).div(l-1),i,0),s=[...cU(new cM(a),new cM(o).sub(new cM(.99).mul(c)),c),o];return r>n?cz(s):s}),cY=e=>e.rootProps.maxBarSize,cG=e=>e.rootProps.barCategoryGap,cZ=e=>e.rootProps.stackOffset,cX=e=>e.options.chartName,cJ=e=>e.rootProps.syncId,cQ=e=>e.rootProps.syncMethod,c0=e=>e.options.eventEmitter,c1={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},c2={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},c3=(e,t)=>e&&t?null!=e&&e.reversed?[t[1],t[0]]:t:void 0,c5={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:c1.angleAxisId,includeHidden:!1,name:void 0,reversed:c1.reversed,scale:c1.scale,tick:c1.tick,tickCount:void 0,ticks:void 0,type:c1.type,unit:void 0},c4={allowDataOverflow:c2.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c2.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c2.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c2.scale,tick:c2.tick,tickCount:c2.tickCount,ticks:void 0,type:c2.type,unit:void 0},c6={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:c1.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c1.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c1.scale,tick:c1.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},c8={allowDataOverflow:c2.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c2.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c2.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c2.scale,tick:c2.tick,tickCount:c2.tickCount,ticks:void 0,type:"category",unit:void 0},c7=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?c6:c5,c9=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?c8:c4,se=e=>e.polarOptions,st=r1([nW,nV,n2],nv),sr=r1([se,st],(e,t)=>{if(null!=e)return es(e.innerRadius,t,0)}),sn=r1([se,st],(e,t)=>{if(null!=e)return es(e.outerRadius,t,.8*t)}),si=r1([se],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});r1([c7,si],c3);var sa=r1([st,sr,sn],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});r1([c9,sa],c3);var so=r1([il,se,sr,sn,nW,nV],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:c,endAngle:s}=t;return{cx:es(o,i,i/2),cy:es(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}}),sl=(e,t)=>t,sc=(e,t,r)=>r;function ss(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function su(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ss(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ss(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var sf=[0,"auto"],sd={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},sh=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?sd:r},sp={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:sf,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},sy=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?sp:r},sv={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},sg=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?sv:r},sm=(e,t,r)=>{switch(t){case"xAxis":return sh(e,r);case"yAxis":return sy(e,r);case"zAxis":return sg(e,r);case"angleAxis":return c7(e,r);case"radiusAxis":return c9(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},sb=(e,t,r)=>{switch(t){case"xAxis":return sh(e,r);case"yAxis":return sy(e,r);case"angleAxis":return c7(e,r);case"radiusAxis":return c9(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},sx=e=>e.graphicalItems.countOfBars>0;function sw(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var sO=e=>e.graphicalItems.cartesianItems,sj=r1([sl,sc],sw),sP=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),sA=r1([sO,sm,sj],sP),sS=e=>e.filter(e=>void 0===e.stackId),sk=r1([sA],sS),sM=e=>e.map(e=>e.data).filter(Boolean).flat(1),sE=r1([sA],sM),sT=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},s_=r1([sE,lQ],sT),sN=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:nP(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:nP(e,t)}))):e.map(e=>({value:e})),sC=r1([s_,sm,sA],sN);function sD(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function sI(e){return e.filter(e=>eo(e)||e instanceof Date).map(Number).filter(e=>!1===en(e))}var sR=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t;return[n,{stackedData:nC(e,i.map(e=>e.dataKey),r),graphicalItems:i}]})),sL=r1([s_,sA,cZ],sR),sz=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=nz(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},s$=r1([sL,lX,sl],sz),sB=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null===(i=r.errorBars)||void 0===i?void 0:i.filter(e=>sD(n,e)),l=nP(e,null!==(a=t.dataKey)&&void 0!==a?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||en(t)||!r.length?[]:sI(r.flatMap(r=>{var n,i,a=nP(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,l0(n)&&l0(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:nP(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),sU=r1(s_,sm,sk,sl,sB);function sF(e){var{value:t}=e;if(eo(t)||t instanceof Date)return t}var sK=e=>{var t=sI(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},sq=(e,t,r)=>{var n=e.map(sF).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&eu(n))?iu()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},sH=e=>{var t;if(null==e||!("domain"in e))return sf;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=sI(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!==(t=null==e?void 0:e.domain)&&void 0!==t?t:sf},sW=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},sV=e=>e.referenceElements.dots,sY=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),sG=r1([sV,sl,sc],sY),sZ=e=>e.referenceElements.areas,sX=r1([sZ,sl,sc],sY),sJ=e=>e.referenceElements.lines,sQ=r1([sJ,sl,sc],sY),s0=(e,t)=>{var r=sI(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},s1=r1(sG,sl,s0),s2=(e,t)=>{var r=sI(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},s3=r1([sX,sl],s2),s5=(e,t)=>{var r=sI(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},s4=r1(sQ,sl,s5),s6=r1(s1,s4,s3,(e,t,r)=>sW(e,r,t)),s8=r1([sm],sH),s7=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(l0(i))r=i;else if("function"==typeof i)return;if(l0(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(l2(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(l2(n))return l3(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(ea(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&n$.test(o)){var c=n$.exec(o);if(null==c||null==t)i=void 0;else{var s=+c[1];i=t[0]-s}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(ea(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&nB.test(l)){var u=nB.exec(l);if(null==u||null==t)a=void 0;else{var f=+u[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(l2(d))return null==t?d:l3(d,t,r)}}}(t,sW(r,i,sK(n)),e.allowDataOverflow)},s9=r1([sm,s8,s$,sU,s6],s7),ue=[0,1],ut=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:c}=e,s=nk(t,a);return s&&null==l?iu()(0,r.length):"category"===c?sq(n,e,s):"expand"===i?ue:o}},ur=r1([sm,il,s_,sC,cZ,sl,s9],ut),un=(e,t,r,i,a)=>{if(null!=e){var{scale:o,type:l}=e;if("auto"===o)return"radial"===t&&"radiusAxis"===a?"band":"radial"===t&&"angleAxis"===a?"linear":"category"===l&&i&&(i.indexOf("LineChart")>=0||i.indexOf("AreaChart")>=0||i.indexOf("ComposedChart")>=0&&!r)?"point":"category"===l?"band":"linear";if("string"==typeof o){var c="scale".concat(eh(o));return c in n?c:"point"}}},ui=r1([sm,il,sx,cX,sl],un);function ua(e,t,r,i){if(null!=r&&null!=i){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(i);var a=function(e){if(null!=e){if(e in n)return n[e]();var t="scale".concat(eh(e));if(t in n)return n[t]()}}(t);if(null!=a){var o=a.domain(r).range(i);return nT(o),o}}}var uo=(e,t,r)=>{var n=sH(t);return"auto"!==r&&"linear"!==r?void 0:null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&l2(e)?cW(e,t.tickCount,t.allowDecimals):null!=t&&t.tickCount&&"number"===t.type&&l2(e)?cV(e,t.tickCount,t.allowDecimals):void 0},ul=r1([ur,sb,ui],uo),uc=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&l2(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,us=r1([sm,ur,ul,sl],uc),uu=r1(sC,sm,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(sI(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),uf=r1(uu,il,cG,n2,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!l0(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=es(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),ud=r1(sh,(e,t)=>{var r=sh(e,t);return null==r||"string"!=typeof r.padding?0:uf(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!==(r=i.left)&&void 0!==r?r:0)+t,right:(null!==(n=i.right)&&void 0!==n?n:0)+t}}),uh=r1(sy,(e,t)=>{var r=sy(e,t);return null==r||"string"!=typeof r.padding?0:uf(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!==(r=i.top)&&void 0!==r?r:0)+t,bottom:(null!==(n=i.bottom)&&void 0!==n?n:0)+t}}),up=r1([n2,ud,n7,n8,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),uy=r1([n2,il,uh,n7,n8,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),uv=(e,t,r,n)=>{var i;switch(t){case"xAxis":return up(e,r,n);case"yAxis":return uy(e,r,n);case"zAxis":return null===(i=sg(e,r))||void 0===i?void 0:i.range;case"angleAxis":return si(e);case"radiusAxis":return sa(e,r);default:return}},ug=r1([sm,uv],c3),um=r1([sm,ui,us,ug],ua);function ub(e,t){return e.id<t.id?-1:e.id>t.id?1:0}r1(sA,sl,(e,t)=>e.flatMap(e=>{var t;return null!==(t=e.errorBars)&&void 0!==t?t:[]}).filter(e=>sD(t,e)));var ux=(e,t)=>t,uw=(e,t,r)=>r,uO=r1(nZ,ux,uw,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(ub)),uj=r1(nX,ux,uw,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(ub)),uP=(e,t)=>({width:e.width,height:t.height}),uA=(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}),uS=r1(n2,sh,uP),uk=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},uM=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},uE=r1(nV,n2,uO,ux,uw,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=uP(t,r);null==a&&(a=uk(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height}),o}),uT=r1(nW,n2,uj,ux,uw,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=uA(t,r);null==a&&(a=uM(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width}),o}),u_=(e,t)=>{var r=n2(e),n=sh(e,t);if(null!=n){var i=uE(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},uN=(e,t)=>{var r=n2(e),n=sy(e,t);if(null!=n){var i=uT(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},uC=r1(n2,sy,(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height})),uD=(e,t,r)=>{switch(t){case"xAxis":return uS(e,r).width;case"yAxis":return uC(e,r).height;default:return}},uI=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=nk(e,n),c=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&eu(c))return c}},uR=r1([il,sC,sm,sl],uI),uL=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if(nk(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},uz=r1([il,sC,sb,sl],uL),u$=r1([il,(e,t,r)=>{switch(t){case"xAxis":return sh(e,r);case"yAxis":return sy(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ui,um,uR,uz,uv,ul,sl],(e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var s=nk(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:s,niceTicks:l,range:o,realScaleType:r,scale:n}}),uB=r1([il,sb,ui,um,ul,uv,uR,uz,sl],(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var s=nk(e,c),{type:u,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===u&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===c&&null!=a&&a.length>=2?2*er(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!en(e.coordinate)):s&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),uU=r1([il,sb,um,uv,uR,uz,sl],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=nk(e,o),{tickCount:c}=t,s=0;return(s="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*er(n[0]-n[1])*s:s,l&&a)?a.map((e,t)=>({coordinate:r(e)+s,value:e,index:t,offset:s})):r.ticks?r.ticks(c).map(e=>({coordinate:r(e)+s,value:e,offset:s})):r.domain().map((e,t)=>({coordinate:r(e)+s,value:i?i[e]:e,index:t,offset:s}))}}),uF=r1(sm,um,(e,t)=>{if(null!=e&&null!=t)return su(su({},e),{},{scale:t})}),uK=r1([sm,ui,ur,ug],ua);r1((e,t,r)=>sg(e,r),uK,(e,t)=>{if(null!=e&&null!=t)return su(su({},e),{},{scale:t})});var uq=r1([il,nZ,nX],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),uH=e=>e.options.defaultTooltipEventType,uW=e=>e.options.validateTooltipEventTypes;function uV(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function uY(e,t){return uV(t,uH(e),uW(e))}var uG=(e,t)=>{var r,n=Number(t);if(!en(n)&&null!=t)return n>=0?null==e||null===(r=e[n])||void 0===r?void 0:r.value:void 0};function uZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uZ(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var uJ=(e,t,r,n)=>{if(null==t)return rh;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return rh;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return uX(uX({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return uX(uX({},rh),{},{coordinate:i.coordinate})},uQ=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!l0(n))return r;var i=Infinity;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},u0=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],s=null==c?void 0:l(c.positions,a);if(null!=s)return s;var u=null==i?void 0:i[Number(a)];if(u)return"horizontal"===r?{x:u.coordinate,y:(n.top+t)/2}:{x:(n.left+e)/2,y:u.coordinate}}},u1=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null===(t=e.settings)||void 0===t?void 0:t.dataKey)===i})},u2=e=>e.options.tooltipPayloadSearcher,u3=e=>e.tooltip,u5=e=>{var t=il(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},u4=e=>e.tooltip.settings.axisId,u6=e=>{var t=u5(e),r=u4(e);return sb(e,t,r)},u8=r1([u6,il,sx,cX,u5],un),u7=r1([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),u9=r1([u5,u4],sw),fe=r1([u7,u6,u9],sP),ft=r1([fe],sM),fr=r1([ft,lX],sT),fn=r1([fr,u6,fe],sN),fi=r1([u6],sH),fa=r1([fr,fe,cZ],sR),fo=r1([fa,lX,u5],sz),fl=r1([fe],sS),fc=r1([fr,u6,fl,u5],sB),fs=r1([sV,u5,u4],sY),fu=r1([fs,u5],s0),ff=r1([sZ,u5,u4],sY),fd=r1([ff,u5],s2),fh=r1([sJ,u5,u4],sY),fp=r1([fh,u5],s5),fy=r1([fu,fp,fd],sW),fv=r1([u6,fi,fo,fc,fy],s7),fg=r1([u6,il,fr,fn,cZ,u5,fv],ut),fm=r1([fg,u6,u8],uo),fb=r1([u6,fg,fm,u5],uc),fx=e=>{var t=u5(e),r=u4(e);return uv(e,t,r,!1)},fw=r1([u6,fx],c3),fO=r1([u6,u8,fb,fw],ua),fj=r1([il,fn,u6,u5],uI),fP=r1([il,fn,u6,u5],uL),fA=r1([il,u6,u8,fO,fx,fj,fP,u5],(e,t,r,n,i,a,o,l)=>{if(t){var{type:c}=t,s=nk(e,l);if(n){var u="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,f="category"===c&&n.bandwidth?n.bandwidth()/u:0;return(f="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*er(i[0]-i[1])*f:f,s&&o)?o.map((e,t)=>({coordinate:n(e)+f,value:e,index:t,offset:f})):n.domain().map((e,t)=>({coordinate:n(e)+f,value:a?a[e]:e,index:t,offset:f}))}}}),fS=r1([uH,uW,e=>e.tooltip.settings],(e,t,r)=>uV(r.shared,e,t)),fk=e=>e.tooltip.settings.trigger,fM=e=>e.tooltip.settings.defaultIndex,fE=r1([u3,fS,fk,fM],uJ),fT=r1([fE,fr],uQ),f_=r1([fA,fT],uG),fN=r1([fE],e=>{if(e)return e.dataKey}),fC=r1([u3,fS,fk,fM],u1),fD=r1([nW,nV,il,n2,fA,fM,fC,u2],u0),fI=r1([fE,fD],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),fR=r1([fE],e=>e.active);function fL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fL(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f$=(e,t)=>t,fB=(e,t,r)=>r,fU=(e,t,r,n)=>n,fF=r1(fA,e=>nr()(e,e=>e.coordinate)),fK=r1([u3,f$,fB,fU],uJ),fq=r1([fK,fr],uQ),fH=r1([u3,f$,fB,fU],u1),fW=r1([nW,nV,il,n2,fA,fU,fH,u2],u0);r1([fK,fW],(e,t)=>{var r;return null!==(r=e.coordinate)&&void 0!==r?r:t});var fV=r1(fA,fq,uG);r1([fH,fq,lX,u6,fV,u2,f$],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:c,dataStartIndex:s,dataEndIndex:u}=r;return e.reduce((e,r)=>{var f,d,h,{dataDefinedOnItem:p,settings:y}=r,v=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}(null!=p?p:l,s,u),g=null!==(f=null==y?void 0:y.dataKey)&&void 0!==f?f:null==n?void 0:n.dataKey,m=null==y?void 0:y.nameKey;return Array.isArray(d=null!=n&&n.dataKey&&!(null!=n&&n.allowDuplicatedCategory)&&Array.isArray(v)&&"axis"===o?function(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):et()(e,t))===r)}(v,n.dataKey,i):a(v,t,c,m))?d.forEach(t=>{var r=fz(fz({},y),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(nF({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:nP(t.payload,t.dataKey),name:t.name}))}):e.push(nF({tooltipEntrySettings:y,dataKey:g,payload:d,value:nP(d,g),name:null!==(h=nP(d,m))&&void 0!==h?h:null==y?void 0:y.name})),e},[])}}),r1([fK],e=>({isActive:e.active,activeIndex:e.index}));var fY=r1([(e,t)=>t,il,so,u5,fw,fA,fF,n2],(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var c=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?nw({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(c){var s=nA(nH(c,t),o,a,n,i),u=nq(t,a,s,c);return{activeIndex:String(s),activeCoordinate:u}}}}),fG=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},fZ=tl("mouseClick"),fX=t3();fX.startListening({actionCreator:fZ,effect:(e,t)=>{var r=e.payload,n=fY(t.getState(),fG(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch(rj({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var fJ=tl("mouseMove"),fQ=t3();function f0(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}function f1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f1(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}fQ.startListening({actionCreator:fJ,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=uY(n,n.tooltip.settings.shared),a=fY(n,fG(r));"axis"===i&&((null==a?void 0:a.activeIndex)!=null?t.dispatch(rO({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(rx()))}});var f3=tO({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=f2(f2({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:f5,removeXAxis:f4,addYAxis:f6,removeYAxis:f8,addZAxis:f7,removeZAxis:f9,updateYAxisWidth:de}=f3.actions,dt=f3.reducer,dr=tO({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,i=tr(e).cartesianItems.indexOf(r);i>-1&&(e.cartesianItems[i]=n)},removeCartesianGraphicalItem(e,t){var r=tr(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=tr(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:dn,removeBar:di,addCartesianGraphicalItem:da,replaceCartesianGraphicalItem:dl,removeCartesianGraphicalItem:dc,addPolarGraphicalItem:ds,removePolarGraphicalItem:du}=dr.actions,df=dr.reducer,dd=tO({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=tr(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=tr(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=tr(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:dh,removeDot:dp,addArea:dy,removeArea:dv,addLine:dg,removeLine:dm}=dd.actions,db=dd.reducer,dx={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},dw=tO({name:"brush",initialState:dx,reducers:{setBrushSettings:(e,t)=>null==t.payload?dx:t.payload}}),{setBrushSettings:dO}=dw.actions,dj=dw.reducer,dP=tO({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=tr(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:dA,setLegendSettings:dS,addLegendPayload:dk,removeLegendPayload:dM}=dP.actions,dE=dP.reducer,dT={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},d_=tO({name:"rootProps",initialState:dT,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!==(r=t.payload.barGap)&&void 0!==r?r:dT.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),dN=d_.reducer,{updateOptions:dC}=d_.actions,dD=tO({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:dI,removeRadiusAxis:dR,addAngleAxis:dL,removeAngleAxis:dz}=dD.actions,d$=dD.reducer,dB=tO({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:dU}=dB.actions,dF=dB.reducer,dK=tl("keyDown"),dq=tl("focus"),dH=t3();dH.startListening({actionCreator:dK,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(uQ(n,fr(r))),o=fA(r);if("Enter"===i){var l=fW(r,"axis","hover",String(n.index));t.dispatch(rA({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var c=a+("ArrowRight"===i?1:-1)*("left-to-right"===uq(r)?1:-1);if(null!=o&&!(c>=o.length)&&!(c<0)){var s=fW(r,"axis","hover",String(c));t.dispatch(rA({active:!0,activeIndex:c.toString(),activeDataKey:void 0,activeCoordinate:s}))}}}}}),dH.startListening({actionCreator:dq,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=fW(r,"axis","hover",String("0"));t.dispatch(rA({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var dW=tl("externalEvent"),dV=t3();dV.startListening({actionCreator:dW,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:fI(r),activeDataKey:fN(r),activeIndex:fT(r),activeLabel:f_(r),activeTooltipIndex:fT(r),isTooltipActive:fR(r)};e.payload.handler(n,e.payload.reactEvent)}}});var dY=r1([u3],e=>e.tooltipItemPayloads),dG=r1([dY,u2,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),dZ=tl("touchMove"),dX=t3();dX.startListening({actionCreator:dZ,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=uY(n,n.tooltip.settings.shared);if("axis"===i){var a=fY(n,fG({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==a?void 0:a.activeIndex)!=null&&t.dispatch(rO({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],c=document.elementFromPoint(l.clientX,l.clientY);if(!c||!c.getAttribute)return;var s=c.getAttribute(nJ),u=null!==(o=c.getAttribute(nQ))&&void 0!==o?o:void 0,f=dG(t.getState(),s,u);t.dispatch(rm({activeDataKey:u,activeIndex:s,activeCoordinate:f}))}}});var dJ=ej({brush:dj,cartesianAxis:dt,chartData:r_,graphicalItems:df,layout:rL,legend:dE,options:t7,polarAxis:d$,polarOptions:dF,referenceElements:db,rootProps:dN,tooltip:rS}),dQ=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(e){let t,r;let n=td(),{reducer:i,middleware:a,devTools:o=!0,duplicateMiddlewareCheck:l=!0,preloadedState:c,enhancers:s}=e||{};if("function"==typeof i)t=i;else if(eO(i))t=ej(i);else throw Error(t4(1));r="function"==typeof a?a(n):n();let u=eP;o&&(u=ta({trace:!1,..."object"==typeof o&&o}));let f=ty(function(...e){return t=>(r,n)=>{let i=t(r,n),a=()=>{throw Error(em(15))},o={getState:i.getState,dispatch:(e,...t)=>a(e,...t)};return a=eP(...e.map(e=>e(o)))(i.dispatch),{...i,dispatch:a}}}(...r));return function e(t,r,n){if("function"!=typeof t)throw Error(em(2));if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(em(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(em(1));return n(e)(t,r)}let i=t,a=r,o=new Map,l=o,c=0,s=!1;function u(){l===o&&(l=new Map,o.forEach((e,t)=>{l.set(t,e)}))}function f(){if(s)throw Error(em(3));return a}function d(e){if("function"!=typeof e)throw Error(em(4));if(s)throw Error(em(5));let t=!0;u();let r=c++;return l.set(r,e),function(){if(t){if(s)throw Error(em(6));t=!1,u(),l.delete(r),o=null}}}function h(e){if(!eO(e))throw Error(em(7));if(void 0===e.type)throw Error(em(8));if("string"!=typeof e.type)throw Error(em(17));if(s)throw Error(em(9));try{s=!0,a=i(a,e)}finally{s=!1}return(o=l).forEach(e=>{e()}),e}return h({type:ew.INIT}),{dispatch:h,subscribe:d,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(em(10));i=e,h({type:ew.REPLACE})},[eb]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(em(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:d(t)}},[eb](){return this}}}}}(t,c,u(..."function"==typeof s?s(f):f()))}({reducer:dJ,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([fX.middleware,fQ.middleware,dH.middleware,dV.middleware,dX.middleware]),devTools:{serialize:{replacer:f0},name:"recharts-".concat(t)}})};function d0(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=n6(),o=(0,a.useRef)(null);return i?r:(null==o.current&&(o.current=dQ(t,n)),a.createElement(rd,{context:r5,store:o.current},r))}var d1=e=>{var{chartData:t}=e,r=r6(),n=n6();return(0,a.useEffect)(()=>n?()=>{}:(r(rM(t)),()=>{r(rM(void 0))}),[t,r,n]),null};function d2(e){var{layout:t,width:r,height:n,margin:i}=e;return r6(),n6(),null}function d3(e){return r6(),null}var d5=r(35529),d4=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],d6=["points","pathLength"],d8={svg:["viewBox","children"],polygon:d6,polyline:d6},d7=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],d9=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,a.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var n={};return Object.keys(r).forEach(e=>{d7.includes(e)&&(n[e]=t||(t=>r[e](r,t)))}),n},he=(e,t,r)=>n=>(e(t,r,n),null),ht=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];d7.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=he(a,t,r))}),n},hr=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",hn=null,hi=null,ha=e=>{if(e===hn&&Array.isArray(hi))return hi;var t=[];return a.Children.forEach(e,e=>{ed(e)||((0,d5.zv)(e)?t=t.concat(ha(e.props.children)):t.push(e))}),hi=t,hn=e,t};function ho(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>hr(e)):[hr(t)],ha(e).forEach(e=>{var t=et()(e,"type.displayName")||et()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var hl=(e,t,r,n)=>{var i,a=null!==(i=n&&(null==d8?void 0:d8[n]))&&void 0!==i?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||d4.includes(t))||r&&d7.includes(t)},hc=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;hl(null===(a=n)||void 0===a?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i},hs=()=>ne(e=>e.rootProps.accessibilityLayer),hu=["children","width","height","viewBox","className","style","title","desc"];function hf(){return(hf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hd=(0,a.forwardRef)((e,t)=>{var{children:r,width:n,height:i,viewBox:o,className:l,style:c,title:s,desc:u}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hu),d=o||{width:n,height:i,x:0,y:0},h=(0,X.$)("recharts-surface",l);return a.createElement("svg",hf({},hc(f,!0,"svg"),{className:h,width:n,height:i,style:c,viewBox:"".concat(d.x," ").concat(d.y," ").concat(d.width," ").concat(d.height),ref:t}),a.createElement("title",null,s),a.createElement("desc",null,u),r)}),hh=["children"];function hp(){return(hp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hy={width:"100%",height:"100%"},hv=(0,a.forwardRef)((e,t)=>{var r,n,i=ir(),o=ii(),l=hs();if(!l1(i)||!l1(o))return null;var{children:c,otherAttributes:s,title:u,desc:f}=e;return r="number"==typeof s.tabIndex?s.tabIndex:l?0:void 0,n="string"==typeof s.role?s.role:l?"application":void 0,a.createElement(hd,hp({},s,{title:u,desc:f,role:n,tabIndex:r,width:i,height:o,style:hy,ref:t}),c)}),hg=e=>{var{children:t}=e,r=ne(n7);if(!r)return null;var{width:n,height:i,y:o,x:l}=r;return a.createElement(hd,{width:n,height:i,x:l,y:o},t)},hm=(0,a.forwardRef)((e,t)=>{var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hh);return n6()?a.createElement(hg,null,r):a.createElement(hv,hp({ref:t},n),r)});new(r(38224));var hb=(0,a.createContext)(null),hx=(0,a.createContext)(null),hw=()=>(0,a.useContext)(hx);function hO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var hj=(0,a.forwardRef)((e,t)=>{var{children:r,className:n,height:i,onClick:o,onContextMenu:l,onDoubleClick:c,onMouseDown:s,onMouseEnter:u,onMouseLeave:f,onMouseMove:d,onMouseUp:h,onTouchEnd:p,onTouchMove:y,onTouchStart:v,style:g,width:m}=e,b=r6(),[x,w]=(0,a.useState)(null),[O,j]=(0,a.useState)(null);r6(),ne(cJ),ne(c0),r6(),ne(cQ),ne(fA),ic(),n9(),ne(e=>e.rootProps.className),ne(cJ),ne(c0),r6();var P=function(){r6();var[e,t]=(0,a.useState)(null);return ne(nY),t}(),A=(0,a.useCallback)(e=>{P(e),"function"==typeof t&&t(e),w(e),j(e)},[P,t,w,j]),S=(0,a.useCallback)(e=>{b(fZ(e)),b(dW({handler:o,reactEvent:e}))},[b,o]),k=(0,a.useCallback)(e=>{b(fJ(e)),b(dW({handler:u,reactEvent:e}))},[b,u]),M=(0,a.useCallback)(e=>{b(rx()),b(dW({handler:f,reactEvent:e}))},[b,f]),E=(0,a.useCallback)(e=>{b(fJ(e)),b(dW({handler:d,reactEvent:e}))},[b,d]),T=(0,a.useCallback)(()=>{b(dq())},[b]),_=(0,a.useCallback)(e=>{b(dK(e.key))},[b]),N=(0,a.useCallback)(e=>{b(dW({handler:l,reactEvent:e}))},[b,l]),C=(0,a.useCallback)(e=>{b(dW({handler:c,reactEvent:e}))},[b,c]),D=(0,a.useCallback)(e=>{b(dW({handler:s,reactEvent:e}))},[b,s]),I=(0,a.useCallback)(e=>{b(dW({handler:h,reactEvent:e}))},[b,h]),R=(0,a.useCallback)(e=>{b(dW({handler:v,reactEvent:e}))},[b,v]),L=(0,a.useCallback)(e=>{b(dZ(e)),b(dW({handler:y,reactEvent:e}))},[b,y]),z=(0,a.useCallback)(e=>{b(dW({handler:p,reactEvent:e}))},[b,p]);return a.createElement(hb.Provider,{value:x},a.createElement(hx.Provider,{value:O},a.createElement("div",{className:(0,X.$)("recharts-wrapper",n),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hO(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:m,height:i},g),role:"application",onClick:S,onContextMenu:N,onDoubleClick:C,onFocus:T,onKeyDown:_,onMouseDown:D,onMouseEnter:k,onMouseLeave:M,onMouseMove:E,onMouseUp:I,onTouchEnd:z,onTouchMove:L,onTouchStart:R,ref:A},r)))}),hP=(0,a.createContext)(void 0),hA=e=>{var{children:t}=e,[r]=(0,a.useState)("".concat(ec("recharts"),"-clip")),n=it();if(null==n)return null;var{left:i,top:o,height:l,width:c}=n;return a.createElement(hP.Provider,{value:r},a.createElement("defs",null,a.createElement("clipPath",{id:r},a.createElement("rect",{x:i,y:o,height:l,width:c}))),t)},hS=["children","className","width","height","style","compact","title","desc"],hk=(0,a.forwardRef)((e,t)=>{var{children:r,className:n,width:i,height:o,style:l,compact:c,title:s,desc:u}=e,f=hc(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hS),!1);return c?a.createElement(hm,{otherAttributes:f,title:s,desc:u},r):a.createElement(hj,{className:n,style:l,width:i,height:o,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},a.createElement(hm,{otherAttributes:f,title:s,desc:u,ref:t},a.createElement(hA,null,r)))});function hM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hE(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hM(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}var hT=["width","height"];function h_(){return(h_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hN={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},hC=(0,a.forwardRef)(function(e,t){var r,n=hE(e.categoricalChartProps,hN),{width:i,height:o}=n,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,hT);if(!l1(i)||!l1(o))return null;var{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,categoricalChartProps:d}=e;return a.createElement(d0,{preloadedState:{options:{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,eventEmitter:void 0}},reduxStoreName:null!==(r=d.id)&&void 0!==r?r:c},a.createElement(d1,{chartData:d.data}),a.createElement(d2,{width:i,height:o,layout:n.layout,margin:n.margin}),a.createElement(d3,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),a.createElement(hk,h_({},l,{width:i,height:o,ref:t})))}),hD=["axis","item"],hI=(0,a.forwardRef)((e,t)=>a.createElement(hC,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:hD,tooltipPayloadSearcher:t6,categoricalChartProps:e,ref:t})),hR={isSsr:!0};function hL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hL(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h$={widthCache:{},cacheCount:0},hB={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},hU="recharts_measurement_span",hF=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||hR.isSsr)return{width:0,height:0};var n=(Object.keys(t=hz({},r)).forEach(e=>{t[e]||delete t[e]}),t),i=JSON.stringify({text:e,copyStyle:n});if(h$.widthCache[i])return h$.widthCache[i];try{var a=document.getElementById(hU);a||((a=document.createElement("span")).setAttribute("id",hU),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=hz(hz({},hB),n);Object.assign(a.style,o),a.textContent="".concat(e);var l=a.getBoundingClientRect(),c={width:l.width,height:l.height};return h$.widthCache[i]=c,++h$.cacheCount>2e3&&(h$.cacheCount=0,h$.widthCache={}),c}catch(e){return{width:0,height:0}}};class hK{static create(e){return new hK(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}(function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):e[t]=r})(hK,"EPS",1e-4);var hq=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function hH(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t){if(void 0!==r&&!0!==r(e[i]))return;n.push(e[i])}return n}function hW(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function hV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hV(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hG(e,t,r){var n,{tick:i,ticks:a,viewBox:o,minTickGap:l,orientation:c,interval:s,tickFormatter:u,unit:f,angle:d}=e;if(!a||!a.length||!i)return[];if(ea(s)||hR.isSsr)return null!==(n=hH(a,(ea(s)?s:0)+1))&&void 0!==n?n:[];var h="top"===c||"bottom"===c?"width":"height",p=f&&"width"===h?hF(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},y=(e,n)=>{var i,a="function"==typeof u?u(e.value,n):e.value;return"width"===h?hq({width:(i=hF(a,{fontSize:t,letterSpacing:r})).width+p.width,height:i.height+p.height},d):hF(a,{fontSize:t,letterSpacing:r})[h]},v=a.length>=2?er(a[1].coordinate-a[0].coordinate):1,g=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(o,v,h);return"equidistantPreserveStart"===s?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=t,s=0,u=1,f=l;u<=o.length;)if(a=function(){var t,a=null==n?void 0:n[s];if(void 0===a)return{v:hH(n,u)};var o=s,d=()=>(void 0===t&&(t=r(a,o)),t),h=a.coordinate,p=0===s||hW(e,h,d,f,c);p||(s=0,f=l,u+=1),p&&(f=h+e*(d()/2+i),s+=u)}())return a.v;return[]}(v,g,y,a,l):("preserveStart"===s||"preserveStartEnd"===s?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:s}=t;if(a){var u=n[l-1],f=r(u,l-1),d=e*(u.coordinate+e*f/2-s);o[l-1]=u=hY(hY({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate}),hW(e,u.tickCoord,()=>f,c,s)&&(s=u.tickCoord-e*(f/2+i),o[l-1]=hY(hY({},u),{},{isShow:!0}))}for(var h=a?l-1:l,p=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var u=e*(a.coordinate-e*l()/2-c);o[t]=a=hY(hY({},a),{},{tickCoord:u<0?a.coordinate-u*e:a.coordinate})}else o[t]=a=hY(hY({},a),{},{tickCoord:a.coordinate});hW(e,a.tickCoord,l,c,s)&&(c=a.tickCoord+e*(l()/2+i),o[t]=hY(hY({},a),{},{isShow:!0}))},y=0;y<h;y++)p(y);return o}(v,g,y,a,l,"preserveStartEnd"===s):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,s=function(t){var n,s=a[t],u=()=>(void 0===n&&(n=r(s,t)),n);if(t===o-1){var f=e*(s.coordinate+e*u()/2-c);a[t]=s=hY(hY({},s),{},{tickCoord:f>0?s.coordinate-f*e:s.coordinate})}else a[t]=s=hY(hY({},s),{},{tickCoord:s.coordinate});hW(e,s.tickCoord,u,l,c)&&(c=s.tickCoord-e*(u()/2+i),a[t]=hY(hY({},s),{},{isShow:!0}))},u=o-1;u>=0;u--)s(u);return a}(v,g,y,a,l)).filter(e=>e.isShow)}function hZ(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var hX=["children","className"];function hJ(){return(hJ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hQ=a.forwardRef((e,t)=>{var{children:r,className:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hX),o=(0,X.$)("recharts-layer",n);return a.createElement("g",hJ({className:o},hc(i,!0),{ref:t}),r)}),h0=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,h1=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,h2=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,h3=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,h5={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},h4=Object.keys(h5);class h6{static parse(e){var t,[,r,n]=null!==(t=h3.exec(e))&&void 0!==t?t:[];return new h6(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,en(e)&&(this.unit=""),""===t||h2.test(t)||(this.num=NaN,this.unit=""),h4.includes(t)&&(this.num=e*h5[t],this.unit="px")}add(e){return this.unit!==e.unit?new h6(NaN,""):new h6(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new h6(NaN,""):new h6(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new h6(NaN,""):new h6(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new h6(NaN,""):new h6(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return en(this.num)}}function h8(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!==(r=h0.exec(t))&&void 0!==r?r:[],o=h6.parse(null!=n?n:""),l=h6.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return"NaN";t=t.replace(h0,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,[,u,f,d]=null!==(s=h1.exec(t))&&void 0!==s?s:[],h=h6.parse(null!=u?u:""),p=h6.parse(null!=d?d:""),y="+"===f?h.add(p):h.subtract(p);if(y.isNaN())return"NaN";t=t.replace(h1,y.toString())}return t}var h7=/\(([^()]*)\)/;function h9(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=h7.exec(r));){var[,n]=t;r=r.replace(h7,h8(n))}return r}(t),t=h8(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var pe=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],pt=["dx","dy","angle","className","breakAll"];function pr(){return(pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pn(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var pi=/[ \f\n\r\t\v\u2028\u2029]+/,pa=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];ed(t)||(i=r?t.toString().split(""):t.toString().split(pi));var a=i.map(e=>({word:e,width:hF(e,n).width})),o=r?0:hF("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch(e){return null}},po=(e,t,r,n,i)=>{var a,{maxLines:o,children:l,style:c,breakAll:s}=e,u=ea(o),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},d=f(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!u||i||!(d.length>o||h(d).width>Number(n)))return d;for(var p=e=>{var t=f(pa({breakAll:s,style:c,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>o||h(t).width>Number(n),t]},y=0,v=l.length-1,g=0;y<=v&&g<=l.length-1;){var m=Math.floor((y+v)/2),[b,x]=p(m-1),[w]=p(m);if(b||w||(y=m+1),b&&w&&(v=m-1),!b&&w){a=x;break}g++}return a||d},pl=e=>[{words:ed(e)?[]:e.toString().split(pi)}],pc=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!hR.isSsr){var l=pa({breakAll:a,children:n,style:i});if(!l)return pl(n);var{wordsWithComputedWidth:c,spaceWidth:s}=l;return po({breakAll:a,children:n,maxLines:o,style:i},c,s,t,r)}return pl(n)},ps="#808080",pu=(0,a.forwardRef)((e,t)=>{var r,{x:n=0,y:i=0,lineHeight:o="1em",capHeight:l="0.71em",scaleToFit:c=!1,textAnchor:s="start",verticalAnchor:u="end",fill:f=ps}=e,d=pn(e,pe),h=(0,a.useMemo)(()=>pc({breakAll:d.breakAll,children:d.children,maxLines:d.maxLines,scaleToFit:c,style:d.style,width:d.width}),[d.breakAll,d.children,d.maxLines,c,d.style,d.width]),{dx:p,dy:y,angle:v,className:g,breakAll:m}=d,b=pn(d,pt);if(!eo(n)||!eo(i))return null;var x=n+(ea(p)?p:0),w=i+(ea(y)?y:0);switch(u){case"start":r=h9("calc(".concat(l,")"));break;case"middle":r=h9("calc(".concat((h.length-1)/2," * -").concat(o," + (").concat(l," / 2))"));break;default:r=h9("calc(".concat(h.length-1," * -").concat(o,")"))}var O=[];if(c){var j=h[0].width,{width:P}=d;O.push("scale(".concat(ea(P)?P/j:1,")"))}return v&&O.push("rotate(".concat(v,", ").concat(x,", ").concat(w,")")),O.length&&(b.transform=O.join(" ")),a.createElement("text",pr({},hc(b,!0),{ref:t,x:x,y:w,className:(0,X.$)("recharts-text",g),textAnchor:s,fill:f.includes("url")?ps:f}),h.map((e,t)=>{var n=e.words.join(m?"":" ");return a.createElement("tspan",{x:x,dy:0===t?r:o,key:"".concat(n,"-").concat(t)},n)}))});pu.displayName="Text";var pf=["offset"],pd=["labelRef"];function ph(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function pp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function py(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pp(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pv(){return(pv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var pg=e=>{var{value:t,formatter:r}=e,n=ed(e.children)?t:e.children;return"function"==typeof r?r(n):n},pm=e=>null!=e&&"function"==typeof e,pb=(e,t)=>er(t-e)*Math.min(Math.abs(t-e),360),px=(e,t,r)=>{var n,i,{position:o,viewBox:l,offset:c,className:s}=e,{cx:u,cy:f,innerRadius:d,outerRadius:h,startAngle:p,endAngle:y,clockWise:v}=l,g=(d+h)/2,m=pb(p,y),b=m>=0?1:-1;"insideStart"===o?(n=p+b*c,i=v):"insideEnd"===o?(n=y-b*c,i=!v):"end"===o&&(n=y+b*c,i=v),i=m<=0?i:!i;var x=ny(u,f,g,n),w=ny(u,f,g,n+(i?1:-1)*359),O="M".concat(x.x,",").concat(x.y,"\n    A").concat(g,",").concat(g,",0,1,").concat(i?0:1,",\n    ").concat(w.x,",").concat(w.y),j=ed(e.id)?ec("recharts-radial-line-"):e.id;return a.createElement("text",pv({},r,{dominantBaseline:"central",className:(0,X.$)("recharts-radial-bar-label",s)}),a.createElement("defs",null,a.createElement("path",{id:j,d:O})),a.createElement("textPath",{xlinkHref:"#".concat(j)},t))},pw=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:c,endAngle:s}=t,u=(c+s)/2;if("outside"===n){var{x:f,y:d}=ny(i,a,l+r,u);return{x:f,y:d,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:h,y:p}=ny(i,a,(o+l)/2,u);return{x:h,y:p,textAnchor:"middle",verticalAnchor:"middle"}},pO=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:c}=t,s=c>=0?1:-1,u=s*n,f=s>0?"end":"start",d=s>0?"start":"end",h=l>=0?1:-1,p=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===i)return py(py({},{x:a+l/2,y:o-s*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return py(py({},{x:a+l/2,y:o+c+u,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(o+c),0),width:l}:{});if("left"===i){var g={x:a-p,y:o+c/2,textAnchor:y,verticalAnchor:"middle"};return py(py({},g),r?{width:Math.max(g.x-r.x,0),height:c}:{})}if("right"===i){var m={x:a+l+p,y:o+c/2,textAnchor:v,verticalAnchor:"middle"};return py(py({},m),r?{width:Math.max(r.x+r.width-m.x,0),height:c}:{})}var b=r?{width:l,height:c}:{};return"insideLeft"===i?py({x:a+p,y:o+c/2,textAnchor:v,verticalAnchor:"middle"},b):"insideRight"===i?py({x:a+l-p,y:o+c/2,textAnchor:y,verticalAnchor:"middle"},b):"insideTop"===i?py({x:a+l/2,y:o+u,textAnchor:"middle",verticalAnchor:d},b):"insideBottom"===i?py({x:a+l/2,y:o+c-u,textAnchor:"middle",verticalAnchor:f},b):"insideTopLeft"===i?py({x:a+p,y:o+u,textAnchor:v,verticalAnchor:d},b):"insideTopRight"===i?py({x:a+l-p,y:o+u,textAnchor:y,verticalAnchor:d},b):"insideBottomLeft"===i?py({x:a+p,y:o+c-u,textAnchor:v,verticalAnchor:f},b):"insideBottomRight"===i?py({x:a+l-p,y:o+c-u,textAnchor:y,verticalAnchor:f},b):i&&"object"==typeof i&&(ea(i.x)||ei(i.x))&&(ea(i.y)||ei(i.y))?py({x:a+es(i.x,l),y:o+es(i.y,c),textAnchor:"end",verticalAnchor:"end"},b):py({x:a+l/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},b)},pj=e=>"cx"in e&&ea(e.cx);function pP(e){var t,{offset:r=5}=e,n=py({offset:r},ph(e,pf)),{viewBox:i,position:o,value:l,children:c,content:s,className:u="",textBreakAll:f,labelRef:d}=n,h=n9(),p=i||h;if(!p||ed(l)&&ed(c)&&!(0,a.isValidElement)(s)&&"function"!=typeof s)return null;if((0,a.isValidElement)(s)){var{labelRef:y}=n,v=ph(n,pd);return(0,a.cloneElement)(s,v)}if("function"==typeof s){if(t=(0,a.createElement)(s,n),(0,a.isValidElement)(t))return t}else t=pg(n);var g=pj(p),m=hc(n,!0);if(g&&("insideStart"===o||"insideEnd"===o||"end"===o))return px(n,t,m);var b=g?pw(n):pO(n,p);return a.createElement(pu,pv({ref:d,className:(0,X.$)("recharts-label",u)},m,b,{breakAll:f}),t)}pP.displayName="Label";var pA=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:c,outerRadius:s,x:u,y:f,top:d,left:h,width:p,height:y,clockWise:v,labelViewBox:g}=e;if(g)return g;if(ea(p)&&ea(y)){if(ea(u)&&ea(f))return{x:u,y:f,width:p,height:y};if(ea(d)&&ea(h))return{x:d,y:h,width:p,height:y}}return ea(u)&&ea(f)?{x:u,y:f,width:0,height:0}:ea(t)&&ea(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||l||o||0,clockWise:v}:e.viewBox?e.viewBox:void 0},pS=(e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return!0===e?a.createElement(pP,pv({key:"label-implicit"},n)):eo(e)?a.createElement(pP,pv({key:"label-implicit",value:e},n)):(0,a.isValidElement)(e)?e.type===pP?(0,a.cloneElement)(e,py({key:"label-implicit"},n)):a.createElement(pP,pv({key:"label-implicit",content:e},n)):pm(e)?a.createElement(pP,pv({key:"label-implicit",content:e},n)):e&&"object"==typeof e?a.createElement(pP,pv({},e,{key:"label-implicit"},n)):null};pP.parseViewBox=pA,pP.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:n,labelRef:i}=e,o=pA(e),l=ho(n,pP).map((e,r)=>(0,a.cloneElement)(e,{viewBox:t||o,key:"label-".concat(r)}));return r?[pS(e.label,t||o,i),...l]:l};var pk=["viewBox"],pM=["viewBox"];function pE(){return(pE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pT(Object(r),!0).forEach(function(t){pC(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pN(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function pC(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class pD extends a.Component{constructor(e){super(e),this.tickRefs=a.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=pN(e,pk),i=this.props,{viewBox:a}=i,o=pN(i,pM);return!hZ(r,a)||!hZ(n,o)||!hZ(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:s,height:u,orientation:f,tickSize:d,mirror:h,tickMargin:p}=this.props,y=h?-1:1,v=e.tickSize||d,g=ea(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(i=c+ +!h*u)-y*v)-y*p,a=g;break;case"left":n=i=e.coordinate,a=(t=(r=l+ +!h*s)-y*v)-y*p,o=g;break;case"right":n=i=e.coordinate,a=(t=(r=l+ +h*s)+y*v)+y*p,o=g;break;default:t=r=e.coordinate,o=(n=(i=c+ +h*u)+y*v)+y*p,a=g}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:n,orientation:i,mirror:o,axisLine:l}=this.props,c=p_(p_(p_({},hc(this.props,!1)),hc(l,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var s=+("top"===i&&!o||"bottom"===i&&o);c=p_(p_({},c),{},{x1:e,y1:t+s*n,x2:e+r,y2:t+s*n})}else{var u=+("left"===i&&!o||"right"===i&&o);c=p_(p_({},c),{},{x1:e+u*r,y1:t,x2:e+u*r,y2:t+n})}return a.createElement("line",pE({},c,{className:(0,X.$)("recharts-cartesian-axis-line",et()(l,"className"))}))}static renderTickItem(e,t,r){var n,i=(0,X.$)(t.className,"recharts-cartesian-axis-tick-value");if(a.isValidElement(e))n=a.cloneElement(e,p_(p_({},t),{},{className:i}));else if("function"==typeof e)n=e(p_(p_({},t),{},{className:i}));else{var o="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(o=(0,X.$)(o,e.className)),n=a.createElement(pu,pE({},t,{className:o}),r)}return n}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:n,stroke:i,tick:o,tickFormatter:l,unit:c}=this.props,s=hG(p_(p_({},this.props),{},{ticks:r}),e,t),u=this.getTickTextAnchor(),f=this.getTickVerticalAnchor(),d=hc(this.props,!1),h=hc(o,!1),p=p_(p_({},d),{},{fill:"none"},hc(n,!1)),y=s.map((e,t)=>{var{line:r,tick:y}=this.getTickLineCoord(e),v=p_(p_(p_(p_({textAnchor:u,verticalAnchor:f},d),{},{stroke:"none",fill:i},h),y),{},{index:t,payload:e,visibleTicksCount:s.length,tickFormatter:l});return a.createElement(hQ,pE({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},ht(this.props,e,t)),n&&a.createElement("line",pE({},p,r,{className:(0,X.$)("recharts-cartesian-axis-tick-line",et()(n,"className"))})),o&&pD.renderTickItem(o,v,"".concat("function"==typeof l?l(e.value,t):e.value).concat(c||"")))});return y.length>0?a.createElement("g",{className:"recharts-cartesian-axis-ticks"},y):null}render(){var{axisLine:e,width:t,height:r,className:n,hide:i}=this.props;if(i)return null;var{ticks:o}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:a.createElement(hQ,{className:(0,X.$)("recharts-cartesian-axis",n),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,o),pP.renderCallByParent(this.props))}}pC(pD,"displayName","CartesianAxis"),pC(pD,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var pI=["x1","y1","x2","y2","key"],pR=["offset"],pL=["xAxisId","yAxisId"],pz=["xAxisId","yAxisId"];function p$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pB(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p$(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pU(){return(pU=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pF(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var pK=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:n,y:i,width:o,height:l,ry:c}=e;return a.createElement("rect",{x:n,y:i,ry:c,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function pq(e,t){var r;if(a.isValidElement(e))r=a.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:n,y1:i,x2:o,y2:l,key:c}=t,s=hc(pF(t,pI),!1),{offset:u}=s,f=pF(s,pR);r=a.createElement("line",pU({},f,{x1:n,y1:i,x2:o,y2:l,fill:"none",key:c}))}return r}function pH(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:o,yAxisId:l}=e,c=pF(e,pL),s=i.map((e,i)=>pq(n,pB(pB({},c),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(i),index:i})));return a.createElement("g",{className:"recharts-cartesian-grid-horizontal"},s)}function pW(e){var{y:t,height:r,vertical:n=!0,verticalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:o,yAxisId:l}=e,c=pF(e,pz),s=i.map((e,i)=>pq(n,pB(pB({},c),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(i),index:i})));return a.createElement("g",{className:"recharts-cartesian-grid-vertical"},s)}function pV(e){var{horizontalFill:t,fillOpacity:r,x:n,y:i,width:o,height:l,horizontalPoints:c,horizontal:s=!0}=e;if(!s||!t||!t.length)return null;var u=c.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==u[0]&&u.unshift(0);var f=u.map((e,c)=>{var s=u[c+1]?u[c+1]-e:i+l-e;if(s<=0)return null;var f=c%t.length;return a.createElement("rect",{key:"react-".concat(c),y:e,x:n,height:s,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function pY(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:i,y:o,width:l,height:c,verticalPoints:s}=e;if(!t||!r||!r.length)return null;var u=s.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==u[0]&&u.unshift(0);var f=u.map((e,t)=>{var s=u[t+1]?u[t+1]-e:i+l-e;if(s<=0)return null;var f=t%r.length;return a.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:s,height:c,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var pG=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return nM(hG(pB(pB(pB({},pD.defaultProps),r),{},{ticks:nE(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},pZ=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return nM(hG(pB(pB(pB({},pD.defaultProps),r),{},{ticks:nE(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},pX={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function pJ(e){var t=ir(),r=ii(),n=it(),i=pB(pB({},hE(e,pX)),{},{x:ea(e.x)?e.x:n.left,y:ea(e.y)?e.y:n.top,width:ea(e.width)?e.width:n.width,height:ea(e.height)?e.height:n.height}),{xAxisId:o,yAxisId:l,x:c,y:s,width:u,height:f,syncWithTicks:d,horizontalValues:h,verticalValues:p}=i,y=n6(),v=ne(e=>u$(e,"xAxis",o,y)),g=ne(e=>u$(e,"yAxis",l,y));if(!ea(u)||u<=0||!ea(f)||f<=0||!ea(c)||c!==+c||!ea(s)||s!==+s)return null;var m=i.verticalCoordinatesGenerator||pG,b=i.horizontalCoordinatesGenerator||pZ,{horizontalPoints:x,verticalPoints:w}=i;if((!x||!x.length)&&"function"==typeof b){var O=h&&h.length,j=b({yAxis:g?pB(pB({},g),{},{ticks:O?h:g.ticks}):void 0,width:t,height:r,offset:n},!!O||d);ep(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(x=j)}if((!w||!w.length)&&"function"==typeof m){var P=p&&p.length,A=m({xAxis:v?pB(pB({},v),{},{ticks:P?p:v.ticks}):void 0,width:t,height:r,offset:n},!!P||d);ep(Array.isArray(A),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof A,"]")),Array.isArray(A)&&(w=A)}return a.createElement("g",{className:"recharts-cartesian-grid"},a.createElement(pK,{fill:i.fill,fillOpacity:i.fillOpacity,x:i.x,y:i.y,width:i.width,height:i.height,ry:i.ry}),a.createElement(pV,pU({},i,{horizontalPoints:x})),a.createElement(pY,pU({},i,{verticalPoints:w})),a.createElement(pH,pU({},i,{offset:n,horizontalPoints:x,xAxis:v,yAxis:g})),a.createElement(pW,pU({},i,{offset:n,verticalPoints:w,xAxis:v,yAxis:g})))}pJ.displayName="CartesianGrid";var pQ=["children"],p0=["dangerouslySetInnerHTML","ticks"];function p1(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p2(){return(p2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p3(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function p5(e){r6();var t=(0,a.useMemo)(()=>{var{children:t}=e;return p3(e,pQ)},[e]),r=ne(e=>sh(e,t.id));return t===r?e.children:null}var p4=e=>{var{xAxisId:t,className:r}=e,n=ne(n5),i=n6(),o="xAxis",l=ne(e=>um(e,o,t,i)),c=ne(e=>uB(e,o,t,i)),s=ne(e=>uS(e,t)),u=ne(e=>u_(e,t));if(null==s||null==u)return null;var{dangerouslySetInnerHTML:f,ticks:d}=e,h=p3(e,p0);return a.createElement(pD,p2({},h,{scale:l,x:u.x,y:u.y,width:s.width,height:s.height,className:(0,X.$)("recharts-".concat(o," ").concat(o),r),viewBox:n,ticks:c}))},p6=e=>{var t,r,n,i,o;return a.createElement(p5,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(n=e.angle)&&void 0!==n?n:0,minTickGap:null!==(i=e.minTickGap)&&void 0!==i?i:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter},a.createElement(p4,e))};class p8 extends a.Component{render(){return a.createElement(p6,this.props)}}p1(p8,"displayName","XAxis"),p1(p8,"defaultProps",{allowDataOverflow:sd.allowDataOverflow,allowDecimals:sd.allowDecimals,allowDuplicatedCategory:sd.allowDuplicatedCategory,height:sd.height,hide:!1,mirror:sd.mirror,orientation:sd.orientation,padding:sd.padding,reversed:sd.reversed,scale:sd.scale,tickCount:sd.tickCount,type:sd.type,xAxisId:0});var p7=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},p9=["dangerouslySetInnerHTML","ticks"];function ye(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yt(){return(yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yr(e){return r6(),null}var yn=e=>{var t,{yAxisId:r,className:n,width:i,label:o}=e,l=(0,a.useRef)(null),c=(0,a.useRef)(null),s=ne(n5),u=n6(),f=r6(),d="yAxis",h=ne(e=>um(e,d,r,u)),p=ne(e=>uC(e,r)),y=ne(e=>uN(e,r)),v=ne(e=>uB(e,d,r,u));if((0,a.useLayoutEffect)(()=>{if(!("auto"!==i||!p||pm(o)||(0,a.isValidElement)(o))){var e,t=l.current,n=null==t||null===(e=t.tickRefs)||void 0===e?void 0:e.current,{tickSize:s,tickMargin:u}=t.props,d=p7({ticks:n,label:c.current,labelGapWithTick:5,tickSize:s,tickMargin:u});Math.round(p.width)!==Math.round(d)&&f(de({id:r,width:d}))}},[l,null==l||null===(t=l.current)||void 0===t||null===(t=t.tickRefs)||void 0===t?void 0:t.current,null==p?void 0:p.width,p,f,o,r,i]),null==p||null==y)return null;var{dangerouslySetInnerHTML:g,ticks:m}=e,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,p9);return a.createElement(pD,yt({},b,{ref:l,labelRef:c,scale:h,x:y.x,y:y.y,width:p.width,height:p.height,className:(0,X.$)("recharts-".concat(d," ").concat(d),n),viewBox:s,ticks:v}))},yi=e=>{var t,r,n,i,o;return a.createElement(a.Fragment,null,a.createElement(yr,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(n=e.angle)&&void 0!==n?n:0,minTickGap:null!==(i=e.minTickGap)&&void 0!==i?i:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter}),a.createElement(yn,e))},ya={allowDataOverflow:sp.allowDataOverflow,allowDecimals:sp.allowDecimals,allowDuplicatedCategory:sp.allowDuplicatedCategory,hide:!1,mirror:sp.mirror,orientation:sp.orientation,padding:sp.padding,reversed:sp.reversed,scale:sp.scale,tickCount:sp.tickCount,type:sp.type,width:sp.width,yAxisId:0};class yo extends a.Component{render(){return a.createElement(yi,this.props)}}function yl(e){return r6(),(0,a.useRef)(null),null}function yc(e){return r6(),null}ye(yo,"displayName","YAxis"),ye(yo,"defaultProps",ya);var ys=["children"],yu=()=>{},yf=(0,a.createContext)({addErrorBar:yu,removeErrorBar:yu}),yd=(0,a.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function yh(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ys);return a.createElement(yd.Provider,{value:r},t)}var yp=()=>(0,a.useContext)(yd),yy=e=>{var{children:t,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:o,data:l,stackId:c,hide:s,type:u,barSize:f}=e,[d,h]=a.useState([]),p=(0,a.useCallback)(e=>{h(t=>[...t,e])},[h]),y=(0,a.useCallback)(e=>{h(t=>t.filter(t=>t!==e))},[h]),v=n6();return a.createElement(yf.Provider,{value:{addErrorBar:p,removeErrorBar:y}},a.createElement(yl,{type:u,data:l,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:o,errorBars:d,stackId:c,hide:s,barSize:f,isPanorama:v}),t)};function yv(e){var{addErrorBar:t,removeErrorBar:r}=(0,a.useContext)(yf);return null}var yg=e=>{var t=n6();return ne(r=>uF(r,"xAxis",e,t))},ym=e=>{var t=n6();return ne(r=>uF(r,"yAxis",e,t))},yb=r(13905),yx=r.n(yb),yw=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],yO=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),yj=(e,t)=>r=>yO(yw(e,t),r),yP=(e,t)=>r=>yO([...yw(e,t).map((e,t)=>e*t).slice(1),0],r),yA=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var c=yj(e,t),s=yj(r,n),u=yP(e,t),f=e=>e>1?1:e<0?0:e,d=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=c(r)-t,a=u(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=f(r-i/a)}return s(r)};return d.isStepper=!1,d},yS=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},yk=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return yA(e);case"spring":return yS();default:if("cubic-bezier"===e.split("(")[0])return yA(e)}return"function"==typeof e?e:null};function yM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yM(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var yT=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),y_=(e,t,r)=>e.map(e=>"".concat(yT(e)," ").concat(t,"ms ").concat(r)).join(","),yN=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),yC=(e,t)=>Object.keys(t).reduce((r,n)=>yE(yE({},r),{},{[n]:e(n,t[n])}),{});function yD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yD(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var yR=(e,t,r)=>e+(t-e)*r,yL=e=>{var{from:t,to:r}=e;return t!==r},yz=(e,t,r)=>{var n=yC((t,r)=>{if(yL(r)){var[n,i]=e(r.from,r.to,r.velocity);return yI(yI({},r),{},{from:n,velocity:i})}return r},t);return r<1?yC((e,t)=>yL(t)?yI(yI({},t),{},{velocity:yR(t.velocity,n[e].velocity,r),from:yR(t.from,n[e].from,r)}):t,t):yz(e,n,r-1)};let y$=(e,t,r,n,i,a)=>{var o=yN(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>yI(yI({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),c=()=>yC((e,t)=>t.from,l),s=()=>!Object.values(l).filter(yL).length,u=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;l=yz(r,l,d),i(yI(yI(yI({},e),t),c())),o=n,s()||(u=a.setTimeout(f))};return()=>(u=a.setTimeout(f),()=>{u()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,c=null,s=i.reduce((r,n)=>yI(yI({},r),{},{[n]:[e[n],t[n]]}),{}),u=i=>{l||(l=i);var f=(i-l)/n,d=yC((e,t)=>yR(...t,r(f)),s);if(a(yI(yI(yI({},e),t),d)),f<1)c=o.setTimeout(u);else{var h=yC((e,t)=>yR(...t,r(1)),s);a(yI(yI(yI({},e),t),h))}};return()=>(c=o.setTimeout(u),()=>{c()})}(e,t,r,n,o,i,a)};class yB{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var yU=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function yF(){return(yF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yK(Object(r),!0).forEach(function(t){yH(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yH(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class yW extends a.PureComponent{constructor(e,t){super(e,t),yH(this,"mounted",!1),yH(this,"manager",null),yH(this,"stopJSAnimation",null),yH(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!yx()(e.to,a)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?o:e.to;this.state&&l&&(n&&l[n]!==s||!n&&l!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(yq(yq({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,c=y$(t,r,yk(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof c||"spring"===a){this.runJSAnimation(e);return}var s=n?{[n]:i}:i,u=y_(Object.keys(s),r,a);this.manager.start([o,t,yq(yq({},s),{},{transition:u}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:n,attributeName:i,easing:o,isActive:l,from:c,to:s,canBegin:u,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,yU),v=a.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!l||0===v||n<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,a.cloneElement)(e,yq(yq({},y),{},{style:yq(yq({},t),g),className:r}))};return 1===v?m(a.Children.only(t)):a.createElement("div",null,a.Children.map(t,e=>m(e)))}}yH(yW,"displayName","Animate"),yH(yW,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var yV=(0,a.createContext)(null);function yY(e){var t,r,n,i,o,l,c,s=(0,a.useContext)(yV);return a.createElement(yW,yF({},e,{animationManager:null!==(l=null!==(c=e.animationManager)&&void 0!==c?c:s)&&void 0!==l?l:(t=new yB,r=()=>null,n=!1,i=null,o=e=>{if(!n){if(Array.isArray(e)){if(!e.length)return;var[a,...l]=e;if("number"==typeof a){i=t.setTimeout(o.bind(null,l),a);return}o(a),i=t.setTimeout(o.bind(null,l));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{n=!0},start:e=>{n=!1,i&&(i(),i=null),o(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}var yG=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function yZ(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yX(){return(yX=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yJ(e){var{direction:t,width:r,dataKey:n,isAnimationActive:i,animationBegin:o,animationDuration:l,animationEasing:c}=e,s=hc(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,yG),!1),{data:u,dataPointFormatter:f,xAxisId:d,yAxisId:h,errorBarOffset:p}=yp(),y=yg(d),v=ym(h);if((null==y?void 0:y.scale)==null||(null==v?void 0:v.scale)==null||null==u||"x"===t&&"number"!==y.type)return null;var g=u.map(e=>{var u,d,{x:h,y:g,value:m,errorVal:b}=f(e,n,t);if(!b)return null;var x=[];if(Array.isArray(b)?[u,d]=b:u=d=b,"x"===t){var{scale:w}=y,O=g+p,j=O+r,P=O-r,A=w(m-u),S=w(m+d);x.push({x1:S,y1:j,x2:S,y2:P}),x.push({x1:A,y1:O,x2:S,y2:O}),x.push({x1:A,y1:j,x2:A,y2:P})}else if("y"===t){var{scale:k}=v,M=h+p,E=M-r,T=M+r,_=k(m-u),N=k(m+d);x.push({x1:E,y1:N,x2:T,y2:N}),x.push({x1:M,y1:_,x2:M,y2:N}),x.push({x1:E,y1:_,x2:T,y2:_})}var C="".concat(h+p,"px ").concat(g+p,"px");return a.createElement(hQ,yX({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},s),x.map(e=>{var t=i?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return a.createElement(yY,{from:{transform:"scaleY(0)",transformOrigin:C},to:{transform:"scaleY(1)",transformOrigin:C},begin:o,easing:c,isActive:i,duration:l,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:C}},a.createElement("line",yX({},e,{style:t})))}))});return a.createElement(hQ,{className:"recharts-errorBars"},g)}var yQ=(0,a.createContext)(void 0);function y0(e){var{direction:t,children:r}=e;return a.createElement(yQ.Provider,{value:t},r)}var y1={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function y2(e){var t,r,n=(t=e.direction,r=(0,a.useContext)(yQ),null!=t?t:null!=r?r:"x"),{width:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s}=hE(e,y1);return a.createElement(a.Fragment,null,a.createElement(yv,{dataKey:e.dataKey,direction:n}),a.createElement(yJ,yX({},e,{direction:n,width:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s})))}class y3 extends a.Component{render(){return a.createElement(y2,this.props)}}yZ(y3,"defaultProps",y1),yZ(y3,"displayName","ErrorBar");var y5=e=>null;y5.displayName="Cell";var y4=r(32157),y6=r.n(y4),y8=["valueAccessor"],y7=["data","dataKey","clockWise","id","textBreakAll"];function y9(){return(y9=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ve(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ve(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ve(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vr(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var vn=e=>Array.isArray(e.value)?y6()(e.value):e.value;function vi(e){var{valueAccessor:t=vn}=e,r=vr(e,y8),{data:n,dataKey:i,clockWise:o,id:l,textBreakAll:c}=r,s=vr(r,y7);return n&&n.length?a.createElement(hQ,{className:"recharts-label-list"},n.map((e,r)=>{var n=ed(i)?t(e,r):nP(e&&e.payload,i),u=ed(l)?{}:{id:"".concat(l,"-").concat(r)};return a.createElement(pP,y9({},hc(e,!0),s,u,{parentViewBox:e.parentViewBox,value:n,textBreakAll:c,viewBox:pP.parseViewBox(ed(o)?e:vt(vt({},e),{},{clockWise:o})),key:"label-".concat(r),index:r}))})):null}vi.displayName="LabelList",vi.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i}=e,o=ho(i,vi).map((e,r)=>(0,a.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return n?[(r=e.label)?!0===r?a.createElement(vi,{key:"labelList-implicit",data:t}):a.isValidElement(r)||pm(r)?a.createElement(vi,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?a.createElement(vi,y9({data:t},r,{key:"labelList-implicit"})):null:null,...o]:o};var va=r(63090),vo=r.n(va);function vl(){return(vl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var vc=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,s=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&i instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*u[0]),u[0]>0&&(a+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(s,",").concat(e+c*u[0],",").concat(t)),a+="L ".concat(e+r-c*u[1],",").concat(t),u[1]>0&&(a+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+l*u[1])),a+="L ".concat(e+r,",").concat(t+n-l*u[2]),u[2]>0&&(a+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(s,",\n        ").concat(e+r-c*u[2],",").concat(t+n)),a+="L ".concat(e+c*u[3],",").concat(t+n),u[3]>0&&(a+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-l*u[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},vs={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},vu=e=>{var t=hE(e,vs),r=(0,a.useRef)(null),[n,i]=(0,a.useState)(-1);(0,a.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:o,y:l,width:c,height:s,radius:u,className:f}=t,{animationEasing:d,animationDuration:h,animationBegin:p,isAnimationActive:y,isUpdateAnimationActive:v}=t;if(o!==+o||l!==+l||c!==+c||s!==+s||0===c||0===s)return null;var g=(0,X.$)("recharts-rectangle",f);return v?a.createElement(yY,{canBegin:n>0,from:{width:c,height:s,x:o,y:l},to:{width:c,height:s,x:o,y:l},duration:h,animationEasing:d,isActive:v},e=>{var{width:i,height:o,x:l,y:c}=e;return a.createElement(yY,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,isActive:y,easing:d},a.createElement("path",vl({},hc(t,!0),{className:g,d:vc(l,c,i,o,u),ref:r})))}):a.createElement("path",vl({},hc(t,!0),{className:g,d:vc(o,l,c,s,u)}))};function vf(){return(vf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var vd=(e,t,r,n,i)=>{var a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+i)+"L ".concat(e+r-a/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},vh={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},vp=e=>{var t=hE(e,vh),r=(0,a.useRef)(),[n,i]=(0,a.useState)(-1);(0,a.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:o,y:l,upperWidth:c,lowerWidth:s,height:u,className:f}=t,{animationEasing:d,animationDuration:h,animationBegin:p,isUpdateAnimationActive:y}=t;if(o!==+o||l!==+l||c!==+c||s!==+s||u!==+u||0===c&&0===s||0===u)return null;var v=(0,X.$)("recharts-trapezoid",f);return y?a.createElement(yY,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:u,x:o,y:l},to:{upperWidth:c,lowerWidth:s,height:u,x:o,y:l},duration:h,animationEasing:d,isActive:y},e=>{var{upperWidth:i,lowerWidth:o,height:l,x:c,y:s}=e;return a.createElement(yY,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,easing:d},a.createElement("path",vf({},hc(t,!0),{className:v,d:vd(c,s,i,o,l),ref:r})))}):a.createElement("g",null,a.createElement("path",vf({},hc(t,!0),{className:v,d:vd(o,l,c,s,u)})))};function vy(){return(vy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var vv=(e,t)=>er(t-e)*Math.min(Math.abs(t-e),359.999),vg=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:c}=e,s=l*(o?1:-1)+n,u=Math.asin(l/s)/nh,f=c?i:i+a*u;return{center:ny(t,r,s,f),circleTangency:ny(t,r,n,f),lineTangency:ny(t,r,s*Math.cos(u*nh),c?i-a*u:i),theta:u}},vm=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=vv(a,o),c=a+l,s=ny(t,r,i,a),u=ny(t,r,i,c),f="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>c),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var d=ny(t,r,n,a),h=ny(t,r,n,c);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=c),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},vb=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:s}=e,u=er(s-c),{circleTangency:f,lineTangency:d,theta:h}=vg({cx:t,cy:r,radius:i,angle:c,sign:u,cornerRadius:a,cornerIsExternal:l}),{circleTangency:p,lineTangency:y,theta:v}=vg({cx:t,cy:r,radius:i,angle:s,sign:-u,cornerRadius:a,cornerIsExternal:l}),g=l?Math.abs(c-s):Math.abs(c-s)-h-v;if(g<0)return o?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):vm({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:s});var m="M ".concat(d.x,",").concat(d.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:b,lineTangency:x,theta:w}=vg({cx:t,cy:r,radius:n,angle:c,sign:u,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:O,lineTangency:j,theta:P}=vg({cx:t,cy:r,radius:n,angle:s,sign:-u,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),A=l?Math.abs(c-s):Math.abs(c-s)-w-P;if(A<0&&0===a)return"".concat(m,"L").concat(t,",").concat(r,"Z");m+="L".concat(j.x,",").concat(j.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(O.x,",").concat(O.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(A>180),",").concat(+(u>0),",").concat(b.x,",").concat(b.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(x.x,",").concat(x.y,"Z")}else m+="L".concat(t,",").concat(r,"Z");return m},vx={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},vw=e=>{var t,r=hE(e,vx),{cx:n,cy:i,innerRadius:o,outerRadius:l,cornerRadius:c,forceCornerRadius:s,cornerIsExternal:u,startAngle:f,endAngle:d,className:h}=r;if(l<o||f===d)return null;var p=(0,X.$)("recharts-sector",h),y=l-o,v=es(c,y,0,!0);return t=v>0&&360>Math.abs(f-d)?vb({cx:n,cy:i,innerRadius:o,outerRadius:l,cornerRadius:Math.min(v,y/2),forceCornerRadius:s,cornerIsExternal:u,startAngle:f,endAngle:d}):vm({cx:n,cy:i,innerRadius:o,outerRadius:l,startAngle:f,endAngle:d}),a.createElement("path",vy({},hc(r,!0),{className:p,d:t}))};let vO=Math.cos,vj=Math.sin,vP=Math.sqrt,vA=Math.PI,vS=2*vA,vk={draw(e,t){let r=vP(t/vA);e.moveTo(r,0),e.arc(0,0,r,0,vS)}},vM=vP(1/3),vE=2*vM,vT=vj(vA/10)/vj(7*vA/10),v_=vj(vS/10)*vT,vN=-vO(vS/10)*vT,vC=vP(3),vD=vP(3)/2,vI=1/vP(12),vR=(vI/2+1)*3,vL=Math.PI,vz=2*vL,v$=vz-1e-6;function vB(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class vU{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?vB:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return vB;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e=+e,t=+t,r=+r,n=+n,(i=+i)<0)throw Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-e,c=n-t,s=a-e,u=o-t,f=s*s+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6){if(Math.abs(u*l-c*s)>1e-6&&i){let d=r-a,h=n-o,p=l*l+c*c,y=Math.sqrt(p),v=Math.sqrt(f),g=i*Math.tan((vL-Math.acos((p+f-(d*d+h*h))/(2*y*v)))/2),m=g/v,b=g/y;Math.abs(m-1)>1e-6&&this._append`L${e+m*s},${t+m*u}`,this._append`A${i},${i},0,0,${+(u*d>s*h)},${this._x1=e+b*l},${this._y1=t+b*c}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,n,i,a){if(e=+e,t=+t,a=!!a,(r=+r)<0)throw Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),c=e+o,s=t+l,u=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${c},${s}`:(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-s)>1e-6)&&this._append`L${c},${s}`,r&&(f<0&&(f=f%vz+vz),f>v$?this._append`A${r},${r},0,1,${u},${e-o},${t-l}A${r},${r},0,1,${u},${this._x1=c},${this._y1=s}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=vL)},${u},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function vF(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new vU(t)}vU.prototype,vP(3),vP(3);var vK=["type","size","sizeType"];function vq(){return(vq=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vW(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vH(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var vV={symbolCircle:vk,symbolCross:{draw(e,t){let r=vP(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=vP(t/vE),n=r*vM;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=vP(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=vP(.8908130915292852*t),n=v_*r,i=vN*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=vS*t/5,o=vO(a),l=vj(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-vP(t/(3*vC));e.moveTo(0,2*r),e.lineTo(-vC*r,-r),e.lineTo(vC*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=vP(t/vR),n=r/2,i=r*vI,a=r*vI+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-vD*i,vD*n+-.5*i),e.lineTo(-.5*n-vD*a,vD*n+-.5*a),e.lineTo(-.5*o-vD*a,vD*o+-.5*a),e.lineTo(-.5*n+vD*i,-.5*i-vD*n),e.lineTo(-.5*n+vD*a,-.5*a-vD*n),e.lineTo(-.5*o+vD*a,-.5*a-vD*o),e.closePath()}}},vY=Math.PI/180,vG=e=>vV["symbol".concat(eh(e))]||vk,vZ=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*vY;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},vX=e=>{var{type:t="circle",size:r=64,sizeType:n="area"}=e,i=vW(vW({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vK)),{},{type:t,size:r,sizeType:n}),{className:o,cx:l,cy:c}=i,s=hc(i,!0);return l===+l&&c===+c&&r===+r?a.createElement("path",vq({},s,{className:(0,X.$)("recharts-symbols",o),transform:"translate(".concat(l,", ").concat(c,")"),d:(()=>{var e=vG(t);return(function(e,t){let r=null,n=vF(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:nl(e||vk),t="function"==typeof t?t:nl(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:nl(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:nl(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(vZ(r,n,t))()})()})):null};vX.registerSymbol=(e,t)=>{vV["symbol".concat(eh(e))]=t};var vJ=["option","shapeType","propTransformer","activeClassName","isActive"];function vQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vQ(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v1(e,t){return v0(v0({},t),e)}function v2(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return a.createElement(vu,r);case"trapezoid":return a.createElement(vp,r);case"sector":return a.createElement(vw,r);case"symbols":if("symbols"===t)return a.createElement(vX,r);break;default:return null}}function v3(e){var t,{option:r,shapeType:n,propTransformer:i=v1,activeClassName:o="recharts-active-shape",isActive:l}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vJ);if((0,a.isValidElement)(r))t=(0,a.cloneElement)(r,v0(v0({},c),(0,a.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(c);else if(vo()(r)&&"boolean"!=typeof r){var s=i(r,c);t=a.createElement(v2,{shapeType:n,elementProps:s})}else t=a.createElement(v2,{shapeType:n,elementProps:c});return l?a.createElement(hQ,{className:o},t):t}var v5=["x","y"];function v4(){return(v4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v6(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v7(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,v5),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),c=parseInt("".concat(t.width||i.width),10);return v8(v8(v8(v8(v8({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:c,name:t.name,radius:t.radius})}function v9(e){return a.createElement(v3,v4({shapeType:"rectangle",propTransformer:v7,activeClassName:"recharts-active-bar"},e))}var ge=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if(ea(e))return e;var i=ea(r)||ed(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},gt=(e,t)=>{var r=r6();return(n,i)=>a=>{null==e||e(n,i,a),r(rm({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},gr=e=>{var t=r6();return(r,n)=>i=>{null==e||e(r,n,i),t(rb())}},gn=(e,t)=>{var r=r6();return(n,i)=>a=>{null==e||e(n,i,a),r(rw({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function gi(e){var{fn:t,args:r}=e;return r6(),n6(),null}var ga=()=>{var e=r6();return(0,a.useEffect)(()=>(e(dn()),()=>{e(di())})),null};function go(e,t){var r,n,i=ne(t=>sh(t,e)),a=ne(e=>sy(e,t)),o=null!==(r=null==i?void 0:i.allowDataOverflow)&&void 0!==r?r:sd.allowDataOverflow,l=null!==(n=null==a?void 0:a.allowDataOverflow)&&void 0!==n?n:sp.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function gl(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,i=it(),{needClipX:o,needClipY:l,needClip:c}=go(t,r);if(!c)return null;var{left:s,top:u,width:f,height:d}=i;return a.createElement("clipPath",{id:"clipPath-".concat(n)},a.createElement("rect",{x:o?s:s-f/2,y:l?u:u-d/2,width:o?f:2*f,height:l?d:2*d}))}function gc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gs(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gc(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var gu=(e,t,r,n,i)=>i,gf=(e,t,r)=>{var n=null!=r?r:e;if(!ed(n))return es(n,t,0)},gd=r1([il,sO,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function gh(e){return null!=e.stackId&&null!=e.dataKey}var gp=r1([gd,e=>e.rootProps.barSize,(e,t,r)=>"horizontal"===il(e)?uD(e,"xAxis",t):uD(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(gh),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:gf(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:gf(t,r,e.barSize)}))]}),gy=(e,t,r,n)=>{var i,a;return"horizontal"===il(e)?(i=uF(e,"xAxis",t,n),a=uU(e,"xAxis",t,n)):(i=uF(e,"yAxis",r,n),a=uU(e,"yAxis",r,n)),nU(i,a)},gv=r1([gp,cY,e=>e.rootProps.barGap,cG,(e,t,r,n,i)=>{var a,o,l,c,s=il(e),u=cY(e),{maxBarSize:f}=i,d=ed(f)?u:f;return"horizontal"===s?(l=uF(e,"xAxis",t,n),c=uU(e,"xAxis",t,n)):(l=uF(e,"yAxis",r,n),c=uU(e,"yAxis",r,n)),null!==(a=null!==(o=nU(l,c,!0))&&void 0!==o?o:d)&&void 0!==a?a:0},gy,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=es(e,r,0,!0),c=[];if(l0(n[0].barSize)){var s=!1,u=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*l)>=r&&(f-=(o-1)*l,l=0),f>=r&&u>0&&(s=!0,u*=.9,f=o*u);var d={offset:((r-f)/2>>0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+l,size:s?u:null!==(r=t.barSize)&&void 0!==r?r:0}}];return d=n[n.length-1].position,n},c)}else{var h=es(t,r,0,!0);r-2*h-(o-1)*l<=0&&(l=0);var p=(r-2*h-(o-1)*l)/o;p>1&&(p>>=0);var y=l0(i)?Math.min(p,i):p;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h+(p+l)*r+(p-y)/2,size:y}}],c)}return a}}(r,n,i!==a?i:a,e,ed(o)?t:o);return i!==a&&null!=l&&(l=l.map(e=>gs(gs({},e),{},{position:gs(gs({},e.position),{},{offset:e.position.offset-i/2})}))),l}),gg=r1([gv,gu],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),gm=r1([sO,gu],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),gb=r1([(e,t,r,n)=>"horizontal"===il(e)?sL(e,"yAxis",r,n):sL(e,"xAxis",t,n),gu],(e,t)=>{if(e&&(null==t?void 0:t.dataKey)!=null){var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}}),gx=r1([n2,(e,t,r,n)=>uF(e,"xAxis",t,n),(e,t,r,n)=>uF(e,"yAxis",r,n),(e,t,r,n)=>uU(e,"xAxis",t,n),(e,t,r,n)=>uU(e,"yAxis",r,n),gg,il,lQ,gy,gb,gm,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,c,s,u,f)=>{var d,{chartData:h,dataStartIndex:p,dataEndIndex:y}=l;if(null!=u&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var{data:v}=u;if(null!=(d=null!=v&&v.length>0?v:null==h?void 0:h.slice(p,y+1)))return function(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:s,stackedData:u,displayedData:f,offset:d,cells:h}=e,p="horizontal"===t?l:o,y=u?p.scale.domain():null,v=nI({numericAxis:p});return f.map((e,f)=>{u?g=n_(u[f],y):Array.isArray(g=nP(e,r))||(g=[v,g]);var p=ge(n,0)(g[1],f);if("horizontal"===t){var g,m,b,x,w,O,j,[P,A]=[l.scale(g[0]),l.scale(g[1])];m=nD({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),b=null!==(j=null!=A?A:P)&&void 0!==j?j:void 0,x=i.size;var S=P-A;if(w=en(S)?0:S,O={x:m,y:d.top,width:x,height:d.height},Math.abs(p)>0&&Math.abs(w)<Math.abs(p)){var k=er(w||p)*(Math.abs(p)-Math.abs(w));b-=k,w+=k}}else{var[M,E]=[o.scale(g[0]),o.scale(g[1])];if(m=M,b=nD({axis:l,ticks:s,bandSize:a,offset:i.offset,entry:e,index:f}),x=E-M,w=i.size,O={x:d.left,y:b,width:d.width,height:w},Math.abs(p)>0&&Math.abs(x)<Math.abs(p)){var T=er(x||p)*(Math.abs(p)-Math.abs(x));x+=T}}return gE(gE({},e),{},{x:m,y:b,width:x,height:w,value:u?g:g[1],payload:e,background:O,tooltipPosition:{x:m+x/2,y:b+w/2}},h&&h[f]&&h[f].props)})}({layout:o,barSettings:u,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:s,displayedData:d,offset:e,cells:f})}});function gw(e){var{legendPayload:t}=e;return r6(),n6(),null}function gO(e){var{legendPayload:t}=e;return r6(),ne(il),null}function gj(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,a.useRef)(ec(t)),n=(0,a.useRef)(e);return n.current!==e&&(r.current=ec(t),n.current=e),r.current}var gP=["onMouseEnter","onMouseLeave","onClick"],gA=["value","background","tooltipPosition"],gS=["onMouseEnter","onClick","onMouseLeave"];function gk(){return(gk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gM(Object(r),!0).forEach(function(t){gT(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gT(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function g_(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var gN=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:nK(r,t),payload:e}]};function gC(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:nK(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function gD(e){var t=ne(fT),{data:r,dataKey:n,background:i,allOtherBarProps:o}=e,{onMouseEnter:l,onMouseLeave:c,onClick:s}=o,u=g_(o,gP),f=gt(l,n),d=gr(c),h=gn(s,n);if(!i||null==r)return null;var p=hc(i,!1);return a.createElement(a.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:c}=e,s=g_(e,gA);if(!l)return null;var y=f(e,r),v=d(e,r),g=h(e,r),m=gE(gE(gE(gE(gE({option:i,isActive:String(r)===t},s),{},{fill:"#eee"},l),p),ht(u,e,r)),{},{onMouseEnter:y,onMouseLeave:v,onClick:g,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a.createElement(v9,gk({key:"background-bar-".concat(r)},m))}))}function gI(e){var{data:t,props:r,showLabels:n}=e,i=hc(r,!1),{shape:o,dataKey:l,activeBar:c}=r,s=ne(fT),u=ne(fN),{onMouseEnter:f,onClick:d,onMouseLeave:h}=r,p=g_(r,gS),y=gt(f,l),v=gr(h),g=gn(d,l);return t?a.createElement(a.Fragment,null,t.map((e,t)=>{var r=c&&String(t)===s&&(null==u||l===u),n=gE(gE(gE({},i),e),{},{isActive:r,option:r?c:o,index:t,dataKey:l});return a.createElement(hQ,gk({className:"recharts-bar-rectangle"},ht(p,e,t),{onMouseEnter:y(e,t),onMouseLeave:v(e,t),onClick:g(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),a.createElement(v9,n))}),n&&vi.renderCallByParent(r,t)):null}function gR(e){var{props:t,previousRectanglesRef:r}=e,{data:n,layout:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s,onAnimationEnd:u,onAnimationStart:f}=t,d=r.current,h=gj(t,"recharts-bar-"),[p,y]=(0,a.useState)(!1),v=(0,a.useCallback)(()=>{"function"==typeof u&&u(),y(!1)},[u]),g=(0,a.useCallback)(()=>{"function"==typeof f&&f(),y(!0)},[f]);return a.createElement(yY,{begin:l,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},onAnimationEnd:v,onAnimationStart:g,key:h},e=>{var{t:o}=e,l=1===o?n:n.map((e,t)=>{var r=d&&d[t];if(r){var n=ef(r.x,e.x),a=ef(r.y,e.y),l=ef(r.width,e.width),c=ef(r.height,e.height);return gE(gE({},e),{},{x:n(o),y:a(o),width:l(o),height:c(o)})}if("horizontal"===i){var s=ef(0,e.height)(o);return gE(gE({},e),{},{y:e.y+e.height-s,height:s})}var u=ef(0,e.width)(o);return gE(gE({},e),{},{width:u})});return o>0&&(r.current=l),a.createElement(hQ,null,a.createElement(gI,{props:t,data:l,showLabels:!p}))})}function gL(e){var{data:t,isAnimationActive:r}=e,n=(0,a.useRef)(null);return r&&t&&t.length&&(null==n.current||n.current!==t)?a.createElement(gR,{previousRectanglesRef:n,props:e}):a.createElement(gI,{props:e,data:t,showLabels:!0})}var gz=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:nP(e,t)}};class g$ extends a.PureComponent{constructor(){super(...arguments),gT(this,"id",ec("recharts-bar-"))}render(){var{hide:e,data:t,dataKey:r,className:n,xAxisId:i,yAxisId:o,needClip:l,background:c,id:s,layout:u}=this.props;if(e)return null;var f=(0,X.$)("recharts-bar",n),d=ed(s)?this.id:s;return a.createElement(hQ,{className:f},l&&a.createElement("defs",null,a.createElement(gl,{clipPathId:d,xAxisId:i,yAxisId:o})),a.createElement(hQ,{className:"recharts-bar-rectangles",clipPath:l?"url(#clipPath-".concat(d,")"):null},a.createElement(gD,{data:t,dataKey:r,background:c,allOtherBarProps:this.props}),a.createElement(gL,this.props)),a.createElement(y0,{direction:"horizontal"===u?"y":"x"},this.props.children))}}var gB={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!hR.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function gU(e){var t,{xAxisId:r,yAxisId:n,hide:i,legendType:o,minPointSize:l,activeBar:c,animationBegin:s,animationDuration:u,animationEasing:f,isAnimationActive:d}=hE(e,gB),{needClip:h}=go(r,n),p=ic(),y=n6(),v=(0,a.useMemo)(()=>{var t;return{barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:l,stackId:null==(t=e.stackId)?void 0:String(t)}},[e.barSize,e.dataKey,e.maxBarSize,l,e.stackId]),g=ho(e.children,y5),m=ne(e=>gx(e,r,n,y,v,g));if("vertical"!==p&&"horizontal"!==p)return null;var b=null==m?void 0:m[0];return t=null==b||null==b.height||null==b.width?0:"vertical"===p?b.height/2:b.width/2,a.createElement(yh,{xAxisId:r,yAxisId:n,data:m,dataPointFormatter:gz,errorBarOffset:t},a.createElement(g$,gk({},e,{layout:p,needClip:h,data:m,xAxisId:r,yAxisId:n,hide:i,legendType:o,minPointSize:l,activeBar:c,animationBegin:s,animationDuration:u,animationEasing:f,isAnimationActive:d})))}class gF extends a.PureComponent{render(){return a.createElement(yy,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},a.createElement(ga,null),a.createElement(gw,{legendPayload:gN(this.props)}),a.createElement(gi,{fn:gC,args:this.props}),a.createElement(gU,this.props))}}function gK(e){return r6(),null}gT(gF,"displayName","Bar"),gT(gF,"defaultProps",gB);var gq=["width","height","layout"];function gH(){return(gH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var gW={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},gV=(0,a.forwardRef)(function(e,t){var r,n=hE(e.categoricalChartProps,gW),{width:i,height:o,layout:l}=n,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,gq);if(!l1(i)||!l1(o))return null;var{chartName:s,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=e;return a.createElement(d0,{preloadedState:{options:{chartName:s,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0}},reduxStoreName:null!==(r=n.id)&&void 0!==r?r:s},a.createElement(d1,{chartData:n.data}),a.createElement(d2,{width:i,height:o,layout:l,margin:n.margin}),a.createElement(d3,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),a.createElement(gK,{cx:n.cx,cy:n.cy,startAngle:n.startAngle,endAngle:n.endAngle,innerRadius:n.innerRadius,outerRadius:n.outerRadius}),a.createElement(hk,gH({width:i,height:o},c,{ref:t})))}),gY=["item"],gG={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},gZ=(0,a.forwardRef)((e,t)=>{var r=hE(e,gG);return a.createElement(gV,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:gY,tooltipPayloadSearcher:t6,categoricalChartProps:r,ref:t})}),gX=e=>e.graphicalItems.polarItems,gJ=r1([sl,sc],sw),gQ=r1([gX,sm,gJ],sP),g0=r1([gQ],sM),g1=r1([g0,lJ],sT),g2=r1([g1,sm,gQ],sN),g3=r1([g1,sm,gQ],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:nP(e,null!==(n=t.dataKey)&&void 0!==n?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:nP(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),g5=()=>void 0,g4=r1([sm,s8,g5,g3,g5],s7),g6=r1([sm,il,g1,g2,cZ,sl,g4],ut),g8=r1([g6,sm,ui],uo);function g7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g9(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g7(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r1([sm,g6,g8,sl],uc);var me=(e,t)=>t,mt=[],mr=(e,t,r)=>(null==r?void 0:r.length)===0?mt:r,mn=r1([lJ,me,mr],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>g9(g9({},t.presentationProps),e.props))),null!=n)return n}),mi=r1([mn,me,mr],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=nP(e,t.nameKey,t.name);return a=null!=r&&null!==(i=r[n])&&void 0!==i&&null!==(i=i.props)&&void 0!==i&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:nK(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),ma=r1([gX,me],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),mo=r1([mn,ma,mr,n2],(e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:c,startAngle:s,endAngle:u,dataKey:f,nameKey:d,tooltipType:h}=i,p=Math.abs(i.minAngle),y=mG(s,u),v=Math.abs(y),g=a.length<=1?0:null!==(t=i.paddingAngle)&&void 0!==t?t:0,m=a.filter(e=>0!==nP(e,f,0)).length,b=v-m*p-(v>=360?m:m-1)*g,x=a.reduce((e,t)=>{var r=nP(t,f,0);return e+(ea(r)?r:0)},0);return x>0&&(r=a.map((e,t)=>{var r,a=nP(e,f,0),u=nP(e,d,t),v=mY(i,l,e),m=(ea(a)?a:0)/x,w=mU(mU({},e),o&&o[t]&&o[t].props),O=(r=t?n.endAngle+er(y)*g*(0!==a?1:0):s)+er(y)*((0!==a?p:0)+m*b),j=(r+O)/2,P=(v.innerRadius+v.outerRadius)/2,A=[{name:u,value:a,payload:w,dataKey:f,type:h}],S=ny(v.cx,v.cy,P,j);return n=mU(mU(mU(mU({},i.presentationProps),{},{percent:m,cornerRadius:c,name:u,tooltipPayload:A,midAngle:j,middleRadius:P,tooltipPosition:S},w),v),{},{value:nP(e,f),startAngle:r,endAngle:O,payload:w,paddingAngle:er(y)*g})})),r}({offset:n,pieSettings:t,displayedData:e,cells:r})});function ml(){}function mc(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function ms(e){this._context=e}function mu(e){this._context=e}function mf(e){this._context=e}ms.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:mc(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:mc(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},mu.prototype={areaStart:ml,areaEnd:ml,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:mc(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},mf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:mc(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class md{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function mh(e){this._context=e}function mp(e){this._context=e}function my(e){return new mp(e)}function mv(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function mg(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function mm(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function mb(e){this._context=e}function mx(e){this._context=new mw(e)}function mw(e){this._context=e}function mO(e){this._context=e}function mj(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function mP(e,t){this._context=e,this._t=t}function mA(e){return e[0]}function mS(e){return e[1]}function mk(e,t){var r=nl(!0),n=null,i=my,a=null,o=vF(l);function l(l){var c,s,u,f=(l=no(l)).length,d=!1;for(null==n&&(a=i(u=o())),c=0;c<=f;++c)!(c<f&&r(s=l[c],c,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(s,c,l),+t(s,c,l));if(u)return a=null,u+""||null}return e="function"==typeof e?e:void 0===e?mA:nl(e),t="function"==typeof t?t:void 0===t?mS:nl(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:nl(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:nl(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:nl(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function mM(e,t,r){var n=null,i=nl(!0),a=null,o=my,l=null,c=vF(s);function s(s){var u,f,d,h,p,y=(s=no(s)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=c())),u=0;u<=y;++u){if(!(u<y&&i(h=s[u],u,s))===v){if(v=!v)f=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=u-1;d>=f;--d)l.point(g[d],m[d]);l.lineEnd(),l.areaEnd()}}v&&(g[u]=+e(h,u,s),m[u]=+t(h,u,s),l.point(n?+n(h,u,s):g[u],r?+r(h,u,s):m[u]))}if(p)return l=null,p+""||null}function u(){return mk().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?mA:nl(+e),t="function"==typeof t?t:void 0===t?nl(0):nl(+t),r="function"==typeof r?r:void 0===r?mS:nl(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:nl(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:nl(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:nl(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:nl(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:nl(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:nl(+e),s):r},s.lineX0=s.lineY0=function(){return u().x(e).y(t)},s.lineY1=function(){return u().x(e).y(r)},s.lineX1=function(){return u().x(n).y(t)},s.defined=function(e){return arguments.length?(i="function"==typeof e?e:nl(!!e),s):i},s.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),s):o},s.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),s):a},s}function mE(){return(mE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function mT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mT(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}mh.prototype={areaStart:ml,areaEnd:ml,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},mp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},mb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:mm(this,this._t0,mg(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,mm(this,mg(this,r=mv(this,e,t)),r);break;default:mm(this,this._t0,r=mv(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(mx.prototype=Object.create(mb.prototype)).point=function(e,t){mb.prototype.point.call(this,t,e)},mw.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},mO.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=mj(e),i=mj(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},mP.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var mN={curveBasisClosed:function(e){return new mu(e)},curveBasisOpen:function(e){return new mf(e)},curveBasis:function(e){return new ms(e)},curveBumpX:function(e){return new md(e,!0)},curveBumpY:function(e){return new md(e,!1)},curveLinearClosed:function(e){return new mh(e)},curveLinear:my,curveMonotoneX:function(e){return new mb(e)},curveMonotoneY:function(e){return new mx(e)},curveNatural:function(e){return new mO(e)},curveStep:function(e){return new mP(e,.5)},curveStepAfter:function(e){return new mP(e,1)},curveStepBefore:function(e){return new mP(e,0)}},mC=e=>l0(e.x)&&l0(e.y),mD=e=>e.x,mI=e=>e.y,mR=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat(eh(e));return("curveMonotone"===r||"curveBump"===r)&&t?mN["".concat(r).concat("vertical"===t?"Y":"X")]:mN[r]||my},mL=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=mR(r,a),c=o?n.filter(mC):n;if(Array.isArray(i)){var s=o?i.filter(e=>mC(e)):i,u=c.map((e,t)=>m_(m_({},e),{},{base:s[t]}));return(t="vertical"===a?mM().y(mI).x1(mD).x0(e=>e.base.x):mM().x(mD).y1(mI).y0(e=>e.base.y)).defined(mC).curve(l),t(u)}return(t="vertical"===a&&ea(i)?mM().y(mI).x1(mD).x0(i):ea(i)?mM().x(mD).y1(mI).y0(i):mk().x(mD).y(mI)).defined(mC).curve(l),t(c)},mz=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if((!r||!r.length)&&!n)return null;var o=r&&r.length?mL(e):n;return a.createElement("path",mE({},hc(e,!1),d9(e),{className:(0,X.$)("recharts-curve",t),d:null===o?void 0:o,ref:i}))},m$=["onMouseEnter","onClick","onMouseLeave"];function mB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mB(Object(r),!0).forEach(function(t){mF(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mF(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mK(){return(mK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function mq(e){var t=(0,a.useMemo)(()=>hc(e,!1),[e]),r=(0,a.useMemo)(()=>ho(e.children,y5),[e.children]),n=(0,a.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=ne(e=>mi(e,n,r));return a.createElement(gO,{legendPayload:i})}function mH(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:c,tooltipType:s}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:nK(l,t),hide:c,type:s,color:o,unit:""}}}var mW=(e,t)=>e>t?"start":e<t?"end":"middle",mV=(e,t,r)=>"function"==typeof t?t(e):es(t,r,.8*r),mY=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=nv(a,o),c=i+es(e.cx,a,a/2),s=n+es(e.cy,o,o/2);return{cx:c,cy:s,innerRadius:es(e.innerRadius,l,0),outerRadius:mV(r,e.outerRadius,l),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},mG=(e,t)=>er(t-e)*Math.min(Math.abs(t-e),360),mZ=(e,t)=>{if(a.isValidElement(e))return a.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,X.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return a.createElement(mz,mK({},t,{type:"linear",className:r}))},mX=(e,t,r)=>{if(a.isValidElement(e))return a.cloneElement(e,t);var n=r;if("function"==typeof e&&(n=e(t),a.isValidElement(n)))return n;var i=(0,X.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return a.createElement(pu,mK({},t,{alignmentBaseline:"middle",className:i}),n)};function mJ(e){var{sectors:t,props:r,showLabels:n}=e,{label:i,labelLine:o,dataKey:l}=r;if(!n||!i||!t)return null;var c=hc(r,!1),s=hc(i,!1),u=hc(o,!1),f="object"==typeof i&&"offsetRadius"in i&&i.offsetRadius||20,d=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,n=ny(e.cx,e.cy,e.outerRadius+f,r),d=mU(mU(mU(mU({},c),e),{},{stroke:"none"},s),{},{index:t,textAnchor:mW(n.x,e.cx)},n),h=mU(mU(mU(mU({},c),e),{},{fill:"none",stroke:e.fill},u),{},{index:t,points:[ny(e.cx,e.cy,e.outerRadius,r),n],key:"line"});return a.createElement(hQ,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&mZ(o,h),mX(i,d,nP(e,l)))});return a.createElement(hQ,{className:"recharts-pie-labels"},d)}function mQ(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:i,showLabels:o}=e,l=ne(fT),{onMouseEnter:c,onClick:s,onMouseLeave:u}=i,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,m$),d=gt(c,i.dataKey),h=gr(u),p=gn(s,i.dataKey);return null==t?null:a.createElement(a.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var c=r&&String(o)===l,s=c?r:l?n:null,u=mU(mU({},e),{},{stroke:e.stroke,tabIndex:-1,[nJ]:o,[nQ]:i.dataKey});return a.createElement(hQ,mK({tabIndex:-1,className:"recharts-pie-sector"},ht(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:h(e,o),onClick:p(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),a.createElement(v3,mK({option:s,isActive:c,shapeType:"sector"},u)))}),a.createElement(mJ,{sectors:t,props:i,showLabels:o}))}function m0(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:i,animationBegin:o,animationDuration:l,animationEasing:c,activeShape:s,inactiveShape:u,onAnimationStart:f,onAnimationEnd:d}=t,h=gj(t,"recharts-pie-"),p=r.current,[y,v]=(0,a.useState)(!0),g=(0,a.useCallback)(()=>{"function"==typeof d&&d(),v(!1)},[d]),m=(0,a.useCallback)(()=>{"function"==typeof f&&f(),v(!0)},[f]);return a.createElement(yY,{begin:o,duration:l,isActive:i,easing:c,from:{t:0},to:{t:1},onAnimationStart:m,onAnimationEnd:g,key:h},e=>{var{t:i}=e,o=[],l=(n&&n[0]).startAngle;return n.forEach((e,t)=>{var r=p&&p[t],n=t>0?et()(e,"paddingAngle",0):0;if(r){var a=ef(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=mU(mU({},e),{},{startAngle:l+n,endAngle:l+a(i)+n});o.push(c),l=c.endAngle}else{var{endAngle:s,startAngle:u}=e,f=ef(0,s-u)(i),d=mU(mU({},e),{},{startAngle:l+n,endAngle:l+f+n});o.push(d),l=d.endAngle}}),r.current=o,a.createElement(hQ,null,a.createElement(mQ,{sectors:o,activeShape:s,inactiveShape:u,allOtherPieProps:t,showLabels:!y}))})}function m1(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:i}=e,o=(0,a.useRef)(null),l=o.current;return r&&t&&t.length&&(!l||l!==t)?a.createElement(m0,{props:e,previousSectorsRef:o}):a.createElement(mQ,{sectors:t,activeShape:n,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function m2(e){var{hide:t,className:r,rootTabIndex:n}=e,i=(0,X.$)("recharts-pie",r);return t?null:a.createElement(hQ,{tabIndex:n,className:i},a.createElement(m1,e))}var m3={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!hR.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function m5(e){var t=hE(e,m3),r=(0,a.useMemo)(()=>ho(e.children,y5),[e.children]),n=hc(t,!1),i=(0,a.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:n}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,n]),o=ne(e=>mo(e,i,r));return a.createElement(a.Fragment,null,a.createElement(gi,{fn:mH,args:mU(mU({},t),{},{sectors:o})}),a.createElement(m2,mK({},t,{sectors:o})))}class m4 extends a.PureComponent{constructor(){super(...arguments),mF(this,"id",ec("recharts-pie-"))}render(){return a.createElement(a.Fragment,null,a.createElement(yc,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),a.createElement(mq,this.props),a.createElement(m5,this.props),this.props.children)}}mF(m4,"displayName","Pie"),mF(m4,"defaultProps",m3);var m6=r(55740);function m8(){return(m8=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function m7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m9(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class be extends a.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,n=32/6,i=32/3,o=e.inactive?r:e.color,l=null!=t?t:e.type;if("none"===l)return null;if("plainline"===l)return a.createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===l)return a.createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(i,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(2*i,",").concat(16,"\n            H").concat(32,"M").concat(2*i,",").concat(16,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(i,",").concat(16),className:"recharts-legend-icon"});if("rect"===l)return a.createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a.isValidElement(e.legendIcon)){var c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m7(Object(r),!0).forEach(function(t){m9(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete c.legendIcon,a.cloneElement(e.legendIcon,c)}return a.createElement(vX,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:l})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:n,inactiveColor:i,iconType:o}=this.props,l={x:0,y:0,width:32,height:32},c={display:"horizontal"===r?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map((e,r)=>{var u=e.formatter||n,f=(0,X.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var d=e.inactive?i:e.color,h=u?u(e.value,e,r):e.value;return a.createElement("li",m8({className:f,style:c,key:"legend-item-".concat(r)},ht(this.props,e,r)),a.createElement(hd,{width:t,height:t,viewBox:l,style:s,"aria-label":"".concat(h," legend icon")},this.renderIcon(e,o)),a.createElement("span",{className:"recharts-legend-item-text",style:{color:d}},h))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?a.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}m9(be,"displayName","Legend"),m9(be,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var bt=r(54813),br=r.n(bt),bn=["contextPayload"];function bi(){return(bi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ba(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ba(Object(r),!0).forEach(function(t){bl(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ba(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bl(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bc(e){return e.value}function bs(e){var t,{contextPayload:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,bn),i=!0===(t=e.payloadUniqBy)?br()(r,bc):"function"==typeof t?br()(r,t):r,o=bo(bo({},n),{},{payload:i});return a.isValidElement(e.content)?a.cloneElement(e.content,o):"function"==typeof e.content?a.createElement(e.content,o):a.createElement(be,o)}function bu(e){return r6(),null}function bf(e){return r6(),null}function bd(e){var t=ne(ni),r=hw(),n=io(),{width:i,height:o,wrapperStyle:l,portal:c}=e,[s,u]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,a.useState)({height:0,left:0,top:0,width:0}),n=(0,a.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}([t]),f=ir(),d=ii(),h=f-(n.left||0)-(n.right||0),p=bh.getWidthOrHeight(e.layout,o,i,h),y=c?l:bo(bo({position:"absolute",width:(null==p?void 0:p.width)||i||"auto",height:(null==p?void 0:p.height)||o||"auto"},function(e,t,r,n,i,a){var o,l,{layout:c,align:s,verticalAlign:u}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(o="center"===s&&"vertical"===c?{left:((n||0)-a.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(l="middle"===u?{top:((i||0)-a.height)/2}:"bottom"===u?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),bo(bo({},o),l)}(l,e,n,f,d,s)),l),v=null!=c?c:r;if(null==v)return null;var g=a.createElement("div",{className:"recharts-legend-wrapper",style:y,ref:u},a.createElement(bu,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),a.createElement(bf,{width:s.width,height:s.height}),a.createElement(bs,bi({},e,p,{margin:n,chartWidth:f,chartHeight:d,contextPayload:t})));return(0,m6.createPortal)(g,v)}class bh extends a.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&ea(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return a.createElement(bd,this.props)}}function bp(){let[e,t]=(0,a.useState)(null),[r,n]=(0,a.useState)(null),[c,s]=(0,a.useState)(!0),[u,f]=(0,a.useState)(null),d=[{name:"Business",value:35},{name:"Self-Help",value:25},{name:"Finance",value:20},{name:"Marketing",value:15},{name:"Technology",value:5}],h=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8"];return c?(0,i.jsx)("div",{className:"container py-8 md:py-12",children:(0,i.jsxs)("div",{className:"flex justify-center items-center h-64",children:[(0,i.jsx)(q.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,i.jsx)("p",{className:"ml-2",children:"Loading analytics..."})]})}):u?(0,i.jsx)("div",{className:"container py-8 md:py-12",children:(0,i.jsx)("div",{className:"text-center py-12",children:(0,i.jsx)("p",{className:"text-destructive mb-4",children:u})})}):e&&r?(0,i.jsxs)("div",{className:"container py-8 md:py-12",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Your Analytics"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Track your reading habits and get personalized recommendations"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,i.jsx)("div",{className:"md:col-span-2",children:(0,i.jsx)(I,{userId:e})}),(0,i.jsx)("div",{className:"md:col-span-1",children:(0,i.jsxs)(o.Zp,{className:"h-full",children:[(0,i.jsxs)(o.aR,{className:"pb-2",children:[(0,i.jsxs)(o.ZB,{className:"text-lg flex items-center gap-2",children:[(0,i.jsx)(C.A,{className:"h-5 w-5 text-primary"}),"Reading Calendar"]}),(0,i.jsx)(o.BT,{children:"Your reading activity over time"})]}),(0,i.jsx)(o.Wu,{children:(0,i.jsx)("div",{className:"h-[300px] mt-4",children:(0,i.jsxs)(Z.at,{children:[(0,i.jsx)(Z.hx,{children:"Reading Time by Day"}),(0,i.jsx)(Z.t1,{children:(0,i.jsx)(eg,{width:"100%",height:"100%",children:(0,i.jsxs)(hI,{data:[{day:"Mon",minutes:45},{day:"Tue",minutes:30},{day:"Wed",minutes:60},{day:"Thu",minutes:15},{day:"Fri",minutes:75},{day:"Sat",minutes:90},{day:"Sun",minutes:120}],children:[(0,i.jsx)(pJ,{strokeDasharray:"3 3",vertical:!1}),(0,i.jsx)(p8,{dataKey:"day"}),(0,i.jsx)(yo,{label:{value:"Minutes",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),(0,i.jsx)(Z.II,{content:({active:e,payload:t})=>e&&t&&t.length?(0,i.jsx)("div",{className:"rounded-lg border bg-background p-2 shadow-sm",children:(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"text-[0.70rem] uppercase text-muted-foreground",children:"Day"}),(0,i.jsx)("span",{className:"font-bold text-muted-foreground",children:t[0].payload.day})]}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"text-[0.70rem] uppercase text-muted-foreground",children:"Reading Time"}),(0,i.jsxs)("span",{className:"font-bold",children:[t[0].value," min"]})]})]})}):null}),(0,i.jsx)(gF,{dataKey:"minutes",fill:"hsl(var(--primary))",radius:[4,4,0,0]})]})})})]})})})]})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,i.jsx)("div",{className:"md:col-span-1",children:(0,i.jsxs)(o.Zp,{className:"h-full",children:[(0,i.jsxs)(o.aR,{className:"pb-2",children:[(0,i.jsxs)(o.ZB,{className:"text-lg flex items-center gap-2",children:[(0,i.jsx)(_,{className:"h-5 w-5 text-primary"}),"Reading by Category"]}),(0,i.jsx)(o.BT,{children:"Distribution of your reading interests"})]}),(0,i.jsx)(o.Wu,{children:(0,i.jsx)("div",{className:"h-[300px] mt-4",children:(0,i.jsx)(Z.at,{children:(0,i.jsx)(Z.t1,{children:(0,i.jsx)(eg,{width:"100%",height:"100%",children:(0,i.jsxs)(gZ,{children:[(0,i.jsx)(m4,{data:d,cx:"50%",cy:"50%",labelLine:!1,outerRadius:80,fill:"#8884d8",dataKey:"value",label:({name:e,percent:t})=>`${e} ${(100*t).toFixed(0)}%`,children:d.map((e,t)=>(0,i.jsx)(y5,{fill:h[t%h.length]},`cell-${t}`))}),(0,i.jsx)(bh,{}),(0,i.jsx)(Z.II,{})]})})})})})})]})}),(0,i.jsx)("div",{className:"md:col-span-2",children:(0,i.jsxs)(l.Tabs,{defaultValue:"recommendations",className:"h-full",children:[(0,i.jsxs)(l.TabsList,{className:"grid w-full grid-cols-2",children:[(0,i.jsx)(l.TabsTrigger,{value:"recommendations",children:"Recommendations"}),(0,i.jsx)(l.TabsTrigger,{value:"activity",children:"Activity"})]}),(0,i.jsx)(l.TabsContent,{value:"recommendations",className:"h-full",children:(0,i.jsx)(G,{userId:e})}),(0,i.jsx)(l.TabsContent,{value:"activity",className:"h-full",children:(0,i.jsx)(H,{userId:e})})]})})]})]}):(0,i.jsx)("div",{className:"container py-8 md:py-12",children:(0,i.jsx)("div",{className:"text-center py-12",children:(0,i.jsx)("p",{className:"text-destructive mb-4",children:"User not found. Please log in to view analytics."})})})}bl(bh,"displayName","Legend"),bl(bh,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"}),r(97730)},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var n=r(45512);r(58009);var i=r(21643),a=r(59462);let o=(0,i.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,a.cn)(o({variant:t}),e),...r})}},70085:(e,t,r)=>{e.exports=r(64332).get},13905:(e,t,r)=>{e.exports=r(4217).isEqual},63090:(e,t,r)=>{e.exports=r(49511).isPlainObject},32157:(e,t,r)=>{e.exports=r(66526).last},41192:(e,t,r)=>{e.exports=r(104).range},17476:(e,t,r)=>{e.exports=r(89355).sortBy},63085:(e,t,r)=>{e.exports=r(25979).throttle},54813:(e,t,r)=>{e.exports=r(31738).uniqBy},70141:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},63647:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},52449:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},24065:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},66536:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:e!=e?4:0}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},41640:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},69733:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},90276:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},44721:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},35968:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44721),i=r(53346),a=r(94605),o=r(83837);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},26134:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(34134),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},57140:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},5891:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},21925:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},66526:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(52449),i=r(5891),a=r(53346);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},56933:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(66536),i=r(26134),a=r(94869);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,s=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:s.map(t=>c(t,e))})).slice().sort((e,t)=>{for(let i=0;i<s.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},89355:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(56933),i=r(63647),a=r(35968);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},31738:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(24065),i=r(16132),a=r(5885),o=r(56594);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},87620:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(46937);t.debounce=function(e,t=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:l}=r,c=[,,];a&&(c[0]="leading"),o&&(c[1]="trailing");let s=null,u=n.debounce(function(...t){i=e.apply(this,t),s=null},t,{edges:c}),f=function(...t){return null!=l&&(null===s&&(s=Date.now()),Date.now()-s>=l)?(i=e.apply(this,t),s=Date.now(),u.cancel(),u.schedule()):u.apply(this,t),i};return f.cancel=u.cancel,f.flush=()=>(u.flush(),i),f}},25979:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(87620);t.throttle=function(e,t=0,r={}){"object"!=typeof r&&(r={});let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,trailing:a,maxWait:t})}},104:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(35968),i=r(96901);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},6331:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(3843);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},3843:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(70272),i=r(57140);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let c=t?.(r,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},64332:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(70141),i=r(90276),a=r(21925),o=r(94869);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a){if(i.isDeepKey(r))return e(t,o.toPath(r),l);return l}return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r))return function(e,t,r){if(0===t.length)return r;let i=e;for(let e=0;e<t.length;e++){if(null==i||n.isUnsafeProperty(t[e]))return r;i=i[t[e]]}return void 0===i?r:i}(t,r,l);if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},38304:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(90276),i=r(44721),a=r(93204),o=r(94869);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},15387:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(64332);t.property=function(e){return function(t){return n.get(t,e)}}},93204:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(69733);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},53346:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(91777);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},5885:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(53346),i=r(83622);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},98761:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(12341);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},12341:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98761),i=r(94605),a=r(72188),o=r(83837);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map)return function(e,t,r,n){if(0===t.size)return!0;if(!(e instanceof Map))return!1;for(let[i,a]of t.entries())if(!1===r(e.get(i),a,i,e,t,n))return!1;return!0}(e,t,r,n);if(t instanceof Set)return s(e,t,r,n);let i=Object.keys(t);if(null==e)return 0===i.length;if(0===i.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let o=0;o<i.length;o++){let l=i[o];if(!a.isPrimitive(e)&&!(l in e)||void 0===t[l]&&void 0!==e[l]||null===t[l]&&null!==e[l]||!r(e[l],t[l],l,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let s=e[c],u=!1;if(r(s,o,a,e,t,n)&&(u=!0),u){i.add(c),l=!0;break}}if(!l)return!1}return!0}function s(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,c){let s=r(t,n,i,a,o,c);return void 0!==s?!!s:l(t,n,e,c)},new Map)},t.isSetMatch=s},94605:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},83622:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},49511:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return!!(null!=t&&Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable)&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},34134:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},47177:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98761),i=r(90312);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},83856:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98761),i=r(21925),a=r(6331),o=r(64332),l=r(38304);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},83837:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},56594:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(16132),i=r(15387),a=r(47177),o=r(83856);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},96901:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(96855);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},96855:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(34134);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},94869:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},46937:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i;let a=null,o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),c=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)},s=()=>{l&&c(),h()},u=null,f=()=>{null!=u&&clearTimeout(u),u=setTimeout(()=>{u=null,s()},t)},d=()=>{null!==u&&(clearTimeout(u),u=null)},h=()=>{d(),i=void 0,a=null},p=function(...e){if(r?.aborted)return;i=this,a=e;let t=null==u;f(),o&&t&&c()};return p.schedule=f,p.cancel=h,p.flush=()=>{d(),c()},r?.addEventListener("abort",h,{once:!0}),p}},16132:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},30470:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},90312:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(70272);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},70272:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(41640),i=r(69733),a=r(57140),o=r(72188),l=r(80352);function c(e,t,r,n=new Map,u){let f=u?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){let t=Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,u);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[i,a]of(n.set(e,t),e))t.set(i,c(a,i,r,n,u));return t}if(e instanceof Set){let t=new Set;for(let i of(n.set(e,t),e))t.add(c(i,void 0,r,n,u));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,u);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),s(t,e,r,n,u),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return n.set(e,t),s(t,e,r,n,u),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return n.set(e,t),s(t,e,r,n,u),t}if(e instanceof Error){let t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,n,u),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),s(t,e,r,n,u),t}return e}function s(e,t,r=e,i,a){let o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){let l=o[n],s=Object.getOwnPropertyDescriptor(e,l);(null==s||s.writable)&&(e[l]=c(t[l],l,r,i,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=s},4217:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(13669),i=r(30470);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},13669:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(66586),i=r(41640),a=r(69733),o=r(57140),l=r(83837);t.isEqualWith=function(e,t,r){return function e(t,r,c,s,u,f,d){let h=d(t,r,c,s,u,f);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,s,u){if(Object.is(r,c))return!0;let f=a.getTag(r),d=a.getTag(c);if(f===o.argumentsTag&&(f=o.objectTag),d===o.argumentsTag&&(d=o.objectTag),f!==d)return!1;switch(f){case o.stringTag:return r.toString()===c.toString();case o.numberTag:{let e=r.valueOf(),t=c.valueOf();return l.eq(e,t)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(r.valueOf(),c.valueOf());case o.regexpTag:return r.source===c.source&&r.flags===c.flags;case o.functionTag:return r===c}let h=(s=s??new Map).get(r),p=s.get(c);if(null!=h&&null!=p)return h===c;s.set(r,c),s.set(c,r);try{switch(f){case o.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,s,u))return!1;return!0;case o.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,s,u));if(-1===o)return!1;n.splice(o,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(r)!==Buffer.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,s,u))return!1;return!0;case o.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,u);case o.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,u);case o.errorTag:return r.name===c.name&&r.message===c.message;case o.objectTag:{if(!(t(r.constructor,c.constructor,s,u)||n.isPlainObject(r)&&n.isPlainObject(c)))return!1;let a=[...Object.keys(r),...i.getSymbols(r)],o=[...Object.keys(c),...i.getSymbols(c)];if(a.length!==o.length)return!1;for(let t=0;t<a.length;t++){let n=a[t],i=r[n];if(!Object.hasOwn(c,n))return!1;let o=c[n];if(!e(i,o,n,r,c,s,u))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(c)}}(t,r,f,d)}(e,t,void 0,void 0,void 0,void 0,r)}},91777:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},66586:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},72188:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},80352:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},38224:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,s,u=this._events[l],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,o),!0}for(s=1,c=Array(f-1);s<f;s++)c[s-1]=arguments[s];u.fn.apply(u.context,c)}else{var d,h=u.length;for(s=0;s<h;s++)switch(u[s].once&&this.removeListener(e,u[s].fn,void 0,!0),f){case 1:u[s].fn.call(u[s].context);break;case 2:u[s].fn.call(u[s].context,t);break;case 3:u[s].fn.call(u[s].context,t,n);break;case 4:u[s].fn.call(u[s].context,t,n,i);break;default:if(!c)for(d=1,c=Array(f-1);d<f;d++)c[d-1]=arguments[d];u[s].fn.apply(u[s].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,s=[],u=l.length;c<u;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&s.push(l[c]);s.length?this._events[a]=1===s.length?s[0]:s:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},45723:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41680).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},46583:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41680).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4643:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(41680).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},35529:(e,t)=>{"use strict";var r=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler");Symbol.for("react.provider");var l=Symbol.for("react.consumer"),c=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.for("react.view_transition");Symbol.for("react.client.reference"),t.zv=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case i:case o:case a:case u:case f:case p:return e;default:switch(e=e&&e.$$typeof){case c:case s:case h:case d:case l:return e;default:return t}}case n:return t}}}(e)===i}},23058:(e,t,r)=>{"use strict";var n=r(58009),i=r(94924),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,s=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var h=o(e,(f=s(function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,r,n,i]))[0],f[1]);return c(function(){d.hasValue=!0,d.value=h},[h]),u(h),h}},34996:(e,t,r)=>{"use strict";var n=r(58009);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},73450:(e,t,r)=>{"use strict";e.exports=r(23058)},14202:(e,t,r)=>{"use strict";r(34996)},38307:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\analytics\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[884,162,999,756,880],()=>r(33811));module.exports=n})();