(()=>{var e={};e.id=401,e.ids=[401],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},91309:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>b,tree:()=>d});var o=r(70260),s=r(28203),n=r(25155),i=r.n(n),a=r(67292),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["books",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,23361)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\books\\[slug]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(r.bind(r,74866)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\books\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"]}],p=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\books\\[slug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},b=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/books/[slug]/page",pathname:"/books/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54587:(e,t,r)=>{Promise.resolve().then(r.bind(r,23361))},65203:(e,t,r)=>{Promise.resolve().then(r.bind(r,94893))},96487:()=>{},78335:()=>{},94893:()=>{throw Error('Module parse failed: Identifier \'useToast\' has already been declared (13:9)\nFile was processed with these loaders:\n * ./node_modules/next/dist/build/webpack/loaders/next-flight-client-module-loader.js\n * ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js\nYou may need an additional loader to handle the result of these loaders.\n| import { OfflineBookToggle } from "@/components/offline-book-toggle";\n| import { getOfflineBook, saveReadingProgress, getReadingProgress, isBookAvailableOffline } from "@/lib/offline-storage";\n> import { useToast } from "@/hooks/use-toast";\n| import { ReadingProgressBar } from "@/components/reading-progress-bar";\n| import { HighlightControls } from "@/components/highlight-controls";')},23361:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\books\\\\[slug]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\books\\[slug]\\page.tsx","default")},74866:(e,t,r)=>{"use strict";function o(){return null}r.r(t),r.d(t,{default:()=>o})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[884,999],()=>r(91309));module.exports=o})();