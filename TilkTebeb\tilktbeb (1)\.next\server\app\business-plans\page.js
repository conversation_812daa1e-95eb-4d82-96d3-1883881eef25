(()=>{var e={};e.id=780,e.ids=[780],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},70181:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=t(70260),i=t(28203),r=t(25155),n=t.n(r),l=t(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["business-plans",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38635)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\business-plans\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\business-plans\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/business-plans/page",pathname:"/business-plans",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},74841:(e,s,t)=>{Promise.resolve().then(t.bind(t,38635))},11289:(e,s,t)=>{Promise.resolve().then(t.bind(t,84364))},84364:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(45512),i=t(58009),r=t(28531),n=t.n(r),l=t(87021),d=t(97643),o=t(69193),c=t(72734),m=t(82901);function u(){let[e,s]=(0,i.useState)([]),[t,r]=(0,i.useState)(!0),[u,h]=(0,i.useState)(null),[p,g]=(0,i.useState)("small"),x=e.filter(e=>e.size===p);return(0,a.jsxs)("div",{className:"container py-8 md:py-12",children:[(0,a.jsx)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight mb-2",children:"Business Plans"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Detailed business plans for different business sizes"})]})}),(0,a.jsxs)(o.Tabs,{defaultValue:"small",value:p,onValueChange:e=>g(e),className:"mb-12",children:[(0,a.jsxs)(o.TabsList,{className:"mb-8",children:[(0,a.jsx)(o.TabsTrigger,{value:"small",children:"Small Business"}),(0,a.jsx)(o.TabsTrigger,{value:"medium",children:"Medium Business"}),(0,a.jsx)(o.TabsTrigger,{value:"large",children:"Large Business"})]}),(0,a.jsx)(o.TabsContent,{value:"small",children:t?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,s)=>(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-40 bg-muted rounded-lg mb-3"}),(0,a.jsx)("div",{className:"h-5 bg-muted rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-full mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-5/6"})]},s))}):u?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:u}),(0,a.jsx)(l.$,{variant:"outline",onClick:()=>window.location.reload(),children:"Try Again"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:x.map(e=>(0,a.jsxs)(d.Zp,{className:"overflow-hidden",children:[(0,a.jsx)(d.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.ZB,{children:e.title}),(0,a.jsx)(d.BT,{children:e.category})]}),e.isPremium&&(0,a.jsxs)("div",{className:"bg-primary/10 text-primary text-xs px-2 py-1 rounded-full flex items-center gap-1",children:[(0,a.jsx)(c.A,{className:"h-3 w-3"}),"Premium"]})]})}),(0,a.jsxs)(d.Wu,{className:"pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Small Business"})]}),(0,a.jsx)("p",{className:"text-sm",children:e.description})]}),(0,a.jsx)(d.wL,{children:(0,a.jsx)(n(),{href:`/business-plans/${e.size}/${e.id}`,className:"w-full",children:(0,a.jsx)(l.$,{variant:e.isPremium?"outline":"default",className:"w-full",children:e.isPremium?"Unlock Plan":"View Plan"})})})]},e.id))})}),(0,a.jsx)(o.TabsContent,{value:"medium",children:t?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,s)=>(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-40 bg-muted rounded-lg mb-3"}),(0,a.jsx)("div",{className:"h-5 bg-muted rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-full mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-5/6"})]},s))}):u?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:u}),(0,a.jsx)(l.$,{variant:"outline",onClick:()=>window.location.reload(),children:"Try Again"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:x.map(e=>(0,a.jsxs)(d.Zp,{className:"overflow-hidden",children:[(0,a.jsx)(d.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.ZB,{children:e.title}),(0,a.jsx)(d.BT,{children:e.category})]}),e.isPremium&&(0,a.jsxs)("div",{className:"bg-primary/10 text-primary text-xs px-2 py-1 rounded-full flex items-center gap-1",children:[(0,a.jsx)(c.A,{className:"h-3 w-3"}),"Premium"]})]})}),(0,a.jsxs)(d.Wu,{className:"pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Medium Business"})]}),(0,a.jsx)("p",{className:"text-sm",children:e.description})]}),(0,a.jsx)(d.wL,{children:(0,a.jsx)(n(),{href:`/business-plans/${e.size}/${e.id}`,className:"w-full",children:(0,a.jsx)(l.$,{variant:"outline",className:"w-full",children:"Unlock Plan"})})})]},e.id))})}),(0,a.jsx)(o.TabsContent,{value:"large",children:t?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,s)=>(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-40 bg-muted rounded-lg mb-3"}),(0,a.jsx)("div",{className:"h-5 bg-muted rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-full mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-5/6"})]},s))}):u?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:u}),(0,a.jsx)(l.$,{variant:"outline",onClick:()=>window.location.reload(),children:"Try Again"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:x.map(e=>(0,a.jsxs)(d.Zp,{className:"overflow-hidden",children:[(0,a.jsx)(d.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.ZB,{children:e.title}),(0,a.jsx)(d.BT,{children:e.category})]}),e.isPremium&&(0,a.jsxs)("div",{className:"bg-primary/10 text-primary text-xs px-2 py-1 rounded-full flex items-center gap-1",children:[(0,a.jsx)(c.A,{className:"h-3 w-3"}),"Premium"]})]})}),(0,a.jsxs)(d.Wu,{className:"pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Large Business"})]}),(0,a.jsx)("p",{className:"text-sm",children:e.description})]}),(0,a.jsx)(d.wL,{children:(0,a.jsx)(n(),{href:`/business-plans/${e.size}/${e.id}`,className:"w-full",children:(0,a.jsx)(l.$,{variant:"outline",className:"w-full",children:"Unlock Plan"})})})]},e.id))})})]}),(0,a.jsx)("div",{className:"bg-primary/5 rounded-lg p-6 md:p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Unlock All Premium Business Plans"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"Get lifetime access to all our premium business plans with a one-time payment. Choose the business size that fits your needs or get access to all plans."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)(n(),{href:"/pricing",children:(0,a.jsx)(l.$,{size:"lg",children:"View Pricing"})}),(0,a.jsx)(n(),{href:"/signup",children:(0,a.jsx)(l.$,{variant:"outline",size:"lg",children:"Sign Up Now"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-background rounded-md p-4 text-center shadow-sm",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"Small"}),(0,a.jsx)("p",{className:"text-3xl font-bold mb-1",children:"$149"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"one-time payment"})]}),(0,a.jsxs)("div",{className:"bg-background rounded-md p-4 text-center shadow-sm",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"Medium"}),(0,a.jsx)("p",{className:"text-3xl font-bold mb-1",children:"$249"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"one-time payment"})]}),(0,a.jsxs)("div",{className:"bg-background rounded-md p-4 text-center shadow-sm",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"Large"}),(0,a.jsx)("p",{className:"text-3xl font-bold mb-1",children:"$399"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"one-time payment"})]})]})]})})]})}t(97730)},69193:(e,s,t)=>{"use strict";t.d(s,{Tabs:()=>l,TabsContent:()=>c,TabsList:()=>d,TabsTrigger:()=>o});var a=t(45512),i=t(58009),r=t(55613),n=t(59462);let l=r.bL,d=i.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));d.displayName=r.B8.displayName;let o=i.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));o.displayName=r.l9.displayName;let c=i.forwardRef(({className:e,...s},t)=>(0,a.jsx)(r.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=r.UC.displayName},97730:(e,s,t)=>{"use strict";t.d(s,{FH:()=>l,lj:()=>o,hS:()=>d,Pr:()=>c}),t(58009);let a=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:8000/api";class i{static{this.baseUrl=a}static async fetchWithErrorHandling(e,s={}){let t=`${this.baseUrl}${e}`;try{let e=await fetch(t,{headers:{"Content-Type":"application/json",...s.headers},...s});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(s){throw console.error(`API call failed for ${e}:`,s),s}}static async getBooks(e){let s=new URLSearchParams;e?.category&&s.append("category",e.category),e?.query&&s.append("query",e.query),e?.premium!==void 0&&s.append("premium",e.premium.toString()),e?.limit&&s.append("limit",e.limit.toString());let t=`/books/${s.toString()?`?${s.toString()}`:""}`;return this.fetchWithErrorHandling(t)}static async getBookById(e){return this.fetchWithErrorHandling(`/books/${e}/`)}static async getBusinessPlans(e){let s=new URLSearchParams;e?.size&&s.append("size",e.size),e?.category&&s.append("category",e.category),e?.query&&s.append("query",e.query),e?.premium!==void 0&&s.append("premium",e.premium.toString());let t=`/business-plans/${s.toString()?`?${s.toString()}`:""}`;return this.fetchWithErrorHandling(t)}static async getBusinessPlanById(e){return this.fetchWithErrorHandling(`/business-plans/${e}/`)}static async login(e){return this.fetchWithErrorHandling("/auth/login/",{method:"POST",body:JSON.stringify(e)})}static async register(e){return this.fetchWithErrorHandling("/auth/register/",{method:"POST",body:JSON.stringify(e)})}static async getUserProfile(e){return this.fetchWithErrorHandling(`/user/${e}/`)}static async updateUserProfile(e,s){return this.fetchWithErrorHandling(`/user/${e}/`,{method:"PATCH",body:JSON.stringify(s)})}static async processPayment(e){return this.fetchWithErrorHandling("/payments/",{method:"POST",body:JSON.stringify(e)})}static async verifyPayment(e){return this.fetchWithErrorHandling(`/payments/verify/${e}/`)}static async getPaymentHistory(e){return this.fetchWithErrorHandling(`/payments/?userId=${e}`)}static async getAdminStats(){return this.fetchWithErrorHandling("/admin/stats/")}static async getAdminUsers(e){let s=new URLSearchParams;e?.page&&s.append("page",e.page.toString()),e?.limit&&s.append("limit",e.limit.toString()),e?.search&&s.append("search",e.search);let t=`/admin/users/${s.toString()?`?${s.toString()}`:""}`;return this.fetchWithErrorHandling(t)}static async getUserBookmarks(e){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/`)}static async addBookmark(e,s){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/`,{method:"POST",body:JSON.stringify({bookId:s})})}static async removeBookmark(e,s){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/${s}/`,{method:"DELETE"})}static async getUserAnalytics(e){return this.fetchWithErrorHandling(`/user/${e}/analytics/`)}}var r=t(12362);class n{static delay(e=500){return new Promise(s=>setTimeout(s,e))}static async getBooks(e){await this.delay();let s=[...r.tn];if(e?.category&&"all"!==e.category&&(s=s.filter(s=>s.category.toLowerCase()===e.category.toLowerCase())),e?.query){let t=e.query.toLowerCase();s=s.filter(e=>e.title.toLowerCase().includes(t)||e.author.toLowerCase().includes(t))}if(e?.premium!==void 0){let t=r.hr.filter(s=>s.isPremium===e.premium).map(e=>e.id);s=s.filter(e=>t.includes(e.id))}return e?.limit&&(s=s.slice(0,e.limit)),s}static async getBookById(e){await this.delay();let s=r.hr.find(s=>s.id===e);if(!s)throw Error(`Book with id ${e} not found`);return s}static async getBusinessPlans(e){await this.delay();let s=[...r.RJ];if(e?.size&&"all"!==e.size&&(s=s.filter(s=>s.size===e.size)),e?.category&&"all"!==e.category&&(s=s.filter(s=>s.category.toLowerCase()===e.category.toLowerCase())),e?.query){let t=e.query.toLowerCase();s=s.filter(e=>e.title.toLowerCase().includes(t)||e.category.toLowerCase().includes(t))}return e?.premium!==void 0&&(s=s.filter(s=>s.isPremium===e.premium)),s}static async getBusinessPlanById(e){await this.delay();let s=r.Z9.find(s=>s.id===e);if(!s)throw Error(`Business plan with id ${e} not found`);return s}static async login(e){return await this.delay(),{id:"user-1",firstName:"John",lastName:"Doe",email:e.email,plan:"medium",token:"mock-jwt-token-123"}}static async register(e){return await this.delay(),{id:`user-${Date.now()}`,firstName:e.firstName,lastName:e.lastName,email:e.email,plan:"base",token:"mock-jwt-token-456"}}static async getUserProfile(e){return await this.delay(),{user:{id:e,firstName:"John",lastName:"Doe",email:"<EMAIL>",plan:"medium"},activities:[{id:"activity-1",userId:e,type:"book_read",itemId:"the-psychology-of-money",itemTitle:"The Psychology of Money",timestamp:new Date(Date.now()-864e5).toISOString()},{id:"activity-2",userId:e,type:"plan_viewed",itemId:"small-1",itemTitle:"Local Coffee Shop",timestamp:new Date(Date.now()-1728e5).toISOString()}]}}static async updateUserProfile(e,s){return await this.delay(),{id:e,firstName:s.firstName||"John",lastName:s.lastName||"Doe",email:s.email||"<EMAIL>",plan:"medium"}}static async processPayment(e){return await this.delay(1e3),{success:!0,transactionId:`TRX-${Date.now()}-${Math.floor(1e3*Math.random())}`,message:"Payment processed successfully",timestamp:new Date().toISOString(),plan:e.plan}}static async verifyPayment(e){return await this.delay(),{success:!0,status:"completed",message:"Payment verified successfully"}}static async getPaymentHistory(e){return await this.delay(),[{success:!0,transactionId:"TRX-123456789",message:"Payment processed successfully",timestamp:new Date(Date.now()-2592e6).toISOString(),plan:"medium"}]}static async getAdminStats(){return await this.delay(),{totalUsers:1250,totalBooks:r.hr.length,totalBusinessPlans:r.Z9.length,revenueThisMonth:15750}}static async getAdminUsers(e){await this.delay();let s=[{id:"user-1",firstName:"John",lastName:"Doe",email:"<EMAIL>",plan:"medium",createdAt:"2023-01-15T00:00:00Z"},{id:"user-2",firstName:"Jane",lastName:"Smith",email:"<EMAIL>",plan:"base",createdAt:"2023-03-22T00:00:00Z"}];return{users:s,total:s.length,page:e?.page||1,totalPages:1}}static async getUserBookmarks(e){return await this.delay(),r.tn.slice(0,2)}static async addBookmark(e,s){return await this.delay(),{success:!0}}static async removeBookmark(e,s){return await this.delay(),{success:!0}}static async getUserAnalytics(e){return await this.delay(),{readingStats:{booksRead:12,totalReadingTime:2400,averageReadingSpeed:250,streakDays:7},recentActivity:[{id:"activity-1",type:"book_read",itemTitle:"The Psychology of Money",timestamp:new Date(Date.now()-864e5).toISOString()},{id:"activity-2",type:"plan_viewed",itemTitle:"Local Coffee Shop",timestamp:new Date(Date.now()-1728e5).toISOString()}]}}}let l="true"===process.env.NEXT_PUBLIC_USE_MOCK_API?n:i,d=e=>e instanceof Error?e.message:"An unexpected error occurred";process.env.NEXT_PUBLIC_API_BASE_URL;let o={getToken:()=>null,setToken:e=>{},removeToken:()=>{},isAuthenticated:()=>!!o.getToken()},c={getCurrentUser:()=>null,setCurrentUser:e=>{},removeCurrentUser:()=>{},logout:()=>{o.removeToken(),c.removeCurrentUser()}}},82901:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},72734:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},38635:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\business-plans\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\business-plans\\page.tsx","default")},55613:(e,s,t)=>{"use strict";t.d(s,{B8:()=>A,UC:()=>B,bL:()=>S,l9:()=>$});var a=t(58009),i=t(31412),r=t(6004),n=t(48305),l=t(98060),d=t(30830),o=t(59018),c=t(13024),m=t(30096),u=t(45512),h="Tabs",[p,g]=(0,r.A)(h,[n.RG]),x=(0,n.RG)(),[f,y]=p(h),b=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:i,defaultValue:r,orientation:n="horizontal",dir:l,activationMode:p="automatic",...g}=e,x=(0,o.jH)(l),[y,b]=(0,c.i)({prop:a,onChange:i,defaultProp:r??"",caller:h});return(0,u.jsx)(f,{scope:t,baseId:(0,m.B)(),value:y,onValueChange:b,orientation:n,dir:x,activationMode:p,children:(0,u.jsx)(d.sG.div,{dir:x,"data-orientation":n,...g,ref:s})})});b.displayName=h;var j="TabsList",v=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...i}=e,r=y(j,t),l=x(t);return(0,u.jsx)(n.bL,{asChild:!0,...l,orientation:r.orientation,dir:r.dir,loop:a,children:(0,u.jsx)(d.sG.div,{role:"tablist","aria-orientation":r.orientation,...i,ref:s})})});v.displayName=j;var w="TabsTrigger",N=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:r=!1,...l}=e,o=y(w,t),c=x(t),m=T(o.baseId,a),h=C(o.baseId,a),p=a===o.value;return(0,u.jsx)(n.q7,{asChild:!0,...c,focusable:!r,active:p,children:(0,u.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:m,...l,ref:s,onMouseDown:(0,i.m)(e.onMouseDown,e=>{r||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(a)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(a)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;p||r||!e||o.onValueChange(a)})})})});N.displayName=w;var k="TabsContent",P=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,forceMount:r,children:n,...o}=e,c=y(k,t),m=T(c.baseId,i),h=C(c.baseId,i),p=i===c.value,g=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(l.C,{present:r||p,children:({present:t})=>(0,u.jsx)(d.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:h,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:g.current?"0s":void 0},children:t&&n})})});function T(e,s){return`${e}-trigger-${s}`}function C(e,s){return`${e}-content-${s}`}P.displayName=k;var S=b,A=v,$=N,B=P}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[884,999,756],()=>t(70181));module.exports=a})();