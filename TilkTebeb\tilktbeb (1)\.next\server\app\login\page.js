(()=>{var e={};e.id=520,e.ids=[520],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},31547:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(70260),a=s(28203),i=s(25155),n=s.n(i),o=s(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,43642)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],d=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\login\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48325:(e,t,s)=>{Promise.resolve().then(s.bind(s,43642))},61477:(e,t,s)=>{Promise.resolve().then(s.bind(s,74014))},74014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(45512),a=s(58009),i=s(28531),n=s.n(i),o=s(79334),l=s(87021),c=s(97643),d=s(25409),m=s(53261),u=s(4269),p=s(70801),h=s(97730);function y(){let[e,t]=(0,a.useState)(""),[s,i]=(0,a.useState)(""),[y,g]=(0,a.useState)(!1),f=(0,o.useRouter)(),{toast:w}=(0,p.dj)(),x=async t=>{if(t.preventDefault(),!e||!s){w({title:"Error",description:"Please enter both email and password",variant:"destructive"});return}try{g(!0);let t=await h.FH.login({email:e,password:s});t.token&&h.lj.setToken(t.token),h.Pr.setCurrentUser({id:t.id,firstName:t.firstName,lastName:t.lastName,email:t.email,plan:t.plan}),w({title:"Success",description:"You have successfully logged in"}),f.push("/account")}catch(e){console.error("Login error:",e),w({title:"Login Failed",description:(0,h.hS)(e),variant:"destructive"})}finally{g(!1)}};return(0,r.jsx)("div",{className:"container flex items-center justify-center min-h-[calc(100vh-16rem)] py-8 md:py-12",children:(0,r.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,r.jsxs)(c.aR,{className:"space-y-1",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-6 w-6 text-primary"}),(0,r.jsx)("span",{className:"font-bold text-xl",children:"TILkTBEB"})]})}),(0,r.jsx)(c.ZB,{className:"text-2xl text-center",children:"Welcome back"}),(0,r.jsx)(c.BT,{className:"text-center",children:"Enter your credentials to access your account"})]}),(0,r.jsxs)("form",{onSubmit:x,children:[(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.Label,{htmlFor:"email",children:"Email"}),(0,r.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>t(e.target.value),disabled:y})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(m.Label,{htmlFor:"password",children:"Password"}),(0,r.jsx)(n(),{href:"/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot password?"})]}),(0,r.jsx)(d.p,{id:"password",type:"password",value:s,onChange:e=>i(e.target.value),disabled:y})]})]}),(0,r.jsxs)(c.wL,{className:"flex flex-col",children:[(0,r.jsx)(l.$,{className:"w-full mb-4",type:"submit",disabled:y,children:y?"Logging in...":"Log in"}),(0,r.jsxs)("p",{className:"text-sm text-center text-muted-foreground",children:["Don't have an account?"," ",(0,r.jsx)(n(),{href:"/signup",className:"text-primary hover:underline",children:"Sign up"})]})]})]})]})})}},53261:(e,t,s)=>{"use strict";s.d(t,{Label:()=>d});var r=s(45512),a=s(58009),i=s(30830),n=a.forwardRef((e,t)=>(0,r.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=s(21643),l=s(59462);let c=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n,{ref:s,className:(0,l.cn)(c(),e),...t}));d.displayName=n.displayName},97730:(e,t,s)=>{"use strict";s.d(t,{FH:()=>o,lj:()=>c,hS:()=>l,Pr:()=>d}),s(58009);let r=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:8000/api";class a{static{this.baseUrl=r}static async fetchWithErrorHandling(e,t={}){let s=`${this.baseUrl}${e}`;try{let e=await fetch(s,{headers:{"Content-Type":"application/json",...t.headers},...t});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(t){throw console.error(`API call failed for ${e}:`,t),t}}static async getBooks(e){let t=new URLSearchParams;e?.category&&t.append("category",e.category),e?.query&&t.append("query",e.query),e?.premium!==void 0&&t.append("premium",e.premium.toString()),e?.limit&&t.append("limit",e.limit.toString());let s=`/books/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(s)}static async getBookById(e){return this.fetchWithErrorHandling(`/books/${e}/`)}static async getBusinessPlans(e){let t=new URLSearchParams;e?.size&&t.append("size",e.size),e?.category&&t.append("category",e.category),e?.query&&t.append("query",e.query),e?.premium!==void 0&&t.append("premium",e.premium.toString());let s=`/business-plans/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(s)}static async getBusinessPlanById(e){return this.fetchWithErrorHandling(`/business-plans/${e}/`)}static async login(e){return this.fetchWithErrorHandling("/auth/login/",{method:"POST",body:JSON.stringify(e)})}static async register(e){return this.fetchWithErrorHandling("/auth/register/",{method:"POST",body:JSON.stringify(e)})}static async getUserProfile(e){return this.fetchWithErrorHandling(`/user/${e}/`)}static async updateUserProfile(e,t){return this.fetchWithErrorHandling(`/user/${e}/`,{method:"PATCH",body:JSON.stringify(t)})}static async processPayment(e){return this.fetchWithErrorHandling("/payments/",{method:"POST",body:JSON.stringify(e)})}static async verifyPayment(e){return this.fetchWithErrorHandling(`/payments/verify/${e}/`)}static async getPaymentHistory(e){return this.fetchWithErrorHandling(`/payments/?userId=${e}`)}static async getAdminStats(){return this.fetchWithErrorHandling("/admin/stats/")}static async getAdminUsers(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search);let s=`/admin/users/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(s)}static async getUserBookmarks(e){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/`)}static async addBookmark(e,t){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/`,{method:"POST",body:JSON.stringify({bookId:t})})}static async removeBookmark(e,t){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/${t}/`,{method:"DELETE"})}static async getUserAnalytics(e){return this.fetchWithErrorHandling(`/user/${e}/analytics/`)}}var i=s(12362);class n{static delay(e=500){return new Promise(t=>setTimeout(t,e))}static async getBooks(e){await this.delay();let t=[...i.tn];if(e?.category&&"all"!==e.category&&(t=t.filter(t=>t.category.toLowerCase()===e.category.toLowerCase())),e?.query){let s=e.query.toLowerCase();t=t.filter(e=>e.title.toLowerCase().includes(s)||e.author.toLowerCase().includes(s))}if(e?.premium!==void 0){let s=i.hr.filter(t=>t.isPremium===e.premium).map(e=>e.id);t=t.filter(e=>s.includes(e.id))}return e?.limit&&(t=t.slice(0,e.limit)),t}static async getBookById(e){await this.delay();let t=i.hr.find(t=>t.id===e);if(!t)throw Error(`Book with id ${e} not found`);return t}static async getBusinessPlans(e){await this.delay();let t=[...i.RJ];if(e?.size&&"all"!==e.size&&(t=t.filter(t=>t.size===e.size)),e?.category&&"all"!==e.category&&(t=t.filter(t=>t.category.toLowerCase()===e.category.toLowerCase())),e?.query){let s=e.query.toLowerCase();t=t.filter(e=>e.title.toLowerCase().includes(s)||e.category.toLowerCase().includes(s))}return e?.premium!==void 0&&(t=t.filter(t=>t.isPremium===e.premium)),t}static async getBusinessPlanById(e){await this.delay();let t=i.Z9.find(t=>t.id===e);if(!t)throw Error(`Business plan with id ${e} not found`);return t}static async login(e){return await this.delay(),{id:"user-1",firstName:"John",lastName:"Doe",email:e.email,plan:"medium",token:"mock-jwt-token-123"}}static async register(e){return await this.delay(),{id:`user-${Date.now()}`,firstName:e.firstName,lastName:e.lastName,email:e.email,plan:"base",token:"mock-jwt-token-456"}}static async getUserProfile(e){return await this.delay(),{user:{id:e,firstName:"John",lastName:"Doe",email:"<EMAIL>",plan:"medium"},activities:[{id:"activity-1",userId:e,type:"book_read",itemId:"the-psychology-of-money",itemTitle:"The Psychology of Money",timestamp:new Date(Date.now()-864e5).toISOString()},{id:"activity-2",userId:e,type:"plan_viewed",itemId:"small-1",itemTitle:"Local Coffee Shop",timestamp:new Date(Date.now()-1728e5).toISOString()}]}}static async updateUserProfile(e,t){return await this.delay(),{id:e,firstName:t.firstName||"John",lastName:t.lastName||"Doe",email:t.email||"<EMAIL>",plan:"medium"}}static async processPayment(e){return await this.delay(1e3),{success:!0,transactionId:`TRX-${Date.now()}-${Math.floor(1e3*Math.random())}`,message:"Payment processed successfully",timestamp:new Date().toISOString(),plan:e.plan}}static async verifyPayment(e){return await this.delay(),{success:!0,status:"completed",message:"Payment verified successfully"}}static async getPaymentHistory(e){return await this.delay(),[{success:!0,transactionId:"TRX-123456789",message:"Payment processed successfully",timestamp:new Date(Date.now()-2592e6).toISOString(),plan:"medium"}]}static async getAdminStats(){return await this.delay(),{totalUsers:1250,totalBooks:i.hr.length,totalBusinessPlans:i.Z9.length,revenueThisMonth:15750}}static async getAdminUsers(e){await this.delay();let t=[{id:"user-1",firstName:"John",lastName:"Doe",email:"<EMAIL>",plan:"medium",createdAt:"2023-01-15T00:00:00Z"},{id:"user-2",firstName:"Jane",lastName:"Smith",email:"<EMAIL>",plan:"base",createdAt:"2023-03-22T00:00:00Z"}];return{users:t,total:t.length,page:e?.page||1,totalPages:1}}static async getUserBookmarks(e){return await this.delay(),i.tn.slice(0,2)}static async addBookmark(e,t){return await this.delay(),{success:!0}}static async removeBookmark(e,t){return await this.delay(),{success:!0}}static async getUserAnalytics(e){return await this.delay(),{readingStats:{booksRead:12,totalReadingTime:2400,averageReadingSpeed:250,streakDays:7},recentActivity:[{id:"activity-1",type:"book_read",itemTitle:"The Psychology of Money",timestamp:new Date(Date.now()-864e5).toISOString()},{id:"activity-2",type:"plan_viewed",itemTitle:"Local Coffee Shop",timestamp:new Date(Date.now()-1728e5).toISOString()}]}}}let o="true"===process.env.NEXT_PUBLIC_USE_MOCK_API?n:a,l=e=>e instanceof Error?e.message:"An unexpected error occurred";process.env.NEXT_PUBLIC_API_BASE_URL;let c={getToken:()=>null,setToken:e=>{},removeToken:()=>{},isAuthenticated:()=>!!c.getToken()},d={getCurrentUser:()=>null,setCurrentUser:e=>{},removeCurrentUser:()=>{},logout:()=>{c.removeToken(),d.removeCurrentUser()}}},43642:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\login\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[884,999,756],()=>s(31547));module.exports=r})();