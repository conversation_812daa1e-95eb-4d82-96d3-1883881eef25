(()=>{var e={};e.id=698,e.ids=[698],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50719:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=t(70260),r=t(28203),i=t(25155),n=t.n(i),l=t(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5128)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,54899)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\admin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8419:(e,s,t)=>{Promise.resolve().then(t.bind(t,5128))},74075:(e,s,t)=>{Promise.resolve().then(t.bind(t,80556))},57136:(e,s,t)=>{Promise.resolve().then(t.bind(t,5986))},91984:(e,s,t)=>{Promise.resolve().then(t.bind(t,48790))},80556:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(45512),r=t(58009),i=t(79334),n=t(97643),l=t(69193),o=t(87103),d=t(86235),c=t(4269),u=t(82901),x=t(64977),h=t(91124),m=t(97730);function p(){(0,i.useRouter)();let[e,s]=(0,r.useState)(!1),[t,p]=(0,r.useState)(null),[v,j]=(0,r.useState)(!0),[f,y]=(0,r.useState)(null),b=async()=>{try{j(!0);let e=await m.FH.getAdminStats();p(e)}catch(e){y((0,m.hS)(e)),console.error("Error fetching admin stats:",e)}finally{j(!1)}};return e?v?(0,a.jsx)("div",{className:"container py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsx)(d.A,{className:"h-8 w-8 animate-spin"})})}):f?(0,a.jsx)("div",{className:"container py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:f}),(0,a.jsx)("button",{onClick:b,className:"btn",children:"Retry"})]})}):(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Admin Dashboard"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"Total Books"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:t?.totalBooks||0})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"h-6 w-6 text-primary"})})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"Business Plans"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:t?.totalBusinessPlans||0})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-primary"})})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"Total Users"}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:t?.totalUsers.toLocaleString()||0})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"h-6 w-6 text-primary"})})]})}),(0,a.jsx)(n.Zp,{children:(0,a.jsxs)(n.Wu,{className:"p-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"Revenue This Month"}),(0,a.jsxs)("p",{className:"text-3xl font-bold",children:["$",t?.revenueThisMonth.toLocaleString()||0]})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-primary"})})]})})]}),(0,a.jsxs)(l.Tabs,{defaultValue:"overview",className:"w-full",children:[(0,a.jsxs)(l.TabsList,{children:[(0,a.jsx)(l.TabsTrigger,{value:"overview",children:"Overview"}),(0,a.jsx)(l.TabsTrigger,{value:"sales",children:"Sales"}),(0,a.jsx)(l.TabsTrigger,{value:"users",children:"Users"})]}),(0,a.jsx)(l.TabsContent,{value:"overview",className:"mt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Revenue Overview"}),(0,a.jsx)(n.BT,{children:"Monthly revenue for the current year"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(o.LineChart,{data:[{name:"Jan",value:15e3},{name:"Feb",value:18500},{name:"Mar",value:22e3},{name:"Apr",value:21e3},{name:"May",value:24500},{name:"Jun",value:28e3},{name:"Jul",value:30500},{name:"Aug",value:32e3},{name:"Sep",value:33500},{name:"Oct",value:29e3},{name:"Nov",value:26e3},{name:"Dec",value:24500}],xAxisKey:"name",yAxisKey:"value",height:300})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"New Users"}),(0,a.jsx)(n.BT,{children:"New user registrations by month"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(o.BarChart,{data:[{name:"Jan",value:120},{name:"Feb",value:145},{name:"Mar",value:178},{name:"Apr",value:165},{name:"May",value:198},{name:"Jun",value:220},{name:"Jul",value:235},{name:"Aug",value:253},{name:"Sep",value:268},{name:"Oct",value:230},{name:"Nov",value:210},{name:"Dec",value:195}],xAxisKey:"name",yAxisKey:"value",height:300})})]})]})}),(0,a.jsx)(l.TabsContent,{value:"sales",className:"mt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Sales by Plan"}),(0,a.jsx)(n.BT,{children:"Distribution of plan sales"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(o.PieChart,{data:[{name:"Base Access",value:45},{name:"Small Business",value:25},{name:"Medium Business",value:20},{name:"Large Business",value:10}],height:300})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Revenue Growth"}),(0,a.jsx)(n.BT,{children:"Year-over-year revenue growth"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(o.LineChart,{data:[{name:"2021",value:12e4},{name:"2022",value:18e4},{name:"2023",value:248950},{name:"2024 (Projected)",value:31e4}],xAxisKey:"name",yAxisKey:"value",height:300})})]})]})}),(0,a.jsx)(l.TabsContent,{value:"users",className:"mt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"User Demographics"}),(0,a.jsx)(n.BT,{children:"Age distribution of users"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(o.BarChart,{data:[{name:"18-24",value:15},{name:"25-34",value:40},{name:"35-44",value:25},{name:"45-54",value:12},{name:"55+",value:8}],xAxisKey:"name",yAxisKey:"value",height:300})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"User Engagement"}),(0,a.jsx)(n.BT,{children:"Average time spent by users"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(o.LineChart,{data:[{name:"Jan",value:45},{name:"Feb",value:48},{name:"Mar",value:52},{name:"Apr",value:49},{name:"May",value:55},{name:"Jun",value:58},{name:"Jul",value:61},{name:"Aug",value:62},{name:"Sep",value:63},{name:"Oct",value:59},{name:"Nov",value:56},{name:"Dec",value:54}],xAxisKey:"name",yAxisKey:"value",height:300})})]})]})})]})]}):null}},48790:(e,s,t)=>{"use strict";t.d(s,{AdminSidebar:()=>A});var a=t(45512),r=t(58009),i=t(28531),n=t.n(i),l=t(79334),o=t(41680);let d=(0,o.A)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var c=t(4269),u=t(82901),x=t(64977),h=t(91124);let m=(0,o.A)("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]);var p=t(94520);let v=(0,o.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var j=t(87021),f=t(30830),y="horizontal",b=["horizontal","vertical"],g=r.forwardRef((e,s)=>{let{decorative:t,orientation:r=y,...i}=e,n=b.includes(r)?r:y;return(0,a.jsx)(f.sG.div,{"data-orientation":n,...t?{role:"none"}:{"aria-orientation":"vertical"===n?n:void 0,role:"separator"},...i,ref:s})});g.displayName="Separator";var w=t(59462);let k=r.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...r},i)=>(0,a.jsx)(g,{ref:i,decorative:t,orientation:s,className:(0,w.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));k.displayName=g.displayName;var N=t(70801);function A(){let e=(0,l.usePathname)(),s=(0,l.useRouter)(),{toast:t}=(0,N.dj)(),[i,o]=(0,r.useState)(!1),f=()=>{localStorage.removeItem("adminUser"),t({title:"Logged out",description:"You have been logged out successfully"}),s.push("/admin/login")},y=[{title:"Dashboard",href:"/admin",icon:d},{title:"Books",href:"/admin/books",icon:c.A},{title:"Business Plans",href:"/admin/business-plans",icon:u.A},{title:"Users",href:"/admin/users",icon:x.A},{title:"Orders",href:"/admin/orders",icon:h.A},{title:"Analytics",href:"/admin/analytics",icon:m},{title:"Settings",href:"/admin/settings",icon:p.A}];return(0,a.jsxs)("div",{className:(0,w.cn)("h-screen bg-muted/20 border-r flex flex-col transition-all duration-300 ease-in-out",i?"w-16":"w-64"),children:[(0,a.jsxs)("div",{className:"flex items-center h-16 px-4 border-b",children:[!i&&(0,a.jsxs)(n(),{href:"/admin",className:"flex items-center gap-2 font-bold text-lg",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-primary"}),(0,a.jsx)("span",{children:"TelkTibeb Admin"})]}),i&&(0,a.jsx)(n(),{href:"/admin",className:"mx-auto",children:(0,a.jsx)(c.A,{className:"h-7 w-7 text-primary"})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto py-4",children:(0,a.jsx)("nav",{className:"px-2 space-y-1",children:y.map(s=>(0,a.jsxs)(n(),{href:s.href,className:(0,w.cn)("flex items-center gap-3 px-3 py-2 rounded-md transition-colors",e===s.href||"/admin"!==s.href&&e?.startsWith(s.href)?"bg-primary/10 text-primary":"text-muted-foreground hover:bg-muted hover:text-foreground",i&&"justify-center px-0"),children:[(0,a.jsx)(s.icon,{className:(0,w.cn)("h-5 w-5",i&&"h-6 w-6")}),!i&&(0,a.jsx)("span",{children:s.title})]},s.href))})}),(0,a.jsx)("div",{className:"p-4 border-t",children:i?(0,a.jsxs)("div",{className:"flex flex-col gap-4 items-center",children:[(0,a.jsxs)(j.$,{variant:"outline",size:"icon",onClick:()=>o(!1),children:[(0,a.jsx)("span",{className:"sr-only",children:"Expand"}),(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4",children:[(0,a.jsx)("rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"}),(0,a.jsx)("path",{d:"M15 3v18"}),(0,a.jsx)("path",{d:"m8 9 3 3-3 3"})]})]}),(0,a.jsx)(k,{}),(0,a.jsx)(j.$,{variant:"outline",size:"icon",className:"text-destructive hover:text-destructive",onClick:f,children:(0,a.jsx)(v,{className:"h-4 w-4"})})]}):(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)(j.$,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>o(!0),children:[(0,a.jsx)("span",{className:"sr-only",children:"Collapse"}),(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4 mr-2",children:[(0,a.jsx)("rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"}),(0,a.jsx)("path",{d:"M9 3v18"}),(0,a.jsx)("path",{d:"m16 15-3-3 3-3"})]}),"Collapse"]}),(0,a.jsx)(k,{}),(0,a.jsxs)(j.$,{variant:"outline",className:"w-full justify-start text-destructive hover:text-destructive",onClick:f,children:[(0,a.jsx)(v,{className:"h-4 w-4 mr-2"}),"Logout"]})]})})]})}},82901:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},91124:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},64977:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(41680).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},54899:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>i});var a=t(62740),r=t(5986);let i={title:"Admin Dashboard - TelkTibeb",description:"Manage books, business plans, and users"};function n({children:e}){return(0,a.jsxs)("div",{className:"flex min-h-screen",children:[(0,a.jsx)(r.AdminSidebar,{}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsx)("main",{className:"p-6",children:e})})]})}},5128:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\admin\\page.tsx","default")},5986:(e,s,t)=>{"use strict";t.d(s,{AdminSidebar:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call AdminSidebar() from the server but AdminSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\admin\\admin-sidebar.tsx","AdminSidebar")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[884,162,999,756,880],()=>t(50719));module.exports=a})();