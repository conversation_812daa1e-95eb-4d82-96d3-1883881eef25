"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/book-card.tsx":
/*!**********************************!*\
  !*** ./components/book-card.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookCard: () => (/* binding */ BookCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ BookCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction BookCard(param) {\n    let { id, title, author, coverUrl, category, rating, price = 9.99, isPurchased = false, isFree = false } = param;\n    _s();\n    const [showPurchaseModal, setShowPurchaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const book = {\n        id,\n        title,\n        author,\n        coverUrl,\n        category,\n        rating,\n        price,\n        isPurchased,\n        isFree,\n        pages: 300,\n        language: \"English\",\n        summary: \"<p>Discover the insights and strategies that make this book a must-read in the \".concat(category, \" category.</p>\")\n    };\n    const isBookFree = isFree || price === 0;\n    const handlePurchaseClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setShowPurchaseModal(true);\n    };\n    const handlePurchaseComplete = ()=>{\n        // Handle successful purchase\n        console.log(\"Successfully purchased \".concat(title));\n    // You could update the book state here or refresh the page\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"book-card overflow-hidden h-full transition-all duration-300 hover:shadow-xl group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/books/\".concat(id),\n                className: \"block\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-[3/4] relative overflow-hidden rounded-t-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: coverUrl || \"/placeholder.svg\",\n                                alt: \"\".concat(title, \" book cover\"),\n                                className: \"object-cover w-full h-full transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 glass dark:glass-dark text-secondary text-xs px-2 py-1 rounded-full\",\n                                children: category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            isPurchased && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: \"Owned\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            isBookFree && !isPurchased && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: \"FREE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4 glass dark:glass-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-sm\",\n                                children: author\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 fill-secondary text-secondary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: rating.toFixed(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-primary\",\n                                        children: [\n                                            \"$\",\n                                            price.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                className: \"p-4 pt-0 glass dark:glass-dark\",\n                children: isPurchased ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full\",\n                    variant: \"outline\",\n                    children: \"Read Now\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full gap-2\",\n                    onClick: handlePurchaseClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        \"Buy Now\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(BookCard, \"GMDkQOp6xhk7B/PDWUW4Vk/PMws=\");\n_c = BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvYm9vay1jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFNEI7QUFDSTtBQUNvQztBQUNyQjtBQUVZO0FBZ0JwRCxTQUFTUSxTQUFTLEtBQW1IO1FBQW5ILEVBQUVDLEVBQUUsRUFBRUMsS0FBSyxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsUUFBUSxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsSUFBSSxFQUFFQyxjQUFjLEtBQUssRUFBRUMsU0FBUyxLQUFLLEVBQWlCLEdBQW5IOztJQUN2QixNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUUzRCxNQUFNbUIsT0FBYTtRQUNqQlg7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUksT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFNBQVMsa0ZBQTJGLE9BQVRWLFVBQVM7SUFDdEc7SUFFQSxNQUFNVyxhQUFhUCxVQUFVRixVQUFVO0lBRXZDLE1BQU1VLHNCQUFzQixDQUFDQztRQUMzQkEsRUFBRUMsY0FBYztRQUNoQkQsRUFBRUUsZUFBZTtRQUNqQlQscUJBQXFCO0lBQ3ZCO0lBRUEsTUFBTVUseUJBQXlCO1FBQzdCLDZCQUE2QjtRQUM3QkMsUUFBUUMsR0FBRyxDQUFDLDBCQUFnQyxPQUFOckI7SUFDdEMsMkRBQTJEO0lBQzdEO0lBRUEscUJBQ0UsOERBQUNSLHFEQUFJQTtRQUFDOEIsV0FBVTs7MEJBQ2QsOERBQUNoQyxrREFBSUE7Z0JBQUNpQyxNQUFNLFVBQWEsT0FBSHhCO2dCQUFNdUIsV0FBVTs7a0NBQ3BDLDhEQUFDRTt3QkFBSUYsV0FBVTs7MENBQ2IsOERBQUNHO2dDQUNDQyxLQUFLeEIsWUFBWTtnQ0FDakJ5QixLQUFLLEdBQVMsT0FBTjNCLE9BQU07Z0NBQ2RzQixXQUFVOzs7Ozs7MENBRVosOERBQUNFO2dDQUFJRixXQUFVOzBDQUNabkI7Ozs7Ozs0QkFFRkcsNkJBQ0MsOERBQUNrQjtnQ0FBSUYsV0FBVTswQ0FBK0U7Ozs7Ozs0QkFJL0ZSLGNBQWMsQ0FBQ1IsNkJBQ2QsOERBQUNrQjtnQ0FBSUYsV0FBVTswQ0FBK0U7Ozs7Ozs7Ozs7OztrQ0FLbEcsOERBQUM3Qiw0REFBV0E7d0JBQUM2QixXQUFVOzswQ0FDckIsOERBQUNNO2dDQUFHTixXQUFVOzBDQUFpRnRCOzs7Ozs7MENBQy9GLDhEQUFDNkI7Z0NBQUVQLFdBQVU7MENBQWlDckI7Ozs7OzswQ0FDOUMsOERBQUN1QjtnQ0FBSUYsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFJRixXQUFVOzswREFDYiw4REFBQzFCLDZGQUFJQTtnREFBQzBCLFdBQVU7Ozs7OzswREFDaEIsOERBQUNRO2dEQUFLUixXQUFVOzBEQUF1QmxCLE9BQU8yQixPQUFPLENBQUM7Ozs7Ozs7Ozs7OztrREFFeEQsOERBQUNQO3dDQUFJRixXQUFVOzs0Q0FBaUM7NENBQzVDakIsTUFBTTBCLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLeEIsOERBQUNyQywyREFBVUE7Z0JBQUM0QixXQUFVOzBCQUNuQmhCLDRCQUNDLDhEQUFDWCx5REFBTUE7b0JBQUMyQixXQUFVO29CQUFTVSxTQUFROzhCQUFVOzs7Ozt5Q0FJN0MsOERBQUNyQyx5REFBTUE7b0JBQ0wyQixXQUFVO29CQUNWVyxTQUFTbEI7O3NDQUVULDhEQUFDbEIsNkZBQVlBOzRCQUFDeUIsV0FBVTs7Ozs7O3dCQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFjaEQ7R0E3RmdCeEI7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXERlc2t0b3BcXFByb2plY3RzXFxBc3Rld2FpXFxUaWxrVGViZWJcXHRpbGt0YmViICgxKVxcY29tcG9uZW50c1xcYm9vay1jYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxyXG5cclxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiXHJcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRGb290ZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIlxyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXHJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiXHJcbmltcG9ydCB7IFN0YXIsIFNob3BwaW5nQ2FydCwgQm9va09wZW4gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcclxuLy8gaW1wb3J0IHsgUHVyY2hhc2VNb2RhbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvcHVyY2hhc2UtbW9kYWxcIlxyXG5pbXBvcnQgdHlwZSB7IEJvb2sgfSBmcm9tIFwiQC90eXBlcy9ib29rXCJcclxuXHJcbmludGVyZmFjZSBCb29rQ2FyZFByb3BzIHtcclxuICBpZDogc3RyaW5nXHJcbiAgdGl0bGU6IHN0cmluZ1xyXG4gIGF1dGhvcjogc3RyaW5nXHJcbiAgY292ZXJVcmw6IHN0cmluZ1xyXG4gIGNhdGVnb3J5OiBzdHJpbmdcclxuICByYXRpbmc6IG51bWJlclxyXG4gIHByaWNlPzogbnVtYmVyXHJcbiAgaXNQdXJjaGFzZWQ/OiBib29sZWFuXHJcbiAgaXNGcmVlPzogYm9vbGVhblxyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gQm9va0NhcmQoeyBpZCwgdGl0bGUsIGF1dGhvciwgY292ZXJVcmwsIGNhdGVnb3J5LCByYXRpbmcsIHByaWNlID0gOS45OSwgaXNQdXJjaGFzZWQgPSBmYWxzZSwgaXNGcmVlID0gZmFsc2UgfTogQm9va0NhcmRQcm9wcykge1xyXG4gIGNvbnN0IFtzaG93UHVyY2hhc2VNb2RhbCwgc2V0U2hvd1B1cmNoYXNlTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpXHJcblxyXG4gIGNvbnN0IGJvb2s6IEJvb2sgPSB7XHJcbiAgICBpZCxcclxuICAgIHRpdGxlLFxyXG4gICAgYXV0aG9yLFxyXG4gICAgY292ZXJVcmwsXHJcbiAgICBjYXRlZ29yeSxcclxuICAgIHJhdGluZyxcclxuICAgIHByaWNlLFxyXG4gICAgaXNQdXJjaGFzZWQsXHJcbiAgICBpc0ZyZWUsXHJcbiAgICBwYWdlczogMzAwLFxyXG4gICAgbGFuZ3VhZ2U6IFwiRW5nbGlzaFwiLFxyXG4gICAgc3VtbWFyeTogYDxwPkRpc2NvdmVyIHRoZSBpbnNpZ2h0cyBhbmQgc3RyYXRlZ2llcyB0aGF0IG1ha2UgdGhpcyBib29rIGEgbXVzdC1yZWFkIGluIHRoZSAke2NhdGVnb3J5fSBjYXRlZ29yeS48L3A+YFxyXG4gIH1cclxuXHJcbiAgY29uc3QgaXNCb29rRnJlZSA9IGlzRnJlZSB8fCBwcmljZSA9PT0gMFxyXG5cclxuICBjb25zdCBoYW5kbGVQdXJjaGFzZUNsaWNrID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcclxuICAgIGUucHJldmVudERlZmF1bHQoKVxyXG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKVxyXG4gICAgc2V0U2hvd1B1cmNoYXNlTW9kYWwodHJ1ZSlcclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZVB1cmNoYXNlQ29tcGxldGUgPSAoKSA9PiB7XHJcbiAgICAvLyBIYW5kbGUgc3VjY2Vzc2Z1bCBwdXJjaGFzZVxyXG4gICAgY29uc29sZS5sb2coYFN1Y2Nlc3NmdWxseSBwdXJjaGFzZWQgJHt0aXRsZX1gKVxyXG4gICAgLy8gWW91IGNvdWxkIHVwZGF0ZSB0aGUgYm9vayBzdGF0ZSBoZXJlIG9yIHJlZnJlc2ggdGhlIHBhZ2VcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8Q2FyZCBjbGFzc05hbWU9XCJib29rLWNhcmQgb3ZlcmZsb3ctaGlkZGVuIGgtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2hhZG93LXhsIGdyb3VwXCI+XHJcbiAgICAgIDxMaW5rIGhyZWY9e2AvYm9va3MvJHtpZH1gfSBjbGFzc05hbWU9XCJibG9ja1wiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXNwZWN0LVszLzRdIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLXQteGxcIj5cclxuICAgICAgICAgIDxpbWdcclxuICAgICAgICAgICAgc3JjPXtjb3ZlclVybCB8fCBcIi9wbGFjZWhvbGRlci5zdmdcIn1cclxuICAgICAgICAgICAgYWx0PXtgJHt0aXRsZX0gYm9vayBjb3ZlcmB9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9iamVjdC1jb3ZlciB3LWZ1bGwgaC1mdWxsIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTUwMFwiXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiByaWdodC0yIGdsYXNzIGRhcms6Z2xhc3MtZGFyayB0ZXh0LXNlY29uZGFyeSB0ZXh0LXhzIHB4LTIgcHktMSByb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgICAge2NhdGVnb3J5fVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICB7aXNQdXJjaGFzZWQgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yIGxlZnQtMiBiZy1ncmVlbi01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHB4LTIgcHktMSByb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgICAgICBPd25lZFxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgICB7aXNCb29rRnJlZSAmJiAhaXNQdXJjaGFzZWQgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yIGxlZnQtMiBiZy1ncmVlbi01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHB4LTIgcHktMSByb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgICAgICBGUkVFXHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00IGdsYXNzIGRhcms6Z2xhc3MtZGFya1wiPlxyXG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1sZyBsaW5lLWNsYW1wLTEgZ3JvdXAtaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+e3RpdGxlfTwvaDM+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgdGV4dC1zbVwiPnthdXRob3J9PC9wPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbXQtMlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgPFN0YXIgY2xhc3NOYW1lPVwiaC00IHctNCBmaWxsLXNlY29uZGFyeSB0ZXh0LXNlY29uZGFyeVwiIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPntyYXRpbmcudG9GaXhlZCgxKX08L3NwYW4+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtcHJpbWFyeVwiPlxyXG4gICAgICAgICAgICAgICR7cHJpY2UudG9GaXhlZCgyKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICA8L0xpbms+XHJcbiAgICAgIDxDYXJkRm9vdGVyIGNsYXNzTmFtZT1cInAtNCBwdC0wIGdsYXNzIGRhcms6Z2xhc3MtZGFya1wiPlxyXG4gICAgICAgIHtpc1B1cmNoYXNlZCA/IChcclxuICAgICAgICAgIDxCdXR0b24gY2xhc3NOYW1lPVwidy1mdWxsXCIgdmFyaWFudD1cIm91dGxpbmVcIj5cclxuICAgICAgICAgICAgUmVhZCBOb3dcclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICkgOiAoXHJcbiAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBnYXAtMlwiXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVB1cmNoYXNlQ2xpY2t9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxTaG9wcGluZ0NhcnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgIEJ1eSBOb3dcclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvQ2FyZEZvb3Rlcj5cclxuXHJcbiAgICAgIHsvKiA8UHVyY2hhc2VNb2RhbFxyXG4gICAgICAgIGJvb2s9e2Jvb2t9XHJcbiAgICAgICAgaXNPcGVuPXtzaG93UHVyY2hhc2VNb2RhbH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93UHVyY2hhc2VNb2RhbChmYWxzZSl9XHJcbiAgICAgICAgb25QdXJjaGFzZUNvbXBsZXRlPXtoYW5kbGVQdXJjaGFzZUNvbXBsZXRlfVxyXG4gICAgICAvPiAqL31cclxuICAgIDwvQ2FyZD5cclxuICApXHJcbn1cclxuXHJcbiJdLCJuYW1lcyI6WyJMaW5rIiwidXNlU3RhdGUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRm9vdGVyIiwiQnV0dG9uIiwiU3RhciIsIlNob3BwaW5nQ2FydCIsIkJvb2tDYXJkIiwiaWQiLCJ0aXRsZSIsImF1dGhvciIsImNvdmVyVXJsIiwiY2F0ZWdvcnkiLCJyYXRpbmciLCJwcmljZSIsImlzUHVyY2hhc2VkIiwiaXNGcmVlIiwic2hvd1B1cmNoYXNlTW9kYWwiLCJzZXRTaG93UHVyY2hhc2VNb2RhbCIsImJvb2siLCJwYWdlcyIsImxhbmd1YWdlIiwic3VtbWFyeSIsImlzQm9va0ZyZWUiLCJoYW5kbGVQdXJjaGFzZUNsaWNrIiwiZSIsInByZXZlbnREZWZhdWx0Iiwic3RvcFByb3BhZ2F0aW9uIiwiaGFuZGxlUHVyY2hhc2VDb21wbGV0ZSIsImNvbnNvbGUiLCJsb2ciLCJjbGFzc05hbWUiLCJocmVmIiwiZGl2IiwiaW1nIiwic3JjIiwiYWx0IiwiaDMiLCJwIiwic3BhbiIsInRvRml4ZWQiLCJ2YXJpYW50Iiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/book-card.tsx\n"));

/***/ })

});