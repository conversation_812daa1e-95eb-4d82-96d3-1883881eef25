"use strict";exports.id=932,exports.ids=[932],exports.modules={49656:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(41680).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},33445:(e,t,r)=>{r.d(t,{rc:()=>ew,ZD:()=>eA,UC:()=>eR,VY:()=>eN,hJ:()=>eb,ZL:()=>ej,bL:()=>ex,hE:()=>eC,l9:()=>eD});var o=r(58009),n=r(6004),a=r(29952),i=r(31412),l=r(30096),s=r(13024),d=r(41675),u=r(82534),c=r(80707),p=r(98060),f=r(30830),g=r(19632),m=r(67783),y=r(72421),v=r(12705),h=r(45512),x="Dialog",[D,j]=(0,n.A)(x),[b,R]=D(x),w=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,u=o.useRef(null),c=o.useRef(null),[p,f]=(0,s.i)({prop:n,defaultProp:a??!1,onChange:i,caller:x});return(0,h.jsx)(b,{scope:t,triggerRef:u,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};w.displayName=x;var A="DialogTrigger",C=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=R(A,r),l=(0,a.s)(t,n.triggerRef);return(0,h.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":H(n.open),...o,ref:l,onClick:(0,i.m)(e.onClick,n.onOpenToggle)})});C.displayName=A;var N="DialogPortal",[I,O]=D(N,{forceMount:void 0}),F=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,i=R(N,t);return(0,h.jsx)(I,{scope:t,forceMount:r,children:o.Children.map(n,e=>(0,h.jsx)(p.C,{present:r||i.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};F.displayName=N;var E="DialogOverlay",_=o.forwardRef((e,t)=>{let r=O(E,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=R(E,e.__scopeDialog);return a.modal?(0,h.jsx)(p.C,{present:o||a.open,children:(0,h.jsx)(P,{...n,ref:t})}):null});_.displayName=E;var k=(0,v.TL)("DialogOverlay.RemoveScroll"),P=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=R(E,r);return(0,h.jsx)(m.A,{as:k,allowPinchZoom:!0,shards:[n.contentRef],children:(0,h.jsx)(f.sG.div,{"data-state":H(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),T="DialogContent",$=o.forwardRef((e,t)=>{let r=O(T,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=R(T,e.__scopeDialog);return(0,h.jsx)(p.C,{present:o||a.open,children:a.modal?(0,h.jsx)(M,{...n,ref:t}):(0,h.jsx)(B,{...n,ref:t})})});$.displayName=T;var M=o.forwardRef((e,t)=>{let r=R(T,e.__scopeDialog),n=o.useRef(null),l=(0,a.s)(t,r.contentRef,n);return o.useEffect(()=>{let e=n.current;if(e)return(0,y.Eq)(e)},[]),(0,h.jsx)(q,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=o.forwardRef((e,t)=>{let r=R(T,e.__scopeDialog),n=o.useRef(!1),a=o.useRef(!1);return(0,h.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),q=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,c=R(T,r),p=o.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,h.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(K,{titleId:c.titleId}),(0,h.jsx)(z,{contentRef:p,descriptionId:c.descriptionId})]})]})}),G="DialogTitle",S=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=R(G,r);return(0,h.jsx)(f.sG.h2,{id:n.titleId,...o,ref:t})});S.displayName=G;var V="DialogDescription",W=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=R(V,r);return(0,h.jsx)(f.sG.p,{id:n.descriptionId,...o,ref:t})});W.displayName=V;var Z="DialogClose",L=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=R(Z,r);return(0,h.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,i.m)(e.onClick,()=>n.onOpenChange(!1))})});function H(e){return e?"open":"closed"}L.displayName=Z;var U="DialogTitleWarning",[Y,J]=(0,n.q)(U,{contentName:T,titleName:G,docsSlug:"dialog"}),K=({titleId:e})=>{let t=J(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},z=({contentRef:e,descriptionId:t})=>{let r=J("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return o.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(n)},[n,e,t]),null},Q="AlertDialog",[X,ee]=(0,n.A)(Q,[j]),et=j(),er=e=>{let{__scopeAlertDialog:t,...r}=e,o=et(t);return(0,h.jsx)(w,{...o,...r,modal:!0})};er.displayName=Q;var eo=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=et(r);return(0,h.jsx)(C,{...n,...o,ref:t})});eo.displayName="AlertDialogTrigger";var en=e=>{let{__scopeAlertDialog:t,...r}=e,o=et(t);return(0,h.jsx)(F,{...o,...r})};en.displayName="AlertDialogPortal";var ea=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=et(r);return(0,h.jsx)(_,{...n,...o,ref:t})});ea.displayName="AlertDialogOverlay";var ei="AlertDialogContent",[el,es]=X(ei),ed=(0,v.Dc)("AlertDialogContent"),eu=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...l}=e,s=et(r),d=o.useRef(null),u=(0,a.s)(t,d),c=o.useRef(null);return(0,h.jsx)(Y,{contentName:ei,titleName:ec,docsSlug:"alert-dialog",children:(0,h.jsx)(el,{scope:r,cancelRef:c,children:(0,h.jsxs)($,{role:"alertdialog",...s,...l,ref:u,onOpenAutoFocus:(0,i.m)(l.onOpenAutoFocus,e=>{e.preventDefault(),c.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,h.jsx)(ed,{children:n}),(0,h.jsx)(eh,{contentRef:d})]})})})});eu.displayName=ei;var ec="AlertDialogTitle",ep=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=et(r);return(0,h.jsx)(S,{...n,...o,ref:t})});ep.displayName=ec;var ef="AlertDialogDescription",eg=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=et(r);return(0,h.jsx)(W,{...n,...o,ref:t})});eg.displayName=ef;var em=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=et(r);return(0,h.jsx)(L,{...n,...o,ref:t})});em.displayName="AlertDialogAction";var ey="AlertDialogCancel",ev=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,{cancelRef:n}=es(ey,r),i=et(r),l=(0,a.s)(t,n);return(0,h.jsx)(L,{...i,...o,ref:l})});ev.displayName=ey;var eh=({contentRef:e})=>{let t=`\`${ei}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${ei}\` by passing a \`${ef}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${ei}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return o.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},ex=er,eD=eo,ej=en,eb=ea,eR=eu,ew=em,eA=ev,eC=ep,eN=eg}};