(()=>{var e={};e.id=116,e.ids=[116],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},25083:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(70260),a=s(28203),i=s(25155),n=s.n(i),l=s(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["admin",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,20114)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\admin\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,54899)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,10375)),"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\admin\\login\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/login/page",pathname:"/admin/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83422:(e,t,s)=>{Promise.resolve().then(s.bind(s,20114))},51878:(e,t,s)=>{Promise.resolve().then(s.bind(s,16262))},57136:(e,t,s)=>{Promise.resolve().then(s.bind(s,5986))},91984:(e,t,s)=>{Promise.resolve().then(s.bind(s,48790))},16262:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(45512),a=s(58009),i=s(79334),n=s(87021),l=s(97643),o=s(25409),d=s(53261),c=s(4269),m=s(72734),p=s(70801);function h(){let[e,t]=(0,a.useState)(""),[s,h]=(0,a.useState)(""),[x,u]=(0,a.useState)(!1),f=(0,i.useRouter)(),{toast:b}=(0,p.dj)(),v=async t=>{if(t.preventDefault(),!e||!s){b({title:"Error",description:"Please enter both email and password",variant:"destructive"});return}try{u(!0);let t=await fetch("/api/admin/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:s})});if(!t.ok)throw Error("Login failed");let r=await t.json();localStorage.setItem("adminUser",JSON.stringify(r)),b({title:"Success",description:"You have successfully logged in as admin"}),f.push("/admin")}catch(e){console.error("Admin login error:",e),b({title:"Login Failed",description:"Invalid email or password. Please try again.",variant:"destructive"})}finally{u(!1)}};return(0,r.jsxs)("div",{className:"container flex flex-col items-center justify-center min-h-screen py-12",children:[(0,r.jsxs)("div",{className:"flex items-center mb-8",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 mr-2 text-primary"}),(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"TelkTibeb Admin"})]}),(0,r.jsxs)(l.Zp,{className:"w-full max-w-md",children:[(0,r.jsxs)(l.aR,{className:"space-y-1",children:[(0,r.jsx)(l.ZB,{className:"text-2xl text-center",children:"Admin Login"}),(0,r.jsx)(l.BT,{className:"text-center",children:"Enter your credentials to access the admin dashboard"})]}),(0,r.jsxs)("form",{onSubmit:v,children:[(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.Label,{htmlFor:"email",children:"Email"}),(0,r.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>t(e.target.value),disabled:x})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.Label,{htmlFor:"password",children:"Password"}),(0,r.jsx)(o.p,{id:"password",type:"password",value:s,onChange:e=>h(e.target.value),disabled:x})]})]}),(0,r.jsx)(l.wL,{children:(0,r.jsx)(n.$,{className:"w-full",type:"submit",disabled:x,children:x?"Logging in...":"Log in"})})]}),(0,r.jsx)("div",{className:"p-4 bg-muted/50 rounded-b-lg",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"This area is restricted to authorized administrators only."})]})})]})]})}},48790:(e,t,s)=>{"use strict";s.d(t,{AdminSidebar:()=>A});var r=s(45512),a=s(58009),i=s(28531),n=s.n(i),l=s(79334),o=s(41680);let d=(0,o.A)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var c=s(4269),m=s(82901),p=s(64977),h=s(91124);let x=(0,o.A)("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]);var u=s(94520);let f=(0,o.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var b=s(87021),v=s(30830),y="horizontal",g=["horizontal","vertical"],j=a.forwardRef((e,t)=>{let{decorative:s,orientation:a=y,...i}=e,n=g.includes(a)?a:y;return(0,r.jsx)(v.sG.div,{"data-orientation":n,...s?{role:"none"}:{"aria-orientation":"vertical"===n?n:void 0,role:"separator"},...i,ref:t})});j.displayName="Separator";var w=s(59462);let k=a.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...a},i)=>(0,r.jsx)(j,{ref:i,decorative:s,orientation:t,className:(0,w.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));k.displayName=j.displayName;var N=s(70801);function A(){let e=(0,l.usePathname)(),t=(0,l.useRouter)(),{toast:s}=(0,N.dj)(),[i,o]=(0,a.useState)(!1),v=()=>{localStorage.removeItem("adminUser"),s({title:"Logged out",description:"You have been logged out successfully"}),t.push("/admin/login")},y=[{title:"Dashboard",href:"/admin",icon:d},{title:"Books",href:"/admin/books",icon:c.A},{title:"Business Plans",href:"/admin/business-plans",icon:m.A},{title:"Users",href:"/admin/users",icon:p.A},{title:"Orders",href:"/admin/orders",icon:h.A},{title:"Analytics",href:"/admin/analytics",icon:x},{title:"Settings",href:"/admin/settings",icon:u.A}];return(0,r.jsxs)("div",{className:(0,w.cn)("h-screen bg-muted/20 border-r flex flex-col transition-all duration-300 ease-in-out",i?"w-16":"w-64"),children:[(0,r.jsxs)("div",{className:"flex items-center h-16 px-4 border-b",children:[!i&&(0,r.jsxs)(n(),{href:"/admin",className:"flex items-center gap-2 font-bold text-lg",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-primary"}),(0,r.jsx)("span",{children:"TelkTibeb Admin"})]}),i&&(0,r.jsx)(n(),{href:"/admin",className:"mx-auto",children:(0,r.jsx)(c.A,{className:"h-7 w-7 text-primary"})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-auto py-4",children:(0,r.jsx)("nav",{className:"px-2 space-y-1",children:y.map(t=>(0,r.jsxs)(n(),{href:t.href,className:(0,w.cn)("flex items-center gap-3 px-3 py-2 rounded-md transition-colors",e===t.href||"/admin"!==t.href&&e?.startsWith(t.href)?"bg-primary/10 text-primary":"text-muted-foreground hover:bg-muted hover:text-foreground",i&&"justify-center px-0"),children:[(0,r.jsx)(t.icon,{className:(0,w.cn)("h-5 w-5",i&&"h-6 w-6")}),!i&&(0,r.jsx)("span",{children:t.title})]},t.href))})}),(0,r.jsx)("div",{className:"p-4 border-t",children:i?(0,r.jsxs)("div",{className:"flex flex-col gap-4 items-center",children:[(0,r.jsxs)(b.$,{variant:"outline",size:"icon",onClick:()=>o(!1),children:[(0,r.jsx)("span",{className:"sr-only",children:"Expand"}),(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4",children:[(0,r.jsx)("rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"}),(0,r.jsx)("path",{d:"M15 3v18"}),(0,r.jsx)("path",{d:"m8 9 3 3-3 3"})]})]}),(0,r.jsx)(k,{}),(0,r.jsx)(b.$,{variant:"outline",size:"icon",className:"text-destructive hover:text-destructive",onClick:v,children:(0,r.jsx)(f,{className:"h-4 w-4"})})]}):(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)(b.$,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>o(!0),children:[(0,r.jsx)("span",{className:"sr-only",children:"Collapse"}),(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4 mr-2",children:[(0,r.jsx)("rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"}),(0,r.jsx)("path",{d:"M9 3v18"}),(0,r.jsx)("path",{d:"m16 15-3-3 3-3"})]}),"Collapse"]}),(0,r.jsx)(k,{}),(0,r.jsxs)(b.$,{variant:"outline",className:"w-full justify-start text-destructive hover:text-destructive",onClick:v,children:[(0,r.jsx)(f,{className:"h-4 w-4 mr-2"}),"Logout"]})]})})]})}},97643:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>m});var r=s(45512),a=s(58009),i=s(59462);let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));m.displayName="CardFooter"},53261:(e,t,s)=>{"use strict";s.d(t,{Label:()=>c});var r=s(45512),a=s(58009),i=s(30830),n=a.forwardRef((e,t)=>(0,r.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=s(21643),o=s(59462);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n,{ref:s,className:(0,o.cn)(d(),e),...t}));c.displayName=n.displayName},82901:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},72734:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},91124:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},64977:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(41680).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},54899:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,metadata:()=>i});var r=s(62740),a=s(5986);let i={title:"Admin Dashboard - TelkTibeb",description:"Manage books, business plans, and users"};function n({children:e}){return(0,r.jsxs)("div",{className:"flex min-h-screen",children:[(0,r.jsx)(a.AdminSidebar,{}),(0,r.jsx)("div",{className:"flex-1 overflow-auto",children:(0,r.jsx)("main",{className:"p-6",children:e})})]})}},20114:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\app\\\\admin\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\app\\admin\\login\\page.tsx","default")},5986:(e,t,s)=>{"use strict";s.d(t,{AdminSidebar:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call AdminSidebar() from the server but AdminSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projects\\Astewai\\TilkTebeb\\tilktbeb (1)\\components\\admin\\admin-sidebar.tsx","AdminSidebar")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[884,999],()=>s(25083));module.exports=r})();