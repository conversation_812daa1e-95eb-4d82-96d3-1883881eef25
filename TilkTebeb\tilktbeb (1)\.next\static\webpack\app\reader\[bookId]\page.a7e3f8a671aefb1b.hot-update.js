"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reader/[bookId]/page",{

/***/ "(app-pages-browser)/./lib/mock-data.ts":
/*!**************************!*\
  !*** ./lib/mock-data.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockBookPreviews: () => (/* binding */ mockBookPreviews),\n/* harmony export */   mockBooks: () => (/* binding */ mockBooks),\n/* harmony export */   mockBusinessPlanPreviews: () => (/* binding */ mockBusinessPlanPreviews),\n/* harmony export */   mockBusinessPlans: () => (/* binding */ mockBusinessPlans)\n/* harmony export */ });\n// Mock data for books - this will be replaced by Django API calls\nconst mockBooks = [\n    {\n        id: \"the-psychology-of-money\",\n        title: \"The Psychology Of Money\",\n        author: \"Morgan Housel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Finance\",\n        rating: 4.4,\n        pages: 242,\n        language: \"English\",\n        price: 12.99,\n        summary: \"\\n      <p>The Psychology of Money explores how money moves around in an economy and how people behave with it. The author, Morgan Housel, provides timeless lessons on wealth, greed, and happiness.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. Financial success is not a hard science</strong></p>\\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\\n      \\n      <p><strong>2. No one is crazy with money</strong></p>\\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\\n      \\n      <p><strong>3. Luck and risk are siblings</strong></p>\\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\\n      \\n      <p><strong>4. Never enough</strong></p>\\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Financial success is not a hard science</strong></p>\\n      <p>It's a soft skill where how you behave is more important than what you know. Financial outcomes are driven by luck, independent of intelligence and effort.</p>\\n      \\n      <p><strong>2. No one is crazy with money</strong></p>\\n      <p>People make financial decisions based on their unique experiences, their own personal history, unique worldview, ego, pride, marketing, and odd incentives.</p>\\n      \\n      <p><strong>3. Luck and risk are siblings</strong></p>\\n      <p>They are both the reality that every outcome in life is guided by forces other than individual effort. They both happen because the world is too complex to allow 100% of your actions to dictate 100% of your outcomes.</p>\\n      \\n      <p><strong>4. Never enough</strong></p>\\n      <p>When rich people do crazy things, it's often a case of trying to feel valued after already having more money than they know what to do with.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Personal Finance:</strong></p>\\n      <ul>\\n        <li>Save money without a specific goal in mind</li>\\n        <li>Gain control over your time</li>\\n        <li>Be reasonable rather than rational</li>\\n        <li>Aim for enough, not for maximum</li>\\n      </ul>\\n      \\n      <p><strong>For Investors:</strong></p>\\n      <ul>\\n        <li>Understand the role of luck and risk</li>\\n        <li>Know that getting wealthy and staying wealthy are different skills</li>\\n        <li>Long tails drive everything - a small number of events can account for the majority of outcomes</li>\\n        <li>Use room for error when investing - prepare for a range of outcomes</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"atomic-habits\",\n        title: \"Atomic Habits\",\n        author: \"James Clear\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Productivity\",\n        rating: 4.9,\n        pages: 320,\n        language: \"English\",\n        price: 13.99,\n        summary: \"\\n      <p>Atomic Habits offers a proven framework for improving every day. James Clear reveals practical strategies that will teach you exactly how to form good habits, break bad ones, and master the tiny behaviors that lead to remarkable results.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\\n      \\n      <p><strong>2. Focus on systems instead of goals</strong></p>\\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\\n      \\n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\\n      \\n      <p><strong>4. Identity-based habits</strong></p>\\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Habits are the compound interest of self-improvement</strong></p>\\n      <p>Small changes often appear to make no difference until you cross a critical threshold. The effects of your habits multiply as you repeat them.</p>\\n      \\n      <p><strong>2. Focus on systems instead of goals</strong></p>\\n      <p>Goals are about the results you want to achieve. Systems are about the processes that lead to those results. Focus on the system, not the goal.</p>\\n      \\n      <p><strong>3. The Four Laws of Behavior Change</strong></p>\\n      <p>Make it obvious, make it attractive, make it easy, and make it satisfying. These are the fundamental principles behind habit formation.</p>\\n      \\n      <p><strong>4. Identity-based habits</strong></p>\\n      <p>The most effective way to change your habits is to focus not on what you want to achieve, but on who you wish to become.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Personal Development:</strong></p>\\n      <ul>\\n        <li>Start with an incredibly small habit</li>\\n        <li>Increase your habit in very small ways</li>\\n        <li>Break habits into chunks</li>\\n        <li>When you slip, get back on track quickly</li>\\n      </ul>\\n      \\n      <p><strong>For Business:</strong></p>\\n      <ul>\\n        <li>Create an environment where doing the right thing is as easy as possible</li>\\n        <li>Make good habits obvious in your environment</li>\\n        <li>Reduce friction for good habits</li>\\n        <li>Increase friction for bad habits</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"sapiens\",\n        title: \"Sapiens\",\n        author: \"Yuval Noah Harari\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"History\",\n        rating: 4.7,\n        pages: 464,\n        language: \"English\",\n        price: 14.99,\n        summary: '\\n      <p>Sapiens: A Brief History of Humankind is a book by Yuval Noah Harari that explores the history and impact of Homo sapiens on the world. It traces the evolution of our species from the emergence of Homo sapiens in Africa to our current status as the dominant force on Earth.</p>\\n      \\n      <h3>Key Insights:</h3>\\n      \\n      <p><strong>1. The Cognitive Revolution</strong></p>\\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\\n      \\n      <p><strong>2. The Agricultural Revolution</strong></p>\\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history\\'s biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\\n      \\n      <p><strong>3. The Unification of Humankind</strong></p>\\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\\n      \\n      <p><strong>4. The Scientific Revolution</strong></p>\\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\\n    ',\n        keyInsights: '\\n      <p><strong>1. The Cognitive Revolution</strong></p>\\n      <p>Around 70,000 years ago, Homo sapiens developed unique cognitive abilities, particularly the capacity for fiction and imagination, which allowed for unprecedented cooperation among large groups.</p>\\n      \\n      <p><strong>2. The Agricultural Revolution</strong></p>\\n      <p>Beginning about 12,000 years ago, humans transitioned from hunting and gathering to farming, which Harari describes as \"history\\'s biggest fraud\" because it led to harder work, less leisure, and poorer health for most people.</p>\\n      \\n      <p><strong>3. The Unification of Humankind</strong></p>\\n      <p>Over time, humans have created increasingly larger networks of cooperation through shared myths, including money, empires, and religions.</p>\\n      \\n      <p><strong>4. The Scientific Revolution</strong></p>\\n      <p>The last 500 years have seen an explosion of human power through the willingness to admit ignorance and the development of the scientific method.</p>\\n    ',\n        applications: '\\n      <p><strong>For Understanding Society:</strong></p>\\n      <ul>\\n        <li>Recognize how shared myths and stories shape our world</li>\\n        <li>Understand the historical context of current social structures</li>\\n        <li>Question whether \"progress\" always means improvement</li>\\n        <li>Consider the ethical implications of technological advancement</li>\\n      </ul>\\n      \\n      <p><strong>For Business and Leadership:</strong></p>\\n      <ul>\\n        <li>Appreciate how shared narratives create cohesion in organizations</li>\\n        <li>Understand how money and corporations are social constructs that depend on trust</li>\\n        <li>Consider the long-term implications of short-term decisions</li>\\n        <li>Recognize patterns of human behavior that persist across time</li>\\n      </ul>\\n    ',\n        isPremium: true\n    },\n    {\n        id: \"zero-to-one\",\n        title: \"Zero to One\",\n        author: \"Peter Thiel\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Entrepreneurship\",\n        rating: 4.8,\n        pages: 224,\n        language: \"English\",\n        price: 12.99,\n        summary: \"\\n      <p>Zero to One presents at once an optimistic view of the future of progress in America and a new way of thinking about innovation: it starts by learning to ask the questions that lead you to find value in unexpected places.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\\n\\n      <p><strong>2. Monopolies vs. Competition</strong></p>\\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\\n\\n      <p><strong>3. The Power Law</strong></p>\\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\\n\\n      <p><strong>4. Secrets</strong></p>\\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Vertical vs. Horizontal Progress</strong></p>\\n      <p>Horizontal progress means copying things that work—going from 1 to n. Vertical progress means doing new things—going from 0 to 1. The focus should be on creating something new rather than copying existing models.</p>\\n\\n      <p><strong>2. Monopolies vs. Competition</strong></p>\\n      <p>Competition is for losers. Under perfect competition, no company makes economic profit. The goal should be to create a monopoly through innovation.</p>\\n\\n      <p><strong>3. The Power Law</strong></p>\\n      <p>A small number of companies radically outperform all others. This principle applies to venture capital investments, where a single investment may return more than all others combined.</p>\\n\\n      <p><strong>4. Secrets</strong></p>\\n      <p>Every great business is built around a secret that's hidden from the outside. Great companies find value in unexpected places by thinking about business from first principles.</p>\\n    \",\n        applications: '\\n      <p><strong>For Entrepreneurs:</strong></p>\\n      <ul>\\n        <li>Focus on creating something new rather than improving existing products</li>\\n        <li>Aim to create a monopoly through unique technology, network effects, economies of scale, and branding</li>\\n        <li>Start small and monopolize a niche market before expanding</li>\\n        <li>Build a great team with a strong, unified vision</li>\\n      </ul>\\n\\n      <p><strong>For Investors:</strong></p>\\n      <ul>\\n        <li>Understand that returns follow a power law—a few investments will outperform all others</li>\\n        <li>Look for companies with proprietary technology, network effects, economies of scale, and strong branding</li>\\n        <li>Evaluate the founding team\\'s dynamics and vision</li>\\n        <li>Consider whether the company has discovered a unique \"secret\" about the market</li>\\n      </ul>\\n    ',\n        isPremium: true\n    },\n    {\n        id: \"good-to-great\",\n        title: \"Good to Great\",\n        author: \"Jim Collins\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Leadership\",\n        rating: 4.7,\n        pages: 320,\n        language: \"English\",\n        price: 11.99,\n        summary: \"\\n      <p>Good to Great presents the findings of a five-year study by Jim Collins and his research team. The team identified a set of companies that made the leap from good results to great results and sustained those results for at least fifteen years.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Level 5 Leadership</strong></p>\\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\\n\\n      <p><strong>2. First Who, Then What</strong></p>\\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\\n\\n      <p><strong>3. Confront the Brutal Facts</strong></p>\\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\\n\\n      <p><strong>4. The Hedgehog Concept</strong></p>\\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Level 5 Leadership</strong></p>\\n      <p>Leaders who have a unique blend of personal humility and professional will. They are ambitious for the company, not themselves.</p>\\n\\n      <p><strong>2. First Who, Then What</strong></p>\\n      <p>Get the right people on the bus, the wrong people off the bus, and the right people in the right seats—then figure out where to drive it.</p>\\n\\n      <p><strong>3. Confront the Brutal Facts</strong></p>\\n      <p>Create a culture where people have the opportunity to be heard and where the truth is heard. Maintain unwavering faith that you can and will prevail, regardless of difficulties.</p>\\n\\n      <p><strong>4. The Hedgehog Concept</strong></p>\\n      <p>Focus on the intersection of three circles: what you can be the best in the world at, what drives your economic engine, and what you are deeply passionate about.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Business Leaders:</strong></p>\\n      <ul>\\n        <li>Develop Level 5 Leadership qualities: ambition for the company over self</li>\\n        <li>Focus on getting the right team in place before determining strategy</li>\\n        <li>Create a culture of disciplined people, thought, and action</li>\\n        <li>Apply the Hedgehog Concept to focus resources and efforts</li>\\n      </ul>\\n\\n      <p><strong>For Organizations:</strong></p>\\n      <ul>\\n        <li>Use technology as an accelerator, not a creator of momentum</li>\\n        <li>Build momentum gradually until breakthrough occurs (the flywheel effect)</li>\\n        <li>Maintain discipline to stick with what you can be best at</li>\\n        <li>Confront reality while maintaining faith in ultimate success</li>\\n      </ul>\\n    \",\n        isPremium: true\n    },\n    {\n        id: \"the-lean-startup\",\n        title: \"The Lean Startup\",\n        author: \"Eric Ries\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Startup\",\n        rating: 4.6,\n        pages: 336,\n        language: \"English\",\n        price: 10.99,\n        summary: \"\\n      <p>The Lean Startup introduces a methodology for developing businesses and products that aims to shorten product development cycles and rapidly discover if a proposed business model is viable.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Build-Measure-Learn</strong></p>\\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\\n\\n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\\n\\n      <p><strong>3. Validated Learning</strong></p>\\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\\n\\n      <p><strong>4. Innovation Accounting</strong></p>\\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\\n    \",\n        keyInsights: \"\\n      <p><strong>1. Build-Measure-Learn</strong></p>\\n      <p>The fundamental activity of a startup is to turn ideas into products, measure how customers respond, and then learn whether to pivot or persevere.</p>\\n\\n      <p><strong>2. Minimum Viable Product (MVP)</strong></p>\\n      <p>The version of a new product that allows a team to collect the maximum amount of validated learning about customers with the least effort.</p>\\n\\n      <p><strong>3. Validated Learning</strong></p>\\n      <p>The process of demonstrating empirically that a team has discovered valuable truths about a startup's present and future business prospects.</p>\\n\\n      <p><strong>4. Innovation Accounting</strong></p>\\n      <p>A quantitative approach that allows startups to prove objectively that they are learning how to grow a sustainable business.</p>\\n    \",\n        applications: \"\\n      <p><strong>For Entrepreneurs:</strong></p>\\n      <ul>\\n        <li>Start with a minimum viable product to test assumptions quickly</li>\\n        <li>Use actionable metrics that demonstrate clear cause and effect</li>\\n        <li>Practice continuous deployment and small batch sizes</li>\\n        <li>Be willing to pivot when necessary based on validated learning</li>\\n      </ul>\\n\\n      <p><strong>For Established Companies:</strong></p>\\n      <ul>\\n        <li>Create innovation teams with appropriate structures and metrics</li>\\n        <li>Allocate resources using innovation accounting</li>\\n        <li>Develop internal entrepreneurship through dedicated teams</li>\\n        <li>Apply lean principles to accelerate product development cycles</li>\\n      </ul>\\n    \",\n        isPremium: true\n    },\n    // Free books\n    {\n        id: \"public-domain-classic-1\",\n        title: \"The Art of War\",\n        author: \"Sun Tzu\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Strategy\",\n        rating: 4.8,\n        pages: 96,\n        language: \"English\",\n        price: 0,\n        summary: \"\\n      <p>The Art of War is an ancient Chinese military treatise dating from the Late Spring and Autumn Period. The work, which is attributed to the ancient Chinese military strategist Sun Tzu, is composed of 13 chapters.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Know Yourself and Your Enemy</strong></p>\\n      <p>If you know the enemy and know yourself, you need not fear the result of a hundred battles.</p>\\n\\n      <p><strong>2. Win Without Fighting</strong></p>\\n      <p>The supreme excellence is to subdue the enemy without fighting.</p>\\n\\n      <p><strong>3. Speed and Timing</strong></p>\\n      <p>Rapidity is the essence of war: take advantage of the enemy's unreadiness, make your way by unexpected routes, and attack unguarded spots.</p>\\n\\n      <p><strong>4. Adaptability</strong></p>\\n      <p>Water shapes its course according to the nature of the ground; the soldier works out his victory in relation to the foe he is facing.</p>\\n    \",\n        isPremium: false,\n        isFree: true\n    },\n    {\n        id: \"public-domain-classic-2\",\n        title: \"Think and Grow Rich\",\n        author: \"Napoleon Hill\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Self-Help\",\n        rating: 4.7,\n        pages: 320,\n        language: \"English\",\n        price: 0,\n        summary: \"\\n      <p>Think and Grow Rich is a personal development and self-help book by Napoleon Hill. The book was inspired by a suggestion from Scottish-American businessman Andrew Carnegie.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Desire</strong></p>\\n      <p>The starting point of all achievement is desire. Keep this constantly in mind. Weak desire brings weak results.</p>\\n\\n      <p><strong>2. Faith</strong></p>\\n      <p>Faith is the head chemist of the mind. When faith is blended with the vibration of thought, the subconscious mind instantly picks up the vibration.</p>\\n\\n      <p><strong>3. Persistence</strong></p>\\n      <p>Persistence is to the character of man as carbon is to steel.</p>\\n\\n      <p><strong>4. The Master Mind</strong></p>\\n      <p>The coordination of knowledge and effort of two or more people, who work toward a definite purpose, in the spirit of harmony.</p>\\n    \",\n        isPremium: false,\n        isFree: true\n    },\n    {\n        id: \"free-business-guide\",\n        title: \"Starting Your First Business\",\n        author: \"Astewai Team\",\n        coverUrl: \"/placeholder.svg?height=240&width=180\",\n        category: \"Entrepreneurship\",\n        rating: 4.5,\n        pages: 150,\n        language: \"English\",\n        price: 0,\n        summary: \"\\n      <p>A comprehensive guide for first-time entrepreneurs, covering everything from idea validation to launching your business.</p>\\n\\n      <h3>Key Insights:</h3>\\n\\n      <p><strong>1. Idea Validation</strong></p>\\n      <p>Before investing time and money, validate your business idea with potential customers.</p>\\n\\n      <p><strong>2. Market Research</strong></p>\\n      <p>Understanding your target market is crucial for business success.</p>\\n\\n      <p><strong>3. Financial Planning</strong></p>\\n      <p>Create realistic financial projections and understand your funding needs.</p>\\n\\n      <p><strong>4. Legal Structure</strong></p>\\n      <p>Choose the right business structure for your specific situation.</p>\\n    \",\n        isPremium: false,\n        isFree: true\n    }\n];\n// Convert to BookPreview format for listing pages\nconst mockBookPreviews = mockBooks.map((book)=>({\n        id: book.id,\n        title: book.title,\n        author: book.author,\n        coverUrl: book.coverUrl,\n        category: book.category,\n        rating: book.rating\n    }));\nconst mockBusinessPlans = [\n    {\n        id: \"small-1\",\n        title: \"Local Coffee Shop\",\n        category: \"Food & Beverage\",\n        size: \"small\",\n        description: \"A comprehensive business plan for starting and operating a successful local coffee shop.\",\n        overview: \"\\n      <h3>Executive Summary</h3>\\n      <p>This business plan outlines the establishment of a cozy, community-focused coffee shop that serves premium coffee and light food items. The shop will be located in a high-traffic area with significant foot traffic and limited direct competition.</p>\\n\\n      <h3>Business Concept</h3>\\n      <ul>\\n        <li>Premium coffee and espresso drinks</li>\\n        <li>Fresh pastries and light meals</li>\\n        <li>Comfortable seating and free Wi-Fi</li>\\n        <li>Focus on sustainable practices</li>\\n      </ul>\\n\\n      <h3>Target Market</h3>\\n      <ul>\\n        <li>Young professionals (25-40)</li>\\n        <li>College students</li>\\n        <li>Remote workers</li>\\n        <li>Local residents</li>\\n      </ul>\\n    \",\n        marketAnalysis: \"\\n      <h3>Market Overview</h3>\\n      <p>The coffee shop industry continues to grow, with increasing demand for premium coffee experiences. Key market trends include:</p>\\n      <ul>\\n        <li>Growing preference for specialty coffee</li>\\n        <li>Increased focus on sustainability</li>\\n        <li>Rising demand for plant-based options</li>\\n        <li>Need for comfortable workspaces</li>\\n      </ul>\\n\\n      <h3>Competitive Analysis</h3>\\n      <p>Local competition includes:</p>\\n      <ul>\\n        <li>Chain coffee shops (2 within 1km)</li>\\n        <li>Independent cafes (1 within 1km)</li>\\n        <li>Restaurants serving coffee</li>\\n      </ul>\\n\\n      <h3>Competitive Advantage</h3>\\n      <ul>\\n        <li>Premium quality coffee</li>\\n        <li>Comfortable atmosphere</li>\\n        <li>Excellent customer service</li>\\n        <li>Strategic location</li>\\n      </ul>\\n    \",\n        financials: \"\\n      <h3>Startup Costs</h3>\\n      <ul>\\n        <li>Lease deposit and improvements: $25,000</li>\\n        <li>Equipment: $35,000</li>\\n        <li>Initial inventory: $5,000</li>\\n        <li>Licenses and permits: $2,000</li>\\n        <li>Working capital: $20,000</li>\\n      </ul>\\n\\n      <h3>Financial Projections</h3>\\n      <p>Year 1:</p>\\n      <ul>\\n        <li>Revenue: $300,000</li>\\n        <li>Expenses: $270,000</li>\\n        <li>Net profit: $30,000</li>\\n      </ul>\\n\\n      <p>Year 2:</p>\\n      <ul>\\n        <li>Revenue: $400,000</li>\\n        <li>Expenses: $340,000</li>\\n        <li>Net profit: $60,000</li>\\n      </ul>\\n    \",\n        implementation: \"\\n      <h3>Timeline</h3>\\n      <ul>\\n        <li>Month 1-2: Location selection and lease signing</li>\\n        <li>Month 2-3: Design and permits</li>\\n        <li>Month 3-4: Construction and equipment installation</li>\\n        <li>Month 4: Staff hiring and training</li>\\n        <li>Month 5: Soft opening and marketing</li>\\n        <li>Month 6: Grand opening</li>\\n      </ul>\\n\\n      <h3>Marketing Strategy</h3>\\n      <ul>\\n        <li>Social media presence</li>\\n        <li>Local partnerships</li>\\n        <li>Loyalty program</li>\\n        <li>Community events</li>\\n      </ul>\\n\\n      <h3>Risk Mitigation</h3>\\n      <ul>\\n        <li>Comprehensive insurance coverage</li>\\n        <li>Diverse supplier relationships</li>\\n        <li>Staff training programs</li>\\n        <li>Cash flow management</li>\\n      </ul>\\n    \",\n        isPremium: false\n    },\n    {\n        id: \"small-2\",\n        title: \"Freelance Web Development\",\n        category: \"Technology\",\n        size: \"small\",\n        description: \"A detailed business plan for starting and growing a freelance web development business.\",\n        overview: \"\\n      <h3>Executive Summary</h3>\\n      <p>This business plan outlines the establishment of a freelance web development business focused on creating custom websites and web applications for small to medium-sized businesses. The business will operate remotely with minimal overhead costs.</p>\\n\\n      <h3>Business Concept</h3>\\n      <ul>\\n        <li>Custom website development</li>\\n        <li>Web application development</li>\\n        <li>Website maintenance and support</li>\\n        <li>SEO and digital marketing services</li>\\n      </ul>\\n\\n      <h3>Target Market</h3>\\n      <ul>\\n        <li>Small businesses needing online presence</li>\\n        <li>Medium-sized companies requiring web applications</li>\\n        <li>Startups with limited budgets</li>\\n        <li>Non-profit organizations</li>\\n      </ul>\\n    \",\n        marketAnalysis: \"\\n      <h3>Market Overview</h3>\\n      <p>The web development industry continues to grow as businesses increasingly recognize the importance of online presence. Key market trends include:</p>\\n      <ul>\\n        <li>Increasing demand for mobile-responsive websites</li>\\n        <li>Growing need for e-commerce functionality</li>\\n        <li>Rising importance of user experience (UX) design</li>\\n        <li>Shift toward progressive web applications</li>\\n      </ul>\\n\\n      <h3>Competitive Analysis</h3>\\n      <p>Competition includes:</p>\\n      <ul>\\n        <li>Other freelance developers</li>\\n        <li>Web development agencies</li>\\n        <li>DIY website builders (Wix, Squarespace)</li>\\n      </ul>\\n\\n      <h3>Competitive Advantage</h3>\\n      <ul>\\n        <li>Personalized service and direct client communication</li>\\n        <li>Lower overhead costs than agencies</li>\\n        <li>Specialized expertise in modern frameworks</li>\\n        <li>Flexible pricing models</li>\\n      </ul>\\n    \",\n        financials: \"\\n      <h3>Startup Costs</h3>\\n      <ul>\\n        <li>Computer equipment: $3,000</li>\\n        <li>Software subscriptions: $1,200/year</li>\\n        <li>Website and hosting: $500</li>\\n        <li>Business registration: $300</li>\\n        <li>Initial marketing: $1,000</li>\\n      </ul>\\n\\n      <h3>Financial Projections</h3>\\n      <p>Year 1:</p>\\n      <ul>\\n        <li>Revenue: $60,000</li>\\n        <li>Expenses: $15,000</li>\\n        <li>Net profit: $45,000</li>\\n      </ul>\\n\\n      <p>Year 2:</p>\\n      <ul>\\n        <li>Revenue: $90,000</li>\\n        <li>Expenses: $20,000</li>\\n        <li>Net profit: $70,000</li>\\n      </ul>\\n    \",\n        implementation: \"\\n      <h3>Timeline</h3>\\n      <ul>\\n        <li>Month 1: Business registration and website setup</li>\\n        <li>Month 2: Portfolio development</li>\\n        <li>Month 3: Initial marketing and networking</li>\\n        <li>Month 4-6: Secure first clients and build reputation</li>\\n      </ul>\\n\\n      <h3>Marketing Strategy</h3>\\n      <ul>\\n        <li>Portfolio website showcasing work</li>\\n        <li>Social media presence on LinkedIn and Twitter</li>\\n        <li>Content marketing through blog posts</li>\\n        <li>Networking at local business events</li>\\n        <li>Referral program for existing clients</li>\\n      </ul>\\n\\n      <h3>Risk Mitigation</h3>\\n      <ul>\\n        <li>Diversify client base to avoid dependency</li>\\n        <li>Maintain emergency fund for slow periods</li>\\n        <li>Continuous skill development</li>\\n        <li>Clear contracts and scope definitions</li>\\n      </ul>\\n    \",\n        isPremium: false\n    }\n];\nconst mockBusinessPlanPreviews = mockBusinessPlans.map((plan)=>({\n        id: plan.id,\n        title: plan.title,\n        category: plan.category,\n        size: plan.size,\n        description: plan.description,\n        isPremium: plan.isPremium\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9tb2NrLWRhdGEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVBLGtFQUFrRTtBQUMzRCxNQUFNQSxZQUFvQjtJQUMvQjtRQUNFQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxTQUFVO1FBaUJWQyxhQUFjO1FBYWRDLGNBQWU7UUFpQmZDLFdBQVc7SUFDYjtJQUNBO1FBQ0VaLElBQUk7UUFDSkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFNBQVU7UUFpQlZDLGFBQWM7UUFhZEMsY0FBZTtRQWlCZkMsV0FBVztJQUNiO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsU0FBVTtRQWlCVkMsYUFBYztRQWFkQyxjQUFlO1FBaUJmQyxXQUFXO0lBQ2I7SUFDQTtRQUNFWixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxTQUFVO1FBaUJWQyxhQUFjO1FBYWRDLGNBQWU7UUFpQmZDLFdBQVc7SUFDYjtJQUNBO1FBQ0VaLElBQUk7UUFDSkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFNBQVU7UUFpQlZDLGFBQWM7UUFhZEMsY0FBZTtRQWlCZkMsV0FBVztJQUNiO0lBQ0E7UUFDRVosSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsU0FBVTtRQWlCVkMsYUFBYztRQWFkQyxjQUFlO1FBaUJmQyxXQUFXO0lBQ2I7SUFDQSxhQUFhO0lBQ2I7UUFDRVosSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsU0FBVTtRQWlCVkcsV0FBVztRQUNYQyxRQUFRO0lBQ1Y7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxTQUFVO1FBaUJWRyxXQUFXO1FBQ1hDLFFBQVE7SUFDVjtJQUNBO1FBQ0ViLElBQUk7UUFDSkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFNBQVU7UUFpQlZHLFdBQVc7UUFDWEMsUUFBUTtJQUNWO0NBQ0Q7QUFFRCxrREFBa0Q7QUFDM0MsTUFBTUMsbUJBQWtDZixVQUFVZ0IsR0FBRyxDQUFDQyxDQUFBQSxPQUFTO1FBQ3BFaEIsSUFBSWdCLEtBQUtoQixFQUFFO1FBQ1hDLE9BQU9lLEtBQUtmLEtBQUs7UUFDakJDLFFBQVFjLEtBQUtkLE1BQU07UUFDbkJDLFVBQVVhLEtBQUtiLFFBQVE7UUFDdkJDLFVBQVVZLEtBQUtaLFFBQVE7UUFDdkJDLFFBQVFXLEtBQUtYLE1BQU07SUFDckIsSUFBRztBQXlCSSxNQUFNWSxvQkFBb0M7SUFDL0M7UUFDRWpCLElBQUk7UUFDSkMsT0FBTztRQUNQRyxVQUFVO1FBQ1ZjLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxVQUFXO1FBb0JYQyxnQkFBaUI7UUEwQmpCQyxZQUFhO1FBeUJiQyxnQkFBaUI7UUEyQmpCWCxXQUFXO0lBQ2I7SUFDQTtRQUNFWixJQUFJO1FBQ0pDLE9BQU87UUFDUEcsVUFBVTtRQUNWYyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsVUFBVztRQW9CWEMsZ0JBQWlCO1FBMEJqQkMsWUFBYTtRQXlCYkMsZ0JBQWlCO1FBMEJqQlgsV0FBVztJQUNiO0NBQ0Q7QUFFTSxNQUFNWSwyQkFBa0RQLGtCQUFrQkYsR0FBRyxDQUFDVSxDQUFBQSxPQUFTO1FBQzVGekIsSUFBSXlCLEtBQUt6QixFQUFFO1FBQ1hDLE9BQU93QixLQUFLeEIsS0FBSztRQUNqQkcsVUFBVXFCLEtBQUtyQixRQUFRO1FBQ3ZCYyxNQUFNTyxLQUFLUCxJQUFJO1FBQ2ZDLGFBQWFNLEtBQUtOLFdBQVc7UUFDN0JQLFdBQVdhLEtBQUtiLFNBQVM7SUFDM0IsSUFBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcRGVza3RvcFxcUHJvamVjdHNcXEFzdGV3YWlcXFRpbGtUZWJlYlxcdGlsa3RiZWIgKDEpXFxsaWJcXG1vY2stZGF0YS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEJvb2ssIEJvb2tQcmV2aWV3IH0gZnJvbSBcIkAvdHlwZXMvYm9va1wiXG5cbi8vIE1vY2sgZGF0YSBmb3IgYm9va3MgLSB0aGlzIHdpbGwgYmUgcmVwbGFjZWQgYnkgRGphbmdvIEFQSSBjYWxsc1xuZXhwb3J0IGNvbnN0IG1vY2tCb29rczogQm9va1tdID0gW1xuICB7XG4gICAgaWQ6IFwidGhlLXBzeWNob2xvZ3ktb2YtbW9uZXlcIixcbiAgICB0aXRsZTogXCJUaGUgUHN5Y2hvbG9neSBPZiBNb25leVwiLFxuICAgIGF1dGhvcjogXCJNb3JnYW4gSG91c2VsXCIsXG4gICAgY292ZXJVcmw6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9MjQwJndpZHRoPTE4MFwiLFxuICAgIGNhdGVnb3J5OiBcIkZpbmFuY2VcIixcbiAgICByYXRpbmc6IDQuNCxcbiAgICBwYWdlczogMjQyLFxuICAgIGxhbmd1YWdlOiBcIkVuZ2xpc2hcIixcbiAgICBwcmljZTogMTIuOTksXG4gICAgc3VtbWFyeTogYFxuICAgICAgPHA+VGhlIFBzeWNob2xvZ3kgb2YgTW9uZXkgZXhwbG9yZXMgaG93IG1vbmV5IG1vdmVzIGFyb3VuZCBpbiBhbiBlY29ub215IGFuZCBob3cgcGVvcGxlIGJlaGF2ZSB3aXRoIGl0LiBUaGUgYXV0aG9yLCBNb3JnYW4gSG91c2VsLCBwcm92aWRlcyB0aW1lbGVzcyBsZXNzb25zIG9uIHdlYWx0aCwgZ3JlZWQsIGFuZCBoYXBwaW5lc3MuPC9wPlxuICAgICAgXG4gICAgICA8aDM+S2V5IEluc2lnaHRzOjwvaDM+XG4gICAgICBcbiAgICAgIDxwPjxzdHJvbmc+MS4gRmluYW5jaWFsIHN1Y2Nlc3MgaXMgbm90IGEgaGFyZCBzY2llbmNlPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+SXQncyBhIHNvZnQgc2tpbGwgd2hlcmUgaG93IHlvdSBiZWhhdmUgaXMgbW9yZSBpbXBvcnRhbnQgdGhhbiB3aGF0IHlvdSBrbm93LiBGaW5hbmNpYWwgb3V0Y29tZXMgYXJlIGRyaXZlbiBieSBsdWNrLCBpbmRlcGVuZGVudCBvZiBpbnRlbGxpZ2VuY2UgYW5kIGVmZm9ydC48L3A+XG4gICAgICBcbiAgICAgIDxwPjxzdHJvbmc+Mi4gTm8gb25lIGlzIGNyYXp5IHdpdGggbW9uZXk8L3N0cm9uZz48L3A+XG4gICAgICA8cD5QZW9wbGUgbWFrZSBmaW5hbmNpYWwgZGVjaXNpb25zIGJhc2VkIG9uIHRoZWlyIHVuaXF1ZSBleHBlcmllbmNlcywgdGhlaXIgb3duIHBlcnNvbmFsIGhpc3RvcnksIHVuaXF1ZSB3b3JsZHZpZXcsIGVnbywgcHJpZGUsIG1hcmtldGluZywgYW5kIG9kZCBpbmNlbnRpdmVzLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz4zLiBMdWNrIGFuZCByaXNrIGFyZSBzaWJsaW5nczwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPlRoZXkgYXJlIGJvdGggdGhlIHJlYWxpdHkgdGhhdCBldmVyeSBvdXRjb21lIGluIGxpZmUgaXMgZ3VpZGVkIGJ5IGZvcmNlcyBvdGhlciB0aGFuIGluZGl2aWR1YWwgZWZmb3J0LiBUaGV5IGJvdGggaGFwcGVuIGJlY2F1c2UgdGhlIHdvcmxkIGlzIHRvbyBjb21wbGV4IHRvIGFsbG93IDEwMCUgb2YgeW91ciBhY3Rpb25zIHRvIGRpY3RhdGUgMTAwJSBvZiB5b3VyIG91dGNvbWVzLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz40LiBOZXZlciBlbm91Z2g8L3N0cm9uZz48L3A+XG4gICAgICA8cD5XaGVuIHJpY2ggcGVvcGxlIGRvIGNyYXp5IHRoaW5ncywgaXQncyBvZnRlbiBhIGNhc2Ugb2YgdHJ5aW5nIHRvIGZlZWwgdmFsdWVkIGFmdGVyIGFscmVhZHkgaGF2aW5nIG1vcmUgbW9uZXkgdGhhbiB0aGV5IGtub3cgd2hhdCB0byBkbyB3aXRoLjwvcD5cbiAgICBgLFxuICAgIGtleUluc2lnaHRzOiBgXG4gICAgICA8cD48c3Ryb25nPjEuIEZpbmFuY2lhbCBzdWNjZXNzIGlzIG5vdCBhIGhhcmQgc2NpZW5jZTwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPkl0J3MgYSBzb2Z0IHNraWxsIHdoZXJlIGhvdyB5b3UgYmVoYXZlIGlzIG1vcmUgaW1wb3J0YW50IHRoYW4gd2hhdCB5b3Uga25vdy4gRmluYW5jaWFsIG91dGNvbWVzIGFyZSBkcml2ZW4gYnkgbHVjaywgaW5kZXBlbmRlbnQgb2YgaW50ZWxsaWdlbmNlIGFuZCBlZmZvcnQuPC9wPlxuICAgICAgXG4gICAgICA8cD48c3Ryb25nPjIuIE5vIG9uZSBpcyBjcmF6eSB3aXRoIG1vbmV5PC9zdHJvbmc+PC9wPlxuICAgICAgPHA+UGVvcGxlIG1ha2UgZmluYW5jaWFsIGRlY2lzaW9ucyBiYXNlZCBvbiB0aGVpciB1bmlxdWUgZXhwZXJpZW5jZXMsIHRoZWlyIG93biBwZXJzb25hbCBoaXN0b3J5LCB1bmlxdWUgd29ybGR2aWV3LCBlZ28sIHByaWRlLCBtYXJrZXRpbmcsIGFuZCBvZGQgaW5jZW50aXZlcy48L3A+XG4gICAgICBcbiAgICAgIDxwPjxzdHJvbmc+My4gTHVjayBhbmQgcmlzayBhcmUgc2libGluZ3M8L3N0cm9uZz48L3A+XG4gICAgICA8cD5UaGV5IGFyZSBib3RoIHRoZSByZWFsaXR5IHRoYXQgZXZlcnkgb3V0Y29tZSBpbiBsaWZlIGlzIGd1aWRlZCBieSBmb3JjZXMgb3RoZXIgdGhhbiBpbmRpdmlkdWFsIGVmZm9ydC4gVGhleSBib3RoIGhhcHBlbiBiZWNhdXNlIHRoZSB3b3JsZCBpcyB0b28gY29tcGxleCB0byBhbGxvdyAxMDAlIG9mIHlvdXIgYWN0aW9ucyB0byBkaWN0YXRlIDEwMCUgb2YgeW91ciBvdXRjb21lcy48L3A+XG4gICAgICBcbiAgICAgIDxwPjxzdHJvbmc+NC4gTmV2ZXIgZW5vdWdoPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+V2hlbiByaWNoIHBlb3BsZSBkbyBjcmF6eSB0aGluZ3MsIGl0J3Mgb2Z0ZW4gYSBjYXNlIG9mIHRyeWluZyB0byBmZWVsIHZhbHVlZCBhZnRlciBhbHJlYWR5IGhhdmluZyBtb3JlIG1vbmV5IHRoYW4gdGhleSBrbm93IHdoYXQgdG8gZG8gd2l0aC48L3A+XG4gICAgYCxcbiAgICBhcHBsaWNhdGlvbnM6IGBcbiAgICAgIDxwPjxzdHJvbmc+Rm9yIFBlcnNvbmFsIEZpbmFuY2U6PC9zdHJvbmc+PC9wPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+U2F2ZSBtb25leSB3aXRob3V0IGEgc3BlY2lmaWMgZ29hbCBpbiBtaW5kPC9saT5cbiAgICAgICAgPGxpPkdhaW4gY29udHJvbCBvdmVyIHlvdXIgdGltZTwvbGk+XG4gICAgICAgIDxsaT5CZSByZWFzb25hYmxlIHJhdGhlciB0aGFuIHJhdGlvbmFsPC9saT5cbiAgICAgICAgPGxpPkFpbSBmb3IgZW5vdWdoLCBub3QgZm9yIG1heGltdW08L2xpPlxuICAgICAgPC91bD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz5Gb3IgSW52ZXN0b3JzOjwvc3Ryb25nPjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPlVuZGVyc3RhbmQgdGhlIHJvbGUgb2YgbHVjayBhbmQgcmlzazwvbGk+XG4gICAgICAgIDxsaT5Lbm93IHRoYXQgZ2V0dGluZyB3ZWFsdGh5IGFuZCBzdGF5aW5nIHdlYWx0aHkgYXJlIGRpZmZlcmVudCBza2lsbHM8L2xpPlxuICAgICAgICA8bGk+TG9uZyB0YWlscyBkcml2ZSBldmVyeXRoaW5nIC0gYSBzbWFsbCBudW1iZXIgb2YgZXZlbnRzIGNhbiBhY2NvdW50IGZvciB0aGUgbWFqb3JpdHkgb2Ygb3V0Y29tZXM8L2xpPlxuICAgICAgICA8bGk+VXNlIHJvb20gZm9yIGVycm9yIHdoZW4gaW52ZXN0aW5nIC0gcHJlcGFyZSBmb3IgYSByYW5nZSBvZiBvdXRjb21lczwvbGk+XG4gICAgICA8L3VsPlxuICAgIGAsXG4gICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgfSxcbiAge1xuICAgIGlkOiBcImF0b21pYy1oYWJpdHNcIixcbiAgICB0aXRsZTogXCJBdG9taWMgSGFiaXRzXCIsXG4gICAgYXV0aG9yOiBcIkphbWVzIENsZWFyXCIsXG4gICAgY292ZXJVcmw6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9MjQwJndpZHRoPTE4MFwiLFxuICAgIGNhdGVnb3J5OiBcIlByb2R1Y3Rpdml0eVwiLFxuICAgIHJhdGluZzogNC45LFxuICAgIHBhZ2VzOiAzMjAsXG4gICAgbGFuZ3VhZ2U6IFwiRW5nbGlzaFwiLFxuICAgIHByaWNlOiAxMy45OSxcbiAgICBzdW1tYXJ5OiBgXG4gICAgICA8cD5BdG9taWMgSGFiaXRzIG9mZmVycyBhIHByb3ZlbiBmcmFtZXdvcmsgZm9yIGltcHJvdmluZyBldmVyeSBkYXkuIEphbWVzIENsZWFyIHJldmVhbHMgcHJhY3RpY2FsIHN0cmF0ZWdpZXMgdGhhdCB3aWxsIHRlYWNoIHlvdSBleGFjdGx5IGhvdyB0byBmb3JtIGdvb2QgaGFiaXRzLCBicmVhayBiYWQgb25lcywgYW5kIG1hc3RlciB0aGUgdGlueSBiZWhhdmlvcnMgdGhhdCBsZWFkIHRvIHJlbWFya2FibGUgcmVzdWx0cy48L3A+XG4gICAgICBcbiAgICAgIDxoMz5LZXkgSW5zaWdodHM6PC9oMz5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz4xLiBIYWJpdHMgYXJlIHRoZSBjb21wb3VuZCBpbnRlcmVzdCBvZiBzZWxmLWltcHJvdmVtZW50PC9zdHJvbmc+PC9wPlxuICAgICAgPHA+U21hbGwgY2hhbmdlcyBvZnRlbiBhcHBlYXIgdG8gbWFrZSBubyBkaWZmZXJlbmNlIHVudGlsIHlvdSBjcm9zcyBhIGNyaXRpY2FsIHRocmVzaG9sZC4gVGhlIGVmZmVjdHMgb2YgeW91ciBoYWJpdHMgbXVsdGlwbHkgYXMgeW91IHJlcGVhdCB0aGVtLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz4yLiBGb2N1cyBvbiBzeXN0ZW1zIGluc3RlYWQgb2YgZ29hbHM8L3N0cm9uZz48L3A+XG4gICAgICA8cD5Hb2FscyBhcmUgYWJvdXQgdGhlIHJlc3VsdHMgeW91IHdhbnQgdG8gYWNoaWV2ZS4gU3lzdGVtcyBhcmUgYWJvdXQgdGhlIHByb2Nlc3NlcyB0aGF0IGxlYWQgdG8gdGhvc2UgcmVzdWx0cy4gRm9jdXMgb24gdGhlIHN5c3RlbSwgbm90IHRoZSBnb2FsLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz4zLiBUaGUgRm91ciBMYXdzIG9mIEJlaGF2aW9yIENoYW5nZTwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPk1ha2UgaXQgb2J2aW91cywgbWFrZSBpdCBhdHRyYWN0aXZlLCBtYWtlIGl0IGVhc3ksIGFuZCBtYWtlIGl0IHNhdGlzZnlpbmcuIFRoZXNlIGFyZSB0aGUgZnVuZGFtZW50YWwgcHJpbmNpcGxlcyBiZWhpbmQgaGFiaXQgZm9ybWF0aW9uLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz40LiBJZGVudGl0eS1iYXNlZCBoYWJpdHM8L3N0cm9uZz48L3A+XG4gICAgICA8cD5UaGUgbW9zdCBlZmZlY3RpdmUgd2F5IHRvIGNoYW5nZSB5b3VyIGhhYml0cyBpcyB0byBmb2N1cyBub3Qgb24gd2hhdCB5b3Ugd2FudCB0byBhY2hpZXZlLCBidXQgb24gd2hvIHlvdSB3aXNoIHRvIGJlY29tZS48L3A+XG4gICAgYCxcbiAgICBrZXlJbnNpZ2h0czogYFxuICAgICAgPHA+PHN0cm9uZz4xLiBIYWJpdHMgYXJlIHRoZSBjb21wb3VuZCBpbnRlcmVzdCBvZiBzZWxmLWltcHJvdmVtZW50PC9zdHJvbmc+PC9wPlxuICAgICAgPHA+U21hbGwgY2hhbmdlcyBvZnRlbiBhcHBlYXIgdG8gbWFrZSBubyBkaWZmZXJlbmNlIHVudGlsIHlvdSBjcm9zcyBhIGNyaXRpY2FsIHRocmVzaG9sZC4gVGhlIGVmZmVjdHMgb2YgeW91ciBoYWJpdHMgbXVsdGlwbHkgYXMgeW91IHJlcGVhdCB0aGVtLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz4yLiBGb2N1cyBvbiBzeXN0ZW1zIGluc3RlYWQgb2YgZ29hbHM8L3N0cm9uZz48L3A+XG4gICAgICA8cD5Hb2FscyBhcmUgYWJvdXQgdGhlIHJlc3VsdHMgeW91IHdhbnQgdG8gYWNoaWV2ZS4gU3lzdGVtcyBhcmUgYWJvdXQgdGhlIHByb2Nlc3NlcyB0aGF0IGxlYWQgdG8gdGhvc2UgcmVzdWx0cy4gRm9jdXMgb24gdGhlIHN5c3RlbSwgbm90IHRoZSBnb2FsLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz4zLiBUaGUgRm91ciBMYXdzIG9mIEJlaGF2aW9yIENoYW5nZTwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPk1ha2UgaXQgb2J2aW91cywgbWFrZSBpdCBhdHRyYWN0aXZlLCBtYWtlIGl0IGVhc3ksIGFuZCBtYWtlIGl0IHNhdGlzZnlpbmcuIFRoZXNlIGFyZSB0aGUgZnVuZGFtZW50YWwgcHJpbmNpcGxlcyBiZWhpbmQgaGFiaXQgZm9ybWF0aW9uLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz40LiBJZGVudGl0eS1iYXNlZCBoYWJpdHM8L3N0cm9uZz48L3A+XG4gICAgICA8cD5UaGUgbW9zdCBlZmZlY3RpdmUgd2F5IHRvIGNoYW5nZSB5b3VyIGhhYml0cyBpcyB0byBmb2N1cyBub3Qgb24gd2hhdCB5b3Ugd2FudCB0byBhY2hpZXZlLCBidXQgb24gd2hvIHlvdSB3aXNoIHRvIGJlY29tZS48L3A+XG4gICAgYCxcbiAgICBhcHBsaWNhdGlvbnM6IGBcbiAgICAgIDxwPjxzdHJvbmc+Rm9yIFBlcnNvbmFsIERldmVsb3BtZW50Ojwvc3Ryb25nPjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPlN0YXJ0IHdpdGggYW4gaW5jcmVkaWJseSBzbWFsbCBoYWJpdDwvbGk+XG4gICAgICAgIDxsaT5JbmNyZWFzZSB5b3VyIGhhYml0IGluIHZlcnkgc21hbGwgd2F5czwvbGk+XG4gICAgICAgIDxsaT5CcmVhayBoYWJpdHMgaW50byBjaHVua3M8L2xpPlxuICAgICAgICA8bGk+V2hlbiB5b3Ugc2xpcCwgZ2V0IGJhY2sgb24gdHJhY2sgcXVpY2tseTwvbGk+XG4gICAgICA8L3VsPlxuICAgICAgXG4gICAgICA8cD48c3Ryb25nPkZvciBCdXNpbmVzczo8L3N0cm9uZz48L3A+XG4gICAgICA8dWw+XG4gICAgICAgIDxsaT5DcmVhdGUgYW4gZW52aXJvbm1lbnQgd2hlcmUgZG9pbmcgdGhlIHJpZ2h0IHRoaW5nIGlzIGFzIGVhc3kgYXMgcG9zc2libGU8L2xpPlxuICAgICAgICA8bGk+TWFrZSBnb29kIGhhYml0cyBvYnZpb3VzIGluIHlvdXIgZW52aXJvbm1lbnQ8L2xpPlxuICAgICAgICA8bGk+UmVkdWNlIGZyaWN0aW9uIGZvciBnb29kIGhhYml0czwvbGk+XG4gICAgICAgIDxsaT5JbmNyZWFzZSBmcmljdGlvbiBmb3IgYmFkIGhhYml0czwvbGk+XG4gICAgICA8L3VsPlxuICAgIGAsXG4gICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgfSxcbiAge1xuICAgIGlkOiBcInNhcGllbnNcIixcbiAgICB0aXRsZTogXCJTYXBpZW5zXCIsXG4gICAgYXV0aG9yOiBcIll1dmFsIE5vYWggSGFyYXJpXCIsXG4gICAgY292ZXJVcmw6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9MjQwJndpZHRoPTE4MFwiLFxuICAgIGNhdGVnb3J5OiBcIkhpc3RvcnlcIixcbiAgICByYXRpbmc6IDQuNyxcbiAgICBwYWdlczogNDY0LFxuICAgIGxhbmd1YWdlOiBcIkVuZ2xpc2hcIixcbiAgICBwcmljZTogMTQuOTksXG4gICAgc3VtbWFyeTogYFxuICAgICAgPHA+U2FwaWVuczogQSBCcmllZiBIaXN0b3J5IG9mIEh1bWFua2luZCBpcyBhIGJvb2sgYnkgWXV2YWwgTm9haCBIYXJhcmkgdGhhdCBleHBsb3JlcyB0aGUgaGlzdG9yeSBhbmQgaW1wYWN0IG9mIEhvbW8gc2FwaWVucyBvbiB0aGUgd29ybGQuIEl0IHRyYWNlcyB0aGUgZXZvbHV0aW9uIG9mIG91ciBzcGVjaWVzIGZyb20gdGhlIGVtZXJnZW5jZSBvZiBIb21vIHNhcGllbnMgaW4gQWZyaWNhIHRvIG91ciBjdXJyZW50IHN0YXR1cyBhcyB0aGUgZG9taW5hbnQgZm9yY2Ugb24gRWFydGguPC9wPlxuICAgICAgXG4gICAgICA8aDM+S2V5IEluc2lnaHRzOjwvaDM+XG4gICAgICBcbiAgICAgIDxwPjxzdHJvbmc+MS4gVGhlIENvZ25pdGl2ZSBSZXZvbHV0aW9uPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+QXJvdW5kIDcwLDAwMCB5ZWFycyBhZ28sIEhvbW8gc2FwaWVucyBkZXZlbG9wZWQgdW5pcXVlIGNvZ25pdGl2ZSBhYmlsaXRpZXMsIHBhcnRpY3VsYXJseSB0aGUgY2FwYWNpdHkgZm9yIGZpY3Rpb24gYW5kIGltYWdpbmF0aW9uLCB3aGljaCBhbGxvd2VkIGZvciB1bnByZWNlZGVudGVkIGNvb3BlcmF0aW9uIGFtb25nIGxhcmdlIGdyb3Vwcy48L3A+XG4gICAgICBcbiAgICAgIDxwPjxzdHJvbmc+Mi4gVGhlIEFncmljdWx0dXJhbCBSZXZvbHV0aW9uPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+QmVnaW5uaW5nIGFib3V0IDEyLDAwMCB5ZWFycyBhZ28sIGh1bWFucyB0cmFuc2l0aW9uZWQgZnJvbSBodW50aW5nIGFuZCBnYXRoZXJpbmcgdG8gZmFybWluZywgd2hpY2ggSGFyYXJpIGRlc2NyaWJlcyBhcyBcImhpc3RvcnkncyBiaWdnZXN0IGZyYXVkXCIgYmVjYXVzZSBpdCBsZWQgdG8gaGFyZGVyIHdvcmssIGxlc3MgbGVpc3VyZSwgYW5kIHBvb3JlciBoZWFsdGggZm9yIG1vc3QgcGVvcGxlLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz4zLiBUaGUgVW5pZmljYXRpb24gb2YgSHVtYW5raW5kPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+T3ZlciB0aW1lLCBodW1hbnMgaGF2ZSBjcmVhdGVkIGluY3JlYXNpbmdseSBsYXJnZXIgbmV0d29ya3Mgb2YgY29vcGVyYXRpb24gdGhyb3VnaCBzaGFyZWQgbXl0aHMsIGluY2x1ZGluZyBtb25leSwgZW1waXJlcywgYW5kIHJlbGlnaW9ucy48L3A+XG4gICAgICBcbiAgICAgIDxwPjxzdHJvbmc+NC4gVGhlIFNjaWVudGlmaWMgUmV2b2x1dGlvbjwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPlRoZSBsYXN0IDUwMCB5ZWFycyBoYXZlIHNlZW4gYW4gZXhwbG9zaW9uIG9mIGh1bWFuIHBvd2VyIHRocm91Z2ggdGhlIHdpbGxpbmduZXNzIHRvIGFkbWl0IGlnbm9yYW5jZSBhbmQgdGhlIGRldmVsb3BtZW50IG9mIHRoZSBzY2llbnRpZmljIG1ldGhvZC48L3A+XG4gICAgYCxcbiAgICBrZXlJbnNpZ2h0czogYFxuICAgICAgPHA+PHN0cm9uZz4xLiBUaGUgQ29nbml0aXZlIFJldm9sdXRpb248L3N0cm9uZz48L3A+XG4gICAgICA8cD5Bcm91bmQgNzAsMDAwIHllYXJzIGFnbywgSG9tbyBzYXBpZW5zIGRldmVsb3BlZCB1bmlxdWUgY29nbml0aXZlIGFiaWxpdGllcywgcGFydGljdWxhcmx5IHRoZSBjYXBhY2l0eSBmb3IgZmljdGlvbiBhbmQgaW1hZ2luYXRpb24sIHdoaWNoIGFsbG93ZWQgZm9yIHVucHJlY2VkZW50ZWQgY29vcGVyYXRpb24gYW1vbmcgbGFyZ2UgZ3JvdXBzLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz4yLiBUaGUgQWdyaWN1bHR1cmFsIFJldm9sdXRpb248L3N0cm9uZz48L3A+XG4gICAgICA8cD5CZWdpbm5pbmcgYWJvdXQgMTIsMDAwIHllYXJzIGFnbywgaHVtYW5zIHRyYW5zaXRpb25lZCBmcm9tIGh1bnRpbmcgYW5kIGdhdGhlcmluZyB0byBmYXJtaW5nLCB3aGljaCBIYXJhcmkgZGVzY3JpYmVzIGFzIFwiaGlzdG9yeSdzIGJpZ2dlc3QgZnJhdWRcIiBiZWNhdXNlIGl0IGxlZCB0byBoYXJkZXIgd29yaywgbGVzcyBsZWlzdXJlLCBhbmQgcG9vcmVyIGhlYWx0aCBmb3IgbW9zdCBwZW9wbGUuPC9wPlxuICAgICAgXG4gICAgICA8cD48c3Ryb25nPjMuIFRoZSBVbmlmaWNhdGlvbiBvZiBIdW1hbmtpbmQ8L3N0cm9uZz48L3A+XG4gICAgICA8cD5PdmVyIHRpbWUsIGh1bWFucyBoYXZlIGNyZWF0ZWQgaW5jcmVhc2luZ2x5IGxhcmdlciBuZXR3b3JrcyBvZiBjb29wZXJhdGlvbiB0aHJvdWdoIHNoYXJlZCBteXRocywgaW5jbHVkaW5nIG1vbmV5LCBlbXBpcmVzLCBhbmQgcmVsaWdpb25zLjwvcD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz40LiBUaGUgU2NpZW50aWZpYyBSZXZvbHV0aW9uPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+VGhlIGxhc3QgNTAwIHllYXJzIGhhdmUgc2VlbiBhbiBleHBsb3Npb24gb2YgaHVtYW4gcG93ZXIgdGhyb3VnaCB0aGUgd2lsbGluZ25lc3MgdG8gYWRtaXQgaWdub3JhbmNlIGFuZCB0aGUgZGV2ZWxvcG1lbnQgb2YgdGhlIHNjaWVudGlmaWMgbWV0aG9kLjwvcD5cbiAgICBgLFxuICAgIGFwcGxpY2F0aW9uczogYFxuICAgICAgPHA+PHN0cm9uZz5Gb3IgVW5kZXJzdGFuZGluZyBTb2NpZXR5Ojwvc3Ryb25nPjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPlJlY29nbml6ZSBob3cgc2hhcmVkIG15dGhzIGFuZCBzdG9yaWVzIHNoYXBlIG91ciB3b3JsZDwvbGk+XG4gICAgICAgIDxsaT5VbmRlcnN0YW5kIHRoZSBoaXN0b3JpY2FsIGNvbnRleHQgb2YgY3VycmVudCBzb2NpYWwgc3RydWN0dXJlczwvbGk+XG4gICAgICAgIDxsaT5RdWVzdGlvbiB3aGV0aGVyIFwicHJvZ3Jlc3NcIiBhbHdheXMgbWVhbnMgaW1wcm92ZW1lbnQ8L2xpPlxuICAgICAgICA8bGk+Q29uc2lkZXIgdGhlIGV0aGljYWwgaW1wbGljYXRpb25zIG9mIHRlY2hub2xvZ2ljYWwgYWR2YW5jZW1lbnQ8L2xpPlxuICAgICAgPC91bD5cbiAgICAgIFxuICAgICAgPHA+PHN0cm9uZz5Gb3IgQnVzaW5lc3MgYW5kIExlYWRlcnNoaXA6PC9zdHJvbmc+PC9wPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+QXBwcmVjaWF0ZSBob3cgc2hhcmVkIG5hcnJhdGl2ZXMgY3JlYXRlIGNvaGVzaW9uIGluIG9yZ2FuaXphdGlvbnM8L2xpPlxuICAgICAgICA8bGk+VW5kZXJzdGFuZCBob3cgbW9uZXkgYW5kIGNvcnBvcmF0aW9ucyBhcmUgc29jaWFsIGNvbnN0cnVjdHMgdGhhdCBkZXBlbmQgb24gdHJ1c3Q8L2xpPlxuICAgICAgICA8bGk+Q29uc2lkZXIgdGhlIGxvbmctdGVybSBpbXBsaWNhdGlvbnMgb2Ygc2hvcnQtdGVybSBkZWNpc2lvbnM8L2xpPlxuICAgICAgICA8bGk+UmVjb2duaXplIHBhdHRlcm5zIG9mIGh1bWFuIGJlaGF2aW9yIHRoYXQgcGVyc2lzdCBhY3Jvc3MgdGltZTwvbGk+XG4gICAgICA8L3VsPlxuICAgIGAsXG4gICAgaXNQcmVtaXVtOiB0cnVlLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiemVyby10by1vbmVcIixcbiAgICB0aXRsZTogXCJaZXJvIHRvIE9uZVwiLFxuICAgIGF1dGhvcjogXCJQZXRlciBUaGllbFwiLFxuICAgIGNvdmVyVXJsOiBcIi9wbGFjZWhvbGRlci5zdmc/aGVpZ2h0PTI0MCZ3aWR0aD0xODBcIixcbiAgICBjYXRlZ29yeTogXCJFbnRyZXByZW5ldXJzaGlwXCIsXG4gICAgcmF0aW5nOiA0LjgsXG4gICAgcGFnZXM6IDIyNCxcbiAgICBsYW5ndWFnZTogXCJFbmdsaXNoXCIsXG4gICAgcHJpY2U6IDEyLjk5LFxuICAgIHN1bW1hcnk6IGBcbiAgICAgIDxwPlplcm8gdG8gT25lIHByZXNlbnRzIGF0IG9uY2UgYW4gb3B0aW1pc3RpYyB2aWV3IG9mIHRoZSBmdXR1cmUgb2YgcHJvZ3Jlc3MgaW4gQW1lcmljYSBhbmQgYSBuZXcgd2F5IG9mIHRoaW5raW5nIGFib3V0IGlubm92YXRpb246IGl0IHN0YXJ0cyBieSBsZWFybmluZyB0byBhc2sgdGhlIHF1ZXN0aW9ucyB0aGF0IGxlYWQgeW91IHRvIGZpbmQgdmFsdWUgaW4gdW5leHBlY3RlZCBwbGFjZXMuPC9wPlxuXG4gICAgICA8aDM+S2V5IEluc2lnaHRzOjwvaDM+XG5cbiAgICAgIDxwPjxzdHJvbmc+MS4gVmVydGljYWwgdnMuIEhvcml6b250YWwgUHJvZ3Jlc3M8L3N0cm9uZz48L3A+XG4gICAgICA8cD5Ib3Jpem9udGFsIHByb2dyZXNzIG1lYW5zIGNvcHlpbmcgdGhpbmdzIHRoYXQgd29ya+KAlGdvaW5nIGZyb20gMSB0byBuLiBWZXJ0aWNhbCBwcm9ncmVzcyBtZWFucyBkb2luZyBuZXcgdGhpbmdz4oCUZ29pbmcgZnJvbSAwIHRvIDEuIFRoZSBmb2N1cyBzaG91bGQgYmUgb24gY3JlYXRpbmcgc29tZXRoaW5nIG5ldyByYXRoZXIgdGhhbiBjb3B5aW5nIGV4aXN0aW5nIG1vZGVscy48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+Mi4gTW9ub3BvbGllcyB2cy4gQ29tcGV0aXRpb248L3N0cm9uZz48L3A+XG4gICAgICA8cD5Db21wZXRpdGlvbiBpcyBmb3IgbG9zZXJzLiBVbmRlciBwZXJmZWN0IGNvbXBldGl0aW9uLCBubyBjb21wYW55IG1ha2VzIGVjb25vbWljIHByb2ZpdC4gVGhlIGdvYWwgc2hvdWxkIGJlIHRvIGNyZWF0ZSBhIG1vbm9wb2x5IHRocm91Z2ggaW5ub3ZhdGlvbi48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+My4gVGhlIFBvd2VyIExhdzwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPkEgc21hbGwgbnVtYmVyIG9mIGNvbXBhbmllcyByYWRpY2FsbHkgb3V0cGVyZm9ybSBhbGwgb3RoZXJzLiBUaGlzIHByaW5jaXBsZSBhcHBsaWVzIHRvIHZlbnR1cmUgY2FwaXRhbCBpbnZlc3RtZW50cywgd2hlcmUgYSBzaW5nbGUgaW52ZXN0bWVudCBtYXkgcmV0dXJuIG1vcmUgdGhhbiBhbGwgb3RoZXJzIGNvbWJpbmVkLjwvcD5cblxuICAgICAgPHA+PHN0cm9uZz40LiBTZWNyZXRzPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+RXZlcnkgZ3JlYXQgYnVzaW5lc3MgaXMgYnVpbHQgYXJvdW5kIGEgc2VjcmV0IHRoYXQncyBoaWRkZW4gZnJvbSB0aGUgb3V0c2lkZS4gR3JlYXQgY29tcGFuaWVzIGZpbmQgdmFsdWUgaW4gdW5leHBlY3RlZCBwbGFjZXMgYnkgdGhpbmtpbmcgYWJvdXQgYnVzaW5lc3MgZnJvbSBmaXJzdCBwcmluY2lwbGVzLjwvcD5cbiAgICBgLFxuICAgIGtleUluc2lnaHRzOiBgXG4gICAgICA8cD48c3Ryb25nPjEuIFZlcnRpY2FsIHZzLiBIb3Jpem9udGFsIFByb2dyZXNzPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+SG9yaXpvbnRhbCBwcm9ncmVzcyBtZWFucyBjb3B5aW5nIHRoaW5ncyB0aGF0IHdvcmvigJRnb2luZyBmcm9tIDEgdG8gbi4gVmVydGljYWwgcHJvZ3Jlc3MgbWVhbnMgZG9pbmcgbmV3IHRoaW5nc+KAlGdvaW5nIGZyb20gMCB0byAxLiBUaGUgZm9jdXMgc2hvdWxkIGJlIG9uIGNyZWF0aW5nIHNvbWV0aGluZyBuZXcgcmF0aGVyIHRoYW4gY29weWluZyBleGlzdGluZyBtb2RlbHMuPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjIuIE1vbm9wb2xpZXMgdnMuIENvbXBldGl0aW9uPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+Q29tcGV0aXRpb24gaXMgZm9yIGxvc2Vycy4gVW5kZXIgcGVyZmVjdCBjb21wZXRpdGlvbiwgbm8gY29tcGFueSBtYWtlcyBlY29ub21pYyBwcm9maXQuIFRoZSBnb2FsIHNob3VsZCBiZSB0byBjcmVhdGUgYSBtb25vcG9seSB0aHJvdWdoIGlubm92YXRpb24uPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjMuIFRoZSBQb3dlciBMYXc8L3N0cm9uZz48L3A+XG4gICAgICA8cD5BIHNtYWxsIG51bWJlciBvZiBjb21wYW5pZXMgcmFkaWNhbGx5IG91dHBlcmZvcm0gYWxsIG90aGVycy4gVGhpcyBwcmluY2lwbGUgYXBwbGllcyB0byB2ZW50dXJlIGNhcGl0YWwgaW52ZXN0bWVudHMsIHdoZXJlIGEgc2luZ2xlIGludmVzdG1lbnQgbWF5IHJldHVybiBtb3JlIHRoYW4gYWxsIG90aGVycyBjb21iaW5lZC48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+NC4gU2VjcmV0czwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPkV2ZXJ5IGdyZWF0IGJ1c2luZXNzIGlzIGJ1aWx0IGFyb3VuZCBhIHNlY3JldCB0aGF0J3MgaGlkZGVuIGZyb20gdGhlIG91dHNpZGUuIEdyZWF0IGNvbXBhbmllcyBmaW5kIHZhbHVlIGluIHVuZXhwZWN0ZWQgcGxhY2VzIGJ5IHRoaW5raW5nIGFib3V0IGJ1c2luZXNzIGZyb20gZmlyc3QgcHJpbmNpcGxlcy48L3A+XG4gICAgYCxcbiAgICBhcHBsaWNhdGlvbnM6IGBcbiAgICAgIDxwPjxzdHJvbmc+Rm9yIEVudHJlcHJlbmV1cnM6PC9zdHJvbmc+PC9wPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+Rm9jdXMgb24gY3JlYXRpbmcgc29tZXRoaW5nIG5ldyByYXRoZXIgdGhhbiBpbXByb3ZpbmcgZXhpc3RpbmcgcHJvZHVjdHM8L2xpPlxuICAgICAgICA8bGk+QWltIHRvIGNyZWF0ZSBhIG1vbm9wb2x5IHRocm91Z2ggdW5pcXVlIHRlY2hub2xvZ3ksIG5ldHdvcmsgZWZmZWN0cywgZWNvbm9taWVzIG9mIHNjYWxlLCBhbmQgYnJhbmRpbmc8L2xpPlxuICAgICAgICA8bGk+U3RhcnQgc21hbGwgYW5kIG1vbm9wb2xpemUgYSBuaWNoZSBtYXJrZXQgYmVmb3JlIGV4cGFuZGluZzwvbGk+XG4gICAgICAgIDxsaT5CdWlsZCBhIGdyZWF0IHRlYW0gd2l0aCBhIHN0cm9uZywgdW5pZmllZCB2aXNpb248L2xpPlxuICAgICAgPC91bD5cblxuICAgICAgPHA+PHN0cm9uZz5Gb3IgSW52ZXN0b3JzOjwvc3Ryb25nPjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPlVuZGVyc3RhbmQgdGhhdCByZXR1cm5zIGZvbGxvdyBhIHBvd2VyIGxhd+KAlGEgZmV3IGludmVzdG1lbnRzIHdpbGwgb3V0cGVyZm9ybSBhbGwgb3RoZXJzPC9saT5cbiAgICAgICAgPGxpPkxvb2sgZm9yIGNvbXBhbmllcyB3aXRoIHByb3ByaWV0YXJ5IHRlY2hub2xvZ3ksIG5ldHdvcmsgZWZmZWN0cywgZWNvbm9taWVzIG9mIHNjYWxlLCBhbmQgc3Ryb25nIGJyYW5kaW5nPC9saT5cbiAgICAgICAgPGxpPkV2YWx1YXRlIHRoZSBmb3VuZGluZyB0ZWFtJ3MgZHluYW1pY3MgYW5kIHZpc2lvbjwvbGk+XG4gICAgICAgIDxsaT5Db25zaWRlciB3aGV0aGVyIHRoZSBjb21wYW55IGhhcyBkaXNjb3ZlcmVkIGEgdW5pcXVlIFwic2VjcmV0XCIgYWJvdXQgdGhlIG1hcmtldDwvbGk+XG4gICAgICA8L3VsPlxuICAgIGAsXG4gICAgaXNQcmVtaXVtOiB0cnVlLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiZ29vZC10by1ncmVhdFwiLFxuICAgIHRpdGxlOiBcIkdvb2QgdG8gR3JlYXRcIixcbiAgICBhdXRob3I6IFwiSmltIENvbGxpbnNcIixcbiAgICBjb3ZlclVybDogXCIvcGxhY2Vob2xkZXIuc3ZnP2hlaWdodD0yNDAmd2lkdGg9MTgwXCIsXG4gICAgY2F0ZWdvcnk6IFwiTGVhZGVyc2hpcFwiLFxuICAgIHJhdGluZzogNC43LFxuICAgIHBhZ2VzOiAzMjAsXG4gICAgbGFuZ3VhZ2U6IFwiRW5nbGlzaFwiLFxuICAgIHByaWNlOiAxMS45OSxcbiAgICBzdW1tYXJ5OiBgXG4gICAgICA8cD5Hb29kIHRvIEdyZWF0IHByZXNlbnRzIHRoZSBmaW5kaW5ncyBvZiBhIGZpdmUteWVhciBzdHVkeSBieSBKaW0gQ29sbGlucyBhbmQgaGlzIHJlc2VhcmNoIHRlYW0uIFRoZSB0ZWFtIGlkZW50aWZpZWQgYSBzZXQgb2YgY29tcGFuaWVzIHRoYXQgbWFkZSB0aGUgbGVhcCBmcm9tIGdvb2QgcmVzdWx0cyB0byBncmVhdCByZXN1bHRzIGFuZCBzdXN0YWluZWQgdGhvc2UgcmVzdWx0cyBmb3IgYXQgbGVhc3QgZmlmdGVlbiB5ZWFycy48L3A+XG5cbiAgICAgIDxoMz5LZXkgSW5zaWdodHM6PC9oMz5cblxuICAgICAgPHA+PHN0cm9uZz4xLiBMZXZlbCA1IExlYWRlcnNoaXA8L3N0cm9uZz48L3A+XG4gICAgICA8cD5MZWFkZXJzIHdobyBoYXZlIGEgdW5pcXVlIGJsZW5kIG9mIHBlcnNvbmFsIGh1bWlsaXR5IGFuZCBwcm9mZXNzaW9uYWwgd2lsbC4gVGhleSBhcmUgYW1iaXRpb3VzIGZvciB0aGUgY29tcGFueSwgbm90IHRoZW1zZWx2ZXMuPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjIuIEZpcnN0IFdobywgVGhlbiBXaGF0PC9zdHJvbmc+PC9wPlxuICAgICAgPHA+R2V0IHRoZSByaWdodCBwZW9wbGUgb24gdGhlIGJ1cywgdGhlIHdyb25nIHBlb3BsZSBvZmYgdGhlIGJ1cywgYW5kIHRoZSByaWdodCBwZW9wbGUgaW4gdGhlIHJpZ2h0IHNlYXRz4oCUdGhlbiBmaWd1cmUgb3V0IHdoZXJlIHRvIGRyaXZlIGl0LjwvcD5cblxuICAgICAgPHA+PHN0cm9uZz4zLiBDb25mcm9udCB0aGUgQnJ1dGFsIEZhY3RzPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+Q3JlYXRlIGEgY3VsdHVyZSB3aGVyZSBwZW9wbGUgaGF2ZSB0aGUgb3Bwb3J0dW5pdHkgdG8gYmUgaGVhcmQgYW5kIHdoZXJlIHRoZSB0cnV0aCBpcyBoZWFyZC4gTWFpbnRhaW4gdW53YXZlcmluZyBmYWl0aCB0aGF0IHlvdSBjYW4gYW5kIHdpbGwgcHJldmFpbCwgcmVnYXJkbGVzcyBvZiBkaWZmaWN1bHRpZXMuPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjQuIFRoZSBIZWRnZWhvZyBDb25jZXB0PC9zdHJvbmc+PC9wPlxuICAgICAgPHA+Rm9jdXMgb24gdGhlIGludGVyc2VjdGlvbiBvZiB0aHJlZSBjaXJjbGVzOiB3aGF0IHlvdSBjYW4gYmUgdGhlIGJlc3QgaW4gdGhlIHdvcmxkIGF0LCB3aGF0IGRyaXZlcyB5b3VyIGVjb25vbWljIGVuZ2luZSwgYW5kIHdoYXQgeW91IGFyZSBkZWVwbHkgcGFzc2lvbmF0ZSBhYm91dC48L3A+XG4gICAgYCxcbiAgICBrZXlJbnNpZ2h0czogYFxuICAgICAgPHA+PHN0cm9uZz4xLiBMZXZlbCA1IExlYWRlcnNoaXA8L3N0cm9uZz48L3A+XG4gICAgICA8cD5MZWFkZXJzIHdobyBoYXZlIGEgdW5pcXVlIGJsZW5kIG9mIHBlcnNvbmFsIGh1bWlsaXR5IGFuZCBwcm9mZXNzaW9uYWwgd2lsbC4gVGhleSBhcmUgYW1iaXRpb3VzIGZvciB0aGUgY29tcGFueSwgbm90IHRoZW1zZWx2ZXMuPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjIuIEZpcnN0IFdobywgVGhlbiBXaGF0PC9zdHJvbmc+PC9wPlxuICAgICAgPHA+R2V0IHRoZSByaWdodCBwZW9wbGUgb24gdGhlIGJ1cywgdGhlIHdyb25nIHBlb3BsZSBvZmYgdGhlIGJ1cywgYW5kIHRoZSByaWdodCBwZW9wbGUgaW4gdGhlIHJpZ2h0IHNlYXRz4oCUdGhlbiBmaWd1cmUgb3V0IHdoZXJlIHRvIGRyaXZlIGl0LjwvcD5cblxuICAgICAgPHA+PHN0cm9uZz4zLiBDb25mcm9udCB0aGUgQnJ1dGFsIEZhY3RzPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+Q3JlYXRlIGEgY3VsdHVyZSB3aGVyZSBwZW9wbGUgaGF2ZSB0aGUgb3Bwb3J0dW5pdHkgdG8gYmUgaGVhcmQgYW5kIHdoZXJlIHRoZSB0cnV0aCBpcyBoZWFyZC4gTWFpbnRhaW4gdW53YXZlcmluZyBmYWl0aCB0aGF0IHlvdSBjYW4gYW5kIHdpbGwgcHJldmFpbCwgcmVnYXJkbGVzcyBvZiBkaWZmaWN1bHRpZXMuPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjQuIFRoZSBIZWRnZWhvZyBDb25jZXB0PC9zdHJvbmc+PC9wPlxuICAgICAgPHA+Rm9jdXMgb24gdGhlIGludGVyc2VjdGlvbiBvZiB0aHJlZSBjaXJjbGVzOiB3aGF0IHlvdSBjYW4gYmUgdGhlIGJlc3QgaW4gdGhlIHdvcmxkIGF0LCB3aGF0IGRyaXZlcyB5b3VyIGVjb25vbWljIGVuZ2luZSwgYW5kIHdoYXQgeW91IGFyZSBkZWVwbHkgcGFzc2lvbmF0ZSBhYm91dC48L3A+XG4gICAgYCxcbiAgICBhcHBsaWNhdGlvbnM6IGBcbiAgICAgIDxwPjxzdHJvbmc+Rm9yIEJ1c2luZXNzIExlYWRlcnM6PC9zdHJvbmc+PC9wPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+RGV2ZWxvcCBMZXZlbCA1IExlYWRlcnNoaXAgcXVhbGl0aWVzOiBhbWJpdGlvbiBmb3IgdGhlIGNvbXBhbnkgb3ZlciBzZWxmPC9saT5cbiAgICAgICAgPGxpPkZvY3VzIG9uIGdldHRpbmcgdGhlIHJpZ2h0IHRlYW0gaW4gcGxhY2UgYmVmb3JlIGRldGVybWluaW5nIHN0cmF0ZWd5PC9saT5cbiAgICAgICAgPGxpPkNyZWF0ZSBhIGN1bHR1cmUgb2YgZGlzY2lwbGluZWQgcGVvcGxlLCB0aG91Z2h0LCBhbmQgYWN0aW9uPC9saT5cbiAgICAgICAgPGxpPkFwcGx5IHRoZSBIZWRnZWhvZyBDb25jZXB0IHRvIGZvY3VzIHJlc291cmNlcyBhbmQgZWZmb3J0czwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8cD48c3Ryb25nPkZvciBPcmdhbml6YXRpb25zOjwvc3Ryb25nPjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPlVzZSB0ZWNobm9sb2d5IGFzIGFuIGFjY2VsZXJhdG9yLCBub3QgYSBjcmVhdG9yIG9mIG1vbWVudHVtPC9saT5cbiAgICAgICAgPGxpPkJ1aWxkIG1vbWVudHVtIGdyYWR1YWxseSB1bnRpbCBicmVha3Rocm91Z2ggb2NjdXJzICh0aGUgZmx5d2hlZWwgZWZmZWN0KTwvbGk+XG4gICAgICAgIDxsaT5NYWludGFpbiBkaXNjaXBsaW5lIHRvIHN0aWNrIHdpdGggd2hhdCB5b3UgY2FuIGJlIGJlc3QgYXQ8L2xpPlxuICAgICAgICA8bGk+Q29uZnJvbnQgcmVhbGl0eSB3aGlsZSBtYWludGFpbmluZyBmYWl0aCBpbiB1bHRpbWF0ZSBzdWNjZXNzPC9saT5cbiAgICAgIDwvdWw+XG4gICAgYCxcbiAgICBpc1ByZW1pdW06IHRydWUsXG4gIH0sXG4gIHtcbiAgICBpZDogXCJ0aGUtbGVhbi1zdGFydHVwXCIsXG4gICAgdGl0bGU6IFwiVGhlIExlYW4gU3RhcnR1cFwiLFxuICAgIGF1dGhvcjogXCJFcmljIFJpZXNcIixcbiAgICBjb3ZlclVybDogXCIvcGxhY2Vob2xkZXIuc3ZnP2hlaWdodD0yNDAmd2lkdGg9MTgwXCIsXG4gICAgY2F0ZWdvcnk6IFwiU3RhcnR1cFwiLFxuICAgIHJhdGluZzogNC42LFxuICAgIHBhZ2VzOiAzMzYsXG4gICAgbGFuZ3VhZ2U6IFwiRW5nbGlzaFwiLFxuICAgIHByaWNlOiAxMC45OSxcbiAgICBzdW1tYXJ5OiBgXG4gICAgICA8cD5UaGUgTGVhbiBTdGFydHVwIGludHJvZHVjZXMgYSBtZXRob2RvbG9neSBmb3IgZGV2ZWxvcGluZyBidXNpbmVzc2VzIGFuZCBwcm9kdWN0cyB0aGF0IGFpbXMgdG8gc2hvcnRlbiBwcm9kdWN0IGRldmVsb3BtZW50IGN5Y2xlcyBhbmQgcmFwaWRseSBkaXNjb3ZlciBpZiBhIHByb3Bvc2VkIGJ1c2luZXNzIG1vZGVsIGlzIHZpYWJsZS48L3A+XG5cbiAgICAgIDxoMz5LZXkgSW5zaWdodHM6PC9oMz5cblxuICAgICAgPHA+PHN0cm9uZz4xLiBCdWlsZC1NZWFzdXJlLUxlYXJuPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+VGhlIGZ1bmRhbWVudGFsIGFjdGl2aXR5IG9mIGEgc3RhcnR1cCBpcyB0byB0dXJuIGlkZWFzIGludG8gcHJvZHVjdHMsIG1lYXN1cmUgaG93IGN1c3RvbWVycyByZXNwb25kLCBhbmQgdGhlbiBsZWFybiB3aGV0aGVyIHRvIHBpdm90IG9yIHBlcnNldmVyZS48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+Mi4gTWluaW11bSBWaWFibGUgUHJvZHVjdCAoTVZQKTwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPlRoZSB2ZXJzaW9uIG9mIGEgbmV3IHByb2R1Y3QgdGhhdCBhbGxvd3MgYSB0ZWFtIHRvIGNvbGxlY3QgdGhlIG1heGltdW0gYW1vdW50IG9mIHZhbGlkYXRlZCBsZWFybmluZyBhYm91dCBjdXN0b21lcnMgd2l0aCB0aGUgbGVhc3QgZWZmb3J0LjwvcD5cblxuICAgICAgPHA+PHN0cm9uZz4zLiBWYWxpZGF0ZWQgTGVhcm5pbmc8L3N0cm9uZz48L3A+XG4gICAgICA8cD5UaGUgcHJvY2VzcyBvZiBkZW1vbnN0cmF0aW5nIGVtcGlyaWNhbGx5IHRoYXQgYSB0ZWFtIGhhcyBkaXNjb3ZlcmVkIHZhbHVhYmxlIHRydXRocyBhYm91dCBhIHN0YXJ0dXAncyBwcmVzZW50IGFuZCBmdXR1cmUgYnVzaW5lc3MgcHJvc3BlY3RzLjwvcD5cblxuICAgICAgPHA+PHN0cm9uZz40LiBJbm5vdmF0aW9uIEFjY291bnRpbmc8L3N0cm9uZz48L3A+XG4gICAgICA8cD5BIHF1YW50aXRhdGl2ZSBhcHByb2FjaCB0aGF0IGFsbG93cyBzdGFydHVwcyB0byBwcm92ZSBvYmplY3RpdmVseSB0aGF0IHRoZXkgYXJlIGxlYXJuaW5nIGhvdyB0byBncm93IGEgc3VzdGFpbmFibGUgYnVzaW5lc3MuPC9wPlxuICAgIGAsXG4gICAga2V5SW5zaWdodHM6IGBcbiAgICAgIDxwPjxzdHJvbmc+MS4gQnVpbGQtTWVhc3VyZS1MZWFybjwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPlRoZSBmdW5kYW1lbnRhbCBhY3Rpdml0eSBvZiBhIHN0YXJ0dXAgaXMgdG8gdHVybiBpZGVhcyBpbnRvIHByb2R1Y3RzLCBtZWFzdXJlIGhvdyBjdXN0b21lcnMgcmVzcG9uZCwgYW5kIHRoZW4gbGVhcm4gd2hldGhlciB0byBwaXZvdCBvciBwZXJzZXZlcmUuPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjIuIE1pbmltdW0gVmlhYmxlIFByb2R1Y3QgKE1WUCk8L3N0cm9uZz48L3A+XG4gICAgICA8cD5UaGUgdmVyc2lvbiBvZiBhIG5ldyBwcm9kdWN0IHRoYXQgYWxsb3dzIGEgdGVhbSB0byBjb2xsZWN0IHRoZSBtYXhpbXVtIGFtb3VudCBvZiB2YWxpZGF0ZWQgbGVhcm5pbmcgYWJvdXQgY3VzdG9tZXJzIHdpdGggdGhlIGxlYXN0IGVmZm9ydC48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+My4gVmFsaWRhdGVkIExlYXJuaW5nPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+VGhlIHByb2Nlc3Mgb2YgZGVtb25zdHJhdGluZyBlbXBpcmljYWxseSB0aGF0IGEgdGVhbSBoYXMgZGlzY292ZXJlZCB2YWx1YWJsZSB0cnV0aHMgYWJvdXQgYSBzdGFydHVwJ3MgcHJlc2VudCBhbmQgZnV0dXJlIGJ1c2luZXNzIHByb3NwZWN0cy48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+NC4gSW5ub3ZhdGlvbiBBY2NvdW50aW5nPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+QSBxdWFudGl0YXRpdmUgYXBwcm9hY2ggdGhhdCBhbGxvd3Mgc3RhcnR1cHMgdG8gcHJvdmUgb2JqZWN0aXZlbHkgdGhhdCB0aGV5IGFyZSBsZWFybmluZyBob3cgdG8gZ3JvdyBhIHN1c3RhaW5hYmxlIGJ1c2luZXNzLjwvcD5cbiAgICBgLFxuICAgIGFwcGxpY2F0aW9uczogYFxuICAgICAgPHA+PHN0cm9uZz5Gb3IgRW50cmVwcmVuZXVyczo8L3N0cm9uZz48L3A+XG4gICAgICA8dWw+XG4gICAgICAgIDxsaT5TdGFydCB3aXRoIGEgbWluaW11bSB2aWFibGUgcHJvZHVjdCB0byB0ZXN0IGFzc3VtcHRpb25zIHF1aWNrbHk8L2xpPlxuICAgICAgICA8bGk+VXNlIGFjdGlvbmFibGUgbWV0cmljcyB0aGF0IGRlbW9uc3RyYXRlIGNsZWFyIGNhdXNlIGFuZCBlZmZlY3Q8L2xpPlxuICAgICAgICA8bGk+UHJhY3RpY2UgY29udGludW91cyBkZXBsb3ltZW50IGFuZCBzbWFsbCBiYXRjaCBzaXplczwvbGk+XG4gICAgICAgIDxsaT5CZSB3aWxsaW5nIHRvIHBpdm90IHdoZW4gbmVjZXNzYXJ5IGJhc2VkIG9uIHZhbGlkYXRlZCBsZWFybmluZzwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8cD48c3Ryb25nPkZvciBFc3RhYmxpc2hlZCBDb21wYW5pZXM6PC9zdHJvbmc+PC9wPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+Q3JlYXRlIGlubm92YXRpb24gdGVhbXMgd2l0aCBhcHByb3ByaWF0ZSBzdHJ1Y3R1cmVzIGFuZCBtZXRyaWNzPC9saT5cbiAgICAgICAgPGxpPkFsbG9jYXRlIHJlc291cmNlcyB1c2luZyBpbm5vdmF0aW9uIGFjY291bnRpbmc8L2xpPlxuICAgICAgICA8bGk+RGV2ZWxvcCBpbnRlcm5hbCBlbnRyZXByZW5ldXJzaGlwIHRocm91Z2ggZGVkaWNhdGVkIHRlYW1zPC9saT5cbiAgICAgICAgPGxpPkFwcGx5IGxlYW4gcHJpbmNpcGxlcyB0byBhY2NlbGVyYXRlIHByb2R1Y3QgZGV2ZWxvcG1lbnQgY3ljbGVzPC9saT5cbiAgICAgIDwvdWw+XG4gICAgYCxcbiAgICBpc1ByZW1pdW06IHRydWUsXG4gIH0sXG4gIC8vIEZyZWUgYm9va3NcbiAge1xuICAgIGlkOiBcInB1YmxpYy1kb21haW4tY2xhc3NpYy0xXCIsXG4gICAgdGl0bGU6IFwiVGhlIEFydCBvZiBXYXJcIixcbiAgICBhdXRob3I6IFwiU3VuIFR6dVwiLFxuICAgIGNvdmVyVXJsOiBcIi9wbGFjZWhvbGRlci5zdmc/aGVpZ2h0PTI0MCZ3aWR0aD0xODBcIixcbiAgICBjYXRlZ29yeTogXCJTdHJhdGVneVwiLFxuICAgIHJhdGluZzogNC44LFxuICAgIHBhZ2VzOiA5NixcbiAgICBsYW5ndWFnZTogXCJFbmdsaXNoXCIsXG4gICAgcHJpY2U6IDAsXG4gICAgc3VtbWFyeTogYFxuICAgICAgPHA+VGhlIEFydCBvZiBXYXIgaXMgYW4gYW5jaWVudCBDaGluZXNlIG1pbGl0YXJ5IHRyZWF0aXNlIGRhdGluZyBmcm9tIHRoZSBMYXRlIFNwcmluZyBhbmQgQXV0dW1uIFBlcmlvZC4gVGhlIHdvcmssIHdoaWNoIGlzIGF0dHJpYnV0ZWQgdG8gdGhlIGFuY2llbnQgQ2hpbmVzZSBtaWxpdGFyeSBzdHJhdGVnaXN0IFN1biBUenUsIGlzIGNvbXBvc2VkIG9mIDEzIGNoYXB0ZXJzLjwvcD5cblxuICAgICAgPGgzPktleSBJbnNpZ2h0czo8L2gzPlxuXG4gICAgICA8cD48c3Ryb25nPjEuIEtub3cgWW91cnNlbGYgYW5kIFlvdXIgRW5lbXk8L3N0cm9uZz48L3A+XG4gICAgICA8cD5JZiB5b3Uga25vdyB0aGUgZW5lbXkgYW5kIGtub3cgeW91cnNlbGYsIHlvdSBuZWVkIG5vdCBmZWFyIHRoZSByZXN1bHQgb2YgYSBodW5kcmVkIGJhdHRsZXMuPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjIuIFdpbiBXaXRob3V0IEZpZ2h0aW5nPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+VGhlIHN1cHJlbWUgZXhjZWxsZW5jZSBpcyB0byBzdWJkdWUgdGhlIGVuZW15IHdpdGhvdXQgZmlnaHRpbmcuPC9wPlxuXG4gICAgICA8cD48c3Ryb25nPjMuIFNwZWVkIGFuZCBUaW1pbmc8L3N0cm9uZz48L3A+XG4gICAgICA8cD5SYXBpZGl0eSBpcyB0aGUgZXNzZW5jZSBvZiB3YXI6IHRha2UgYWR2YW50YWdlIG9mIHRoZSBlbmVteSdzIHVucmVhZGluZXNzLCBtYWtlIHlvdXIgd2F5IGJ5IHVuZXhwZWN0ZWQgcm91dGVzLCBhbmQgYXR0YWNrIHVuZ3VhcmRlZCBzcG90cy48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+NC4gQWRhcHRhYmlsaXR5PC9zdHJvbmc+PC9wPlxuICAgICAgPHA+V2F0ZXIgc2hhcGVzIGl0cyBjb3Vyc2UgYWNjb3JkaW5nIHRvIHRoZSBuYXR1cmUgb2YgdGhlIGdyb3VuZDsgdGhlIHNvbGRpZXIgd29ya3Mgb3V0IGhpcyB2aWN0b3J5IGluIHJlbGF0aW9uIHRvIHRoZSBmb2UgaGUgaXMgZmFjaW5nLjwvcD5cbiAgICBgLFxuICAgIGlzUHJlbWl1bTogZmFsc2UsXG4gICAgaXNGcmVlOiB0cnVlLFxuICB9LFxuICB7XG4gICAgaWQ6IFwicHVibGljLWRvbWFpbi1jbGFzc2ljLTJcIixcbiAgICB0aXRsZTogXCJUaGluayBhbmQgR3JvdyBSaWNoXCIsXG4gICAgYXV0aG9yOiBcIk5hcG9sZW9uIEhpbGxcIixcbiAgICBjb3ZlclVybDogXCIvcGxhY2Vob2xkZXIuc3ZnP2hlaWdodD0yNDAmd2lkdGg9MTgwXCIsXG4gICAgY2F0ZWdvcnk6IFwiU2VsZi1IZWxwXCIsXG4gICAgcmF0aW5nOiA0LjcsXG4gICAgcGFnZXM6IDMyMCxcbiAgICBsYW5ndWFnZTogXCJFbmdsaXNoXCIsXG4gICAgcHJpY2U6IDAsXG4gICAgc3VtbWFyeTogYFxuICAgICAgPHA+VGhpbmsgYW5kIEdyb3cgUmljaCBpcyBhIHBlcnNvbmFsIGRldmVsb3BtZW50IGFuZCBzZWxmLWhlbHAgYm9vayBieSBOYXBvbGVvbiBIaWxsLiBUaGUgYm9vayB3YXMgaW5zcGlyZWQgYnkgYSBzdWdnZXN0aW9uIGZyb20gU2NvdHRpc2gtQW1lcmljYW4gYnVzaW5lc3NtYW4gQW5kcmV3IENhcm5lZ2llLjwvcD5cblxuICAgICAgPGgzPktleSBJbnNpZ2h0czo8L2gzPlxuXG4gICAgICA8cD48c3Ryb25nPjEuIERlc2lyZTwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPlRoZSBzdGFydGluZyBwb2ludCBvZiBhbGwgYWNoaWV2ZW1lbnQgaXMgZGVzaXJlLiBLZWVwIHRoaXMgY29uc3RhbnRseSBpbiBtaW5kLiBXZWFrIGRlc2lyZSBicmluZ3Mgd2VhayByZXN1bHRzLjwvcD5cblxuICAgICAgPHA+PHN0cm9uZz4yLiBGYWl0aDwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPkZhaXRoIGlzIHRoZSBoZWFkIGNoZW1pc3Qgb2YgdGhlIG1pbmQuIFdoZW4gZmFpdGggaXMgYmxlbmRlZCB3aXRoIHRoZSB2aWJyYXRpb24gb2YgdGhvdWdodCwgdGhlIHN1YmNvbnNjaW91cyBtaW5kIGluc3RhbnRseSBwaWNrcyB1cCB0aGUgdmlicmF0aW9uLjwvcD5cblxuICAgICAgPHA+PHN0cm9uZz4zLiBQZXJzaXN0ZW5jZTwvc3Ryb25nPjwvcD5cbiAgICAgIDxwPlBlcnNpc3RlbmNlIGlzIHRvIHRoZSBjaGFyYWN0ZXIgb2YgbWFuIGFzIGNhcmJvbiBpcyB0byBzdGVlbC48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+NC4gVGhlIE1hc3RlciBNaW5kPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+VGhlIGNvb3JkaW5hdGlvbiBvZiBrbm93bGVkZ2UgYW5kIGVmZm9ydCBvZiB0d28gb3IgbW9yZSBwZW9wbGUsIHdobyB3b3JrIHRvd2FyZCBhIGRlZmluaXRlIHB1cnBvc2UsIGluIHRoZSBzcGlyaXQgb2YgaGFybW9ueS48L3A+XG4gICAgYCxcbiAgICBpc1ByZW1pdW06IGZhbHNlLFxuICAgIGlzRnJlZTogdHJ1ZSxcbiAgfSxcbiAge1xuICAgIGlkOiBcImZyZWUtYnVzaW5lc3MtZ3VpZGVcIixcbiAgICB0aXRsZTogXCJTdGFydGluZyBZb3VyIEZpcnN0IEJ1c2luZXNzXCIsXG4gICAgYXV0aG9yOiBcIkFzdGV3YWkgVGVhbVwiLFxuICAgIGNvdmVyVXJsOiBcIi9wbGFjZWhvbGRlci5zdmc/aGVpZ2h0PTI0MCZ3aWR0aD0xODBcIixcbiAgICBjYXRlZ29yeTogXCJFbnRyZXByZW5ldXJzaGlwXCIsXG4gICAgcmF0aW5nOiA0LjUsXG4gICAgcGFnZXM6IDE1MCxcbiAgICBsYW5ndWFnZTogXCJFbmdsaXNoXCIsXG4gICAgcHJpY2U6IDAsXG4gICAgc3VtbWFyeTogYFxuICAgICAgPHA+QSBjb21wcmVoZW5zaXZlIGd1aWRlIGZvciBmaXJzdC10aW1lIGVudHJlcHJlbmV1cnMsIGNvdmVyaW5nIGV2ZXJ5dGhpbmcgZnJvbSBpZGVhIHZhbGlkYXRpb24gdG8gbGF1bmNoaW5nIHlvdXIgYnVzaW5lc3MuPC9wPlxuXG4gICAgICA8aDM+S2V5IEluc2lnaHRzOjwvaDM+XG5cbiAgICAgIDxwPjxzdHJvbmc+MS4gSWRlYSBWYWxpZGF0aW9uPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+QmVmb3JlIGludmVzdGluZyB0aW1lIGFuZCBtb25leSwgdmFsaWRhdGUgeW91ciBidXNpbmVzcyBpZGVhIHdpdGggcG90ZW50aWFsIGN1c3RvbWVycy48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+Mi4gTWFya2V0IFJlc2VhcmNoPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+VW5kZXJzdGFuZGluZyB5b3VyIHRhcmdldCBtYXJrZXQgaXMgY3J1Y2lhbCBmb3IgYnVzaW5lc3Mgc3VjY2Vzcy48L3A+XG5cbiAgICAgIDxwPjxzdHJvbmc+My4gRmluYW5jaWFsIFBsYW5uaW5nPC9zdHJvbmc+PC9wPlxuICAgICAgPHA+Q3JlYXRlIHJlYWxpc3RpYyBmaW5hbmNpYWwgcHJvamVjdGlvbnMgYW5kIHVuZGVyc3RhbmQgeW91ciBmdW5kaW5nIG5lZWRzLjwvcD5cblxuICAgICAgPHA+PHN0cm9uZz40LiBMZWdhbCBTdHJ1Y3R1cmU8L3N0cm9uZz48L3A+XG4gICAgICA8cD5DaG9vc2UgdGhlIHJpZ2h0IGJ1c2luZXNzIHN0cnVjdHVyZSBmb3IgeW91ciBzcGVjaWZpYyBzaXR1YXRpb24uPC9wPlxuICAgIGAsXG4gICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgICBpc0ZyZWU6IHRydWUsXG4gIH0sXG5dXG5cbi8vIENvbnZlcnQgdG8gQm9va1ByZXZpZXcgZm9ybWF0IGZvciBsaXN0aW5nIHBhZ2VzXG5leHBvcnQgY29uc3QgbW9ja0Jvb2tQcmV2aWV3czogQm9va1ByZXZpZXdbXSA9IG1vY2tCb29rcy5tYXAoYm9vayA9PiAoe1xuICBpZDogYm9vay5pZCxcbiAgdGl0bGU6IGJvb2sudGl0bGUsXG4gIGF1dGhvcjogYm9vay5hdXRob3IsXG4gIGNvdmVyVXJsOiBib29rLmNvdmVyVXJsLFxuICBjYXRlZ29yeTogYm9vay5jYXRlZ29yeSxcbiAgcmF0aW5nOiBib29rLnJhdGluZyxcbn0pKVxuXG4vLyBCdXNpbmVzcyBQbGFuIHR5cGVzIGFuZCBtb2NrIGRhdGFcbmV4cG9ydCBpbnRlcmZhY2UgQnVzaW5lc3NQbGFuIHtcbiAgaWQ6IHN0cmluZ1xuICB0aXRsZTogc3RyaW5nXG4gIGNhdGVnb3J5OiBzdHJpbmdcbiAgc2l6ZTogXCJzbWFsbFwiIHwgXCJtZWRpdW1cIiB8IFwibGFyZ2VcIlxuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIG92ZXJ2aWV3OiBzdHJpbmdcbiAgbWFya2V0QW5hbHlzaXM6IHN0cmluZ1xuICBmaW5hbmNpYWxzOiBzdHJpbmdcbiAgaW1wbGVtZW50YXRpb246IHN0cmluZ1xuICBpc1ByZW1pdW06IGJvb2xlYW5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBCdXNpbmVzc1BsYW5QcmV2aWV3IHtcbiAgaWQ6IHN0cmluZ1xuICB0aXRsZTogc3RyaW5nXG4gIGNhdGVnb3J5OiBzdHJpbmdcbiAgc2l6ZTogXCJzbWFsbFwiIHwgXCJtZWRpdW1cIiB8IFwibGFyZ2VcIlxuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIGlzUHJlbWl1bTogYm9vbGVhblxufVxuXG5leHBvcnQgY29uc3QgbW9ja0J1c2luZXNzUGxhbnM6IEJ1c2luZXNzUGxhbltdID0gW1xuICB7XG4gICAgaWQ6IFwic21hbGwtMVwiLFxuICAgIHRpdGxlOiBcIkxvY2FsIENvZmZlZSBTaG9wXCIsXG4gICAgY2F0ZWdvcnk6IFwiRm9vZCAmIEJldmVyYWdlXCIsXG4gICAgc2l6ZTogXCJzbWFsbFwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIkEgY29tcHJlaGVuc2l2ZSBidXNpbmVzcyBwbGFuIGZvciBzdGFydGluZyBhbmQgb3BlcmF0aW5nIGEgc3VjY2Vzc2Z1bCBsb2NhbCBjb2ZmZWUgc2hvcC5cIixcbiAgICBvdmVydmlldzogYFxuICAgICAgPGgzPkV4ZWN1dGl2ZSBTdW1tYXJ5PC9oMz5cbiAgICAgIDxwPlRoaXMgYnVzaW5lc3MgcGxhbiBvdXRsaW5lcyB0aGUgZXN0YWJsaXNobWVudCBvZiBhIGNvenksIGNvbW11bml0eS1mb2N1c2VkIGNvZmZlZSBzaG9wIHRoYXQgc2VydmVzIHByZW1pdW0gY29mZmVlIGFuZCBsaWdodCBmb29kIGl0ZW1zLiBUaGUgc2hvcCB3aWxsIGJlIGxvY2F0ZWQgaW4gYSBoaWdoLXRyYWZmaWMgYXJlYSB3aXRoIHNpZ25pZmljYW50IGZvb3QgdHJhZmZpYyBhbmQgbGltaXRlZCBkaXJlY3QgY29tcGV0aXRpb24uPC9wPlxuXG4gICAgICA8aDM+QnVzaW5lc3MgQ29uY2VwdDwvaDM+XG4gICAgICA8dWw+XG4gICAgICAgIDxsaT5QcmVtaXVtIGNvZmZlZSBhbmQgZXNwcmVzc28gZHJpbmtzPC9saT5cbiAgICAgICAgPGxpPkZyZXNoIHBhc3RyaWVzIGFuZCBsaWdodCBtZWFsczwvbGk+XG4gICAgICAgIDxsaT5Db21mb3J0YWJsZSBzZWF0aW5nIGFuZCBmcmVlIFdpLUZpPC9saT5cbiAgICAgICAgPGxpPkZvY3VzIG9uIHN1c3RhaW5hYmxlIHByYWN0aWNlczwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8aDM+VGFyZ2V0IE1hcmtldDwvaDM+XG4gICAgICA8dWw+XG4gICAgICAgIDxsaT5Zb3VuZyBwcm9mZXNzaW9uYWxzICgyNS00MCk8L2xpPlxuICAgICAgICA8bGk+Q29sbGVnZSBzdHVkZW50czwvbGk+XG4gICAgICAgIDxsaT5SZW1vdGUgd29ya2VyczwvbGk+XG4gICAgICAgIDxsaT5Mb2NhbCByZXNpZGVudHM8L2xpPlxuICAgICAgPC91bD5cbiAgICBgLFxuICAgIG1hcmtldEFuYWx5c2lzOiBgXG4gICAgICA8aDM+TWFya2V0IE92ZXJ2aWV3PC9oMz5cbiAgICAgIDxwPlRoZSBjb2ZmZWUgc2hvcCBpbmR1c3RyeSBjb250aW51ZXMgdG8gZ3Jvdywgd2l0aCBpbmNyZWFzaW5nIGRlbWFuZCBmb3IgcHJlbWl1bSBjb2ZmZWUgZXhwZXJpZW5jZXMuIEtleSBtYXJrZXQgdHJlbmRzIGluY2x1ZGU6PC9wPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+R3Jvd2luZyBwcmVmZXJlbmNlIGZvciBzcGVjaWFsdHkgY29mZmVlPC9saT5cbiAgICAgICAgPGxpPkluY3JlYXNlZCBmb2N1cyBvbiBzdXN0YWluYWJpbGl0eTwvbGk+XG4gICAgICAgIDxsaT5SaXNpbmcgZGVtYW5kIGZvciBwbGFudC1iYXNlZCBvcHRpb25zPC9saT5cbiAgICAgICAgPGxpPk5lZWQgZm9yIGNvbWZvcnRhYmxlIHdvcmtzcGFjZXM8L2xpPlxuICAgICAgPC91bD5cblxuICAgICAgPGgzPkNvbXBldGl0aXZlIEFuYWx5c2lzPC9oMz5cbiAgICAgIDxwPkxvY2FsIGNvbXBldGl0aW9uIGluY2x1ZGVzOjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPkNoYWluIGNvZmZlZSBzaG9wcyAoMiB3aXRoaW4gMWttKTwvbGk+XG4gICAgICAgIDxsaT5JbmRlcGVuZGVudCBjYWZlcyAoMSB3aXRoaW4gMWttKTwvbGk+XG4gICAgICAgIDxsaT5SZXN0YXVyYW50cyBzZXJ2aW5nIGNvZmZlZTwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8aDM+Q29tcGV0aXRpdmUgQWR2YW50YWdlPC9oMz5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPlByZW1pdW0gcXVhbGl0eSBjb2ZmZWU8L2xpPlxuICAgICAgICA8bGk+Q29tZm9ydGFibGUgYXRtb3NwaGVyZTwvbGk+XG4gICAgICAgIDxsaT5FeGNlbGxlbnQgY3VzdG9tZXIgc2VydmljZTwvbGk+XG4gICAgICAgIDxsaT5TdHJhdGVnaWMgbG9jYXRpb248L2xpPlxuICAgICAgPC91bD5cbiAgICBgLFxuICAgIGZpbmFuY2lhbHM6IGBcbiAgICAgIDxoMz5TdGFydHVwIENvc3RzPC9oMz5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPkxlYXNlIGRlcG9zaXQgYW5kIGltcHJvdmVtZW50czogJDI1LDAwMDwvbGk+XG4gICAgICAgIDxsaT5FcXVpcG1lbnQ6ICQzNSwwMDA8L2xpPlxuICAgICAgICA8bGk+SW5pdGlhbCBpbnZlbnRvcnk6ICQ1LDAwMDwvbGk+XG4gICAgICAgIDxsaT5MaWNlbnNlcyBhbmQgcGVybWl0czogJDIsMDAwPC9saT5cbiAgICAgICAgPGxpPldvcmtpbmcgY2FwaXRhbDogJDIwLDAwMDwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8aDM+RmluYW5jaWFsIFByb2plY3Rpb25zPC9oMz5cbiAgICAgIDxwPlllYXIgMTo8L3A+XG4gICAgICA8dWw+XG4gICAgICAgIDxsaT5SZXZlbnVlOiAkMzAwLDAwMDwvbGk+XG4gICAgICAgIDxsaT5FeHBlbnNlczogJDI3MCwwMDA8L2xpPlxuICAgICAgICA8bGk+TmV0IHByb2ZpdDogJDMwLDAwMDwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8cD5ZZWFyIDI6PC9wPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+UmV2ZW51ZTogJDQwMCwwMDA8L2xpPlxuICAgICAgICA8bGk+RXhwZW5zZXM6ICQzNDAsMDAwPC9saT5cbiAgICAgICAgPGxpPk5ldCBwcm9maXQ6ICQ2MCwwMDA8L2xpPlxuICAgICAgPC91bD5cbiAgICBgLFxuICAgIGltcGxlbWVudGF0aW9uOiBgXG4gICAgICA8aDM+VGltZWxpbmU8L2gzPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+TW9udGggMS0yOiBMb2NhdGlvbiBzZWxlY3Rpb24gYW5kIGxlYXNlIHNpZ25pbmc8L2xpPlxuICAgICAgICA8bGk+TW9udGggMi0zOiBEZXNpZ24gYW5kIHBlcm1pdHM8L2xpPlxuICAgICAgICA8bGk+TW9udGggMy00OiBDb25zdHJ1Y3Rpb24gYW5kIGVxdWlwbWVudCBpbnN0YWxsYXRpb248L2xpPlxuICAgICAgICA8bGk+TW9udGggNDogU3RhZmYgaGlyaW5nIGFuZCB0cmFpbmluZzwvbGk+XG4gICAgICAgIDxsaT5Nb250aCA1OiBTb2Z0IG9wZW5pbmcgYW5kIG1hcmtldGluZzwvbGk+XG4gICAgICAgIDxsaT5Nb250aCA2OiBHcmFuZCBvcGVuaW5nPC9saT5cbiAgICAgIDwvdWw+XG5cbiAgICAgIDxoMz5NYXJrZXRpbmcgU3RyYXRlZ3k8L2gzPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+U29jaWFsIG1lZGlhIHByZXNlbmNlPC9saT5cbiAgICAgICAgPGxpPkxvY2FsIHBhcnRuZXJzaGlwczwvbGk+XG4gICAgICAgIDxsaT5Mb3lhbHR5IHByb2dyYW08L2xpPlxuICAgICAgICA8bGk+Q29tbXVuaXR5IGV2ZW50czwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8aDM+UmlzayBNaXRpZ2F0aW9uPC9oMz5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPkNvbXByZWhlbnNpdmUgaW5zdXJhbmNlIGNvdmVyYWdlPC9saT5cbiAgICAgICAgPGxpPkRpdmVyc2Ugc3VwcGxpZXIgcmVsYXRpb25zaGlwczwvbGk+XG4gICAgICAgIDxsaT5TdGFmZiB0cmFpbmluZyBwcm9ncmFtczwvbGk+XG4gICAgICAgIDxsaT5DYXNoIGZsb3cgbWFuYWdlbWVudDwvbGk+XG4gICAgICA8L3VsPlxuICAgIGAsXG4gICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgfSxcbiAge1xuICAgIGlkOiBcInNtYWxsLTJcIixcbiAgICB0aXRsZTogXCJGcmVlbGFuY2UgV2ViIERldmVsb3BtZW50XCIsXG4gICAgY2F0ZWdvcnk6IFwiVGVjaG5vbG9neVwiLFxuICAgIHNpemU6IFwic21hbGxcIixcbiAgICBkZXNjcmlwdGlvbjogXCJBIGRldGFpbGVkIGJ1c2luZXNzIHBsYW4gZm9yIHN0YXJ0aW5nIGFuZCBncm93aW5nIGEgZnJlZWxhbmNlIHdlYiBkZXZlbG9wbWVudCBidXNpbmVzcy5cIixcbiAgICBvdmVydmlldzogYFxuICAgICAgPGgzPkV4ZWN1dGl2ZSBTdW1tYXJ5PC9oMz5cbiAgICAgIDxwPlRoaXMgYnVzaW5lc3MgcGxhbiBvdXRsaW5lcyB0aGUgZXN0YWJsaXNobWVudCBvZiBhIGZyZWVsYW5jZSB3ZWIgZGV2ZWxvcG1lbnQgYnVzaW5lc3MgZm9jdXNlZCBvbiBjcmVhdGluZyBjdXN0b20gd2Vic2l0ZXMgYW5kIHdlYiBhcHBsaWNhdGlvbnMgZm9yIHNtYWxsIHRvIG1lZGl1bS1zaXplZCBidXNpbmVzc2VzLiBUaGUgYnVzaW5lc3Mgd2lsbCBvcGVyYXRlIHJlbW90ZWx5IHdpdGggbWluaW1hbCBvdmVyaGVhZCBjb3N0cy48L3A+XG5cbiAgICAgIDxoMz5CdXNpbmVzcyBDb25jZXB0PC9oMz5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPkN1c3RvbSB3ZWJzaXRlIGRldmVsb3BtZW50PC9saT5cbiAgICAgICAgPGxpPldlYiBhcHBsaWNhdGlvbiBkZXZlbG9wbWVudDwvbGk+XG4gICAgICAgIDxsaT5XZWJzaXRlIG1haW50ZW5hbmNlIGFuZCBzdXBwb3J0PC9saT5cbiAgICAgICAgPGxpPlNFTyBhbmQgZGlnaXRhbCBtYXJrZXRpbmcgc2VydmljZXM8L2xpPlxuICAgICAgPC91bD5cblxuICAgICAgPGgzPlRhcmdldCBNYXJrZXQ8L2gzPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+U21hbGwgYnVzaW5lc3NlcyBuZWVkaW5nIG9ubGluZSBwcmVzZW5jZTwvbGk+XG4gICAgICAgIDxsaT5NZWRpdW0tc2l6ZWQgY29tcGFuaWVzIHJlcXVpcmluZyB3ZWIgYXBwbGljYXRpb25zPC9saT5cbiAgICAgICAgPGxpPlN0YXJ0dXBzIHdpdGggbGltaXRlZCBidWRnZXRzPC9saT5cbiAgICAgICAgPGxpPk5vbi1wcm9maXQgb3JnYW5pemF0aW9uczwvbGk+XG4gICAgICA8L3VsPlxuICAgIGAsXG4gICAgbWFya2V0QW5hbHlzaXM6IGBcbiAgICAgIDxoMz5NYXJrZXQgT3ZlcnZpZXc8L2gzPlxuICAgICAgPHA+VGhlIHdlYiBkZXZlbG9wbWVudCBpbmR1c3RyeSBjb250aW51ZXMgdG8gZ3JvdyBhcyBidXNpbmVzc2VzIGluY3JlYXNpbmdseSByZWNvZ25pemUgdGhlIGltcG9ydGFuY2Ugb2Ygb25saW5lIHByZXNlbmNlLiBLZXkgbWFya2V0IHRyZW5kcyBpbmNsdWRlOjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPkluY3JlYXNpbmcgZGVtYW5kIGZvciBtb2JpbGUtcmVzcG9uc2l2ZSB3ZWJzaXRlczwvbGk+XG4gICAgICAgIDxsaT5Hcm93aW5nIG5lZWQgZm9yIGUtY29tbWVyY2UgZnVuY3Rpb25hbGl0eTwvbGk+XG4gICAgICAgIDxsaT5SaXNpbmcgaW1wb3J0YW5jZSBvZiB1c2VyIGV4cGVyaWVuY2UgKFVYKSBkZXNpZ248L2xpPlxuICAgICAgICA8bGk+U2hpZnQgdG93YXJkIHByb2dyZXNzaXZlIHdlYiBhcHBsaWNhdGlvbnM8L2xpPlxuICAgICAgPC91bD5cblxuICAgICAgPGgzPkNvbXBldGl0aXZlIEFuYWx5c2lzPC9oMz5cbiAgICAgIDxwPkNvbXBldGl0aW9uIGluY2x1ZGVzOjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPk90aGVyIGZyZWVsYW5jZSBkZXZlbG9wZXJzPC9saT5cbiAgICAgICAgPGxpPldlYiBkZXZlbG9wbWVudCBhZ2VuY2llczwvbGk+XG4gICAgICAgIDxsaT5ESVkgd2Vic2l0ZSBidWlsZGVycyAoV2l4LCBTcXVhcmVzcGFjZSk8L2xpPlxuICAgICAgPC91bD5cblxuICAgICAgPGgzPkNvbXBldGl0aXZlIEFkdmFudGFnZTwvaDM+XG4gICAgICA8dWw+XG4gICAgICAgIDxsaT5QZXJzb25hbGl6ZWQgc2VydmljZSBhbmQgZGlyZWN0IGNsaWVudCBjb21tdW5pY2F0aW9uPC9saT5cbiAgICAgICAgPGxpPkxvd2VyIG92ZXJoZWFkIGNvc3RzIHRoYW4gYWdlbmNpZXM8L2xpPlxuICAgICAgICA8bGk+U3BlY2lhbGl6ZWQgZXhwZXJ0aXNlIGluIG1vZGVybiBmcmFtZXdvcmtzPC9saT5cbiAgICAgICAgPGxpPkZsZXhpYmxlIHByaWNpbmcgbW9kZWxzPC9saT5cbiAgICAgIDwvdWw+XG4gICAgYCxcbiAgICBmaW5hbmNpYWxzOiBgXG4gICAgICA8aDM+U3RhcnR1cCBDb3N0czwvaDM+XG4gICAgICA8dWw+XG4gICAgICAgIDxsaT5Db21wdXRlciBlcXVpcG1lbnQ6ICQzLDAwMDwvbGk+XG4gICAgICAgIDxsaT5Tb2Z0d2FyZSBzdWJzY3JpcHRpb25zOiAkMSwyMDAveWVhcjwvbGk+XG4gICAgICAgIDxsaT5XZWJzaXRlIGFuZCBob3N0aW5nOiAkNTAwPC9saT5cbiAgICAgICAgPGxpPkJ1c2luZXNzIHJlZ2lzdHJhdGlvbjogJDMwMDwvbGk+XG4gICAgICAgIDxsaT5Jbml0aWFsIG1hcmtldGluZzogJDEsMDAwPC9saT5cbiAgICAgIDwvdWw+XG5cbiAgICAgIDxoMz5GaW5hbmNpYWwgUHJvamVjdGlvbnM8L2gzPlxuICAgICAgPHA+WWVhciAxOjwvcD5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPlJldmVudWU6ICQ2MCwwMDA8L2xpPlxuICAgICAgICA8bGk+RXhwZW5zZXM6ICQxNSwwMDA8L2xpPlxuICAgICAgICA8bGk+TmV0IHByb2ZpdDogJDQ1LDAwMDwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8cD5ZZWFyIDI6PC9wPlxuICAgICAgPHVsPlxuICAgICAgICA8bGk+UmV2ZW51ZTogJDkwLDAwMDwvbGk+XG4gICAgICAgIDxsaT5FeHBlbnNlczogJDIwLDAwMDwvbGk+XG4gICAgICAgIDxsaT5OZXQgcHJvZml0OiAkNzAsMDAwPC9saT5cbiAgICAgIDwvdWw+XG4gICAgYCxcbiAgICBpbXBsZW1lbnRhdGlvbjogYFxuICAgICAgPGgzPlRpbWVsaW5lPC9oMz5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPk1vbnRoIDE6IEJ1c2luZXNzIHJlZ2lzdHJhdGlvbiBhbmQgd2Vic2l0ZSBzZXR1cDwvbGk+XG4gICAgICAgIDxsaT5Nb250aCAyOiBQb3J0Zm9saW8gZGV2ZWxvcG1lbnQ8L2xpPlxuICAgICAgICA8bGk+TW9udGggMzogSW5pdGlhbCBtYXJrZXRpbmcgYW5kIG5ldHdvcmtpbmc8L2xpPlxuICAgICAgICA8bGk+TW9udGggNC02OiBTZWN1cmUgZmlyc3QgY2xpZW50cyBhbmQgYnVpbGQgcmVwdXRhdGlvbjwvbGk+XG4gICAgICA8L3VsPlxuXG4gICAgICA8aDM+TWFya2V0aW5nIFN0cmF0ZWd5PC9oMz5cbiAgICAgIDx1bD5cbiAgICAgICAgPGxpPlBvcnRmb2xpbyB3ZWJzaXRlIHNob3djYXNpbmcgd29yazwvbGk+XG4gICAgICAgIDxsaT5Tb2NpYWwgbWVkaWEgcHJlc2VuY2Ugb24gTGlua2VkSW4gYW5kIFR3aXR0ZXI8L2xpPlxuICAgICAgICA8bGk+Q29udGVudCBtYXJrZXRpbmcgdGhyb3VnaCBibG9nIHBvc3RzPC9saT5cbiAgICAgICAgPGxpPk5ldHdvcmtpbmcgYXQgbG9jYWwgYnVzaW5lc3MgZXZlbnRzPC9saT5cbiAgICAgICAgPGxpPlJlZmVycmFsIHByb2dyYW0gZm9yIGV4aXN0aW5nIGNsaWVudHM8L2xpPlxuICAgICAgPC91bD5cblxuICAgICAgPGgzPlJpc2sgTWl0aWdhdGlvbjwvaDM+XG4gICAgICA8dWw+XG4gICAgICAgIDxsaT5EaXZlcnNpZnkgY2xpZW50IGJhc2UgdG8gYXZvaWQgZGVwZW5kZW5jeTwvbGk+XG4gICAgICAgIDxsaT5NYWludGFpbiBlbWVyZ2VuY3kgZnVuZCBmb3Igc2xvdyBwZXJpb2RzPC9saT5cbiAgICAgICAgPGxpPkNvbnRpbnVvdXMgc2tpbGwgZGV2ZWxvcG1lbnQ8L2xpPlxuICAgICAgICA8bGk+Q2xlYXIgY29udHJhY3RzIGFuZCBzY29wZSBkZWZpbml0aW9uczwvbGk+XG4gICAgICA8L3VsPlxuICAgIGAsXG4gICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgfSxcbl1cblxuZXhwb3J0IGNvbnN0IG1vY2tCdXNpbmVzc1BsYW5QcmV2aWV3czogQnVzaW5lc3NQbGFuUHJldmlld1tdID0gbW9ja0J1c2luZXNzUGxhbnMubWFwKHBsYW4gPT4gKHtcbiAgaWQ6IHBsYW4uaWQsXG4gIHRpdGxlOiBwbGFuLnRpdGxlLFxuICBjYXRlZ29yeTogcGxhbi5jYXRlZ29yeSxcbiAgc2l6ZTogcGxhbi5zaXplLFxuICBkZXNjcmlwdGlvbjogcGxhbi5kZXNjcmlwdGlvbixcbiAgaXNQcmVtaXVtOiBwbGFuLmlzUHJlbWl1bSxcbn0pKVxuIl0sIm5hbWVzIjpbIm1vY2tCb29rcyIsImlkIiwidGl0bGUiLCJhdXRob3IiLCJjb3ZlclVybCIsImNhdGVnb3J5IiwicmF0aW5nIiwicGFnZXMiLCJsYW5ndWFnZSIsInByaWNlIiwic3VtbWFyeSIsImtleUluc2lnaHRzIiwiYXBwbGljYXRpb25zIiwiaXNQcmVtaXVtIiwiaXNGcmVlIiwibW9ja0Jvb2tQcmV2aWV3cyIsIm1hcCIsImJvb2siLCJtb2NrQnVzaW5lc3NQbGFucyIsInNpemUiLCJkZXNjcmlwdGlvbiIsIm92ZXJ2aWV3IiwibWFya2V0QW5hbHlzaXMiLCJmaW5hbmNpYWxzIiwiaW1wbGVtZW50YXRpb24iLCJtb2NrQnVzaW5lc3NQbGFuUHJldmlld3MiLCJwbGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-data.ts\n"));

/***/ })

});