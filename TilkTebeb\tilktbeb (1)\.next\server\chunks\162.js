"use strict";exports.id=162,exports.ids=[162],exports.modules={86235:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41680).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55613:(e,t,r)=>{r.d(t,{B8:()=>D,UC:()=>I,bL:()=>k,l9:()=>M});var n=r(58009),o=r(31412),a=r(6004),i=r(48305),l=r(98060),s=r(30830),u=r(59018),c=r(13024),d=r(30096),p=r(45512),f="Tabs",[x,h]=(0,a.A)(f,[i.RG]),v=(0,i.RG)(),[y,b]=x(f),g=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:x="automatic",...h}=e,v=(0,u.jH)(l),[b,g]=(0,c.i)({prop:n,onChange:o,defaultProp:a??"",caller:f});return(0,p.jsx)(y,{scope:r,baseId:(0,d.B)(),value:b,onValueChange:g,orientation:i,dir:v,activationMode:x,children:(0,p.jsx)(s.sG.div,{dir:v,"data-orientation":i,...h,ref:t})})});g.displayName=f;var m="TabsList",w=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=b(m,r),l=v(r);return(0,p.jsx)(i.bL,{asChild:!0,...l,orientation:a.orientation,dir:a.dir,loop:n,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});w.displayName=m;var C="TabsTrigger",T=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...l}=e,u=b(C,r),c=v(r),d=j(u.baseId,n),f=L(u.baseId,n),x=n===u.value;return(0,p.jsx)(i.q7,{asChild:!0,...c,focusable:!a,active:x,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":f,"data-state":x?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...l,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;x||a||!e||u.onValueChange(n)})})})});T.displayName=C;var E="TabsContent",R=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:i,...u}=e,c=b(E,r),d=j(c.baseId,o),f=L(c.baseId,o),x=o===c.value,h=n.useRef(x);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(l.C,{present:a||x,children:({present:r})=>(0,p.jsx)(s.sG.div,{"data-state":x?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:f,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&i})})});function j(e,t){return`${e}-trigger-${t}`}function L(e,t){return`${e}-content-${t}`}R.displayName=E;var k=g,D=w,M=T,I=R},24808:(e,t,r)=>{r.d(t,{Kq:()=>q,UC:()=>V,bL:()=>H,l9:()=>K});var n=r(58009),o=r(31412),a=r(29952),i=r(6004),l=r(41675),s=r(30096),u=r(53337),c=(r(80707),r(98060)),d=r(30830),p=r(12705),f=r(13024),x=r(56441),h=r(45512),[v,y]=(0,i.A)("Tooltip",[u.Bk]),b=(0,u.Bk)(),g="TooltipProvider",m="tooltip.open",[w,C]=v(g),T=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:a=!1,children:i}=e,l=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(w,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),l.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:a,children:i})};T.displayName=g;var E="Tooltip",[R,j]=v(E),L=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:a,onOpenChange:i,disableHoverableContent:l,delayDuration:c}=e,d=C(E,e.__scopeTooltip),p=b(t),[x,v]=n.useState(null),y=(0,s.B)(),g=n.useRef(0),w=l??d.disableHoverableContent,T=c??d.delayDuration,j=n.useRef(!1),[L,k]=(0,f.i)({prop:o,defaultProp:a??!1,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(m))):d.onClose(),i?.(e)},caller:E}),D=n.useMemo(()=>L?j.current?"delayed-open":"instant-open":"closed",[L]),M=n.useCallback(()=>{window.clearTimeout(g.current),g.current=0,j.current=!1,k(!0)},[k]),I=n.useCallback(()=>{window.clearTimeout(g.current),g.current=0,k(!1)},[k]),P=n.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{j.current=!0,k(!0),g.current=0},T)},[T,k]);return n.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,h.jsx)(u.bL,{...p,children:(0,h.jsx)(R,{scope:t,contentId:y,open:L,stateAttribute:D,trigger:x,onTriggerChange:v,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?P():M()},[d.isOpenDelayedRef,P,M]),onTriggerLeave:n.useCallback(()=>{w?I():(window.clearTimeout(g.current),g.current=0)},[I,w]),onOpen:M,onClose:I,disableHoverableContent:w,children:r})})};L.displayName=E;var k="TooltipTrigger",D=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,l=j(k,r),s=C(k,r),c=b(r),p=n.useRef(null),f=(0,a.s)(t,p,l.onTriggerChange),x=n.useRef(!1),v=n.useRef(!1),y=n.useCallback(()=>x.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,h.jsx)(u.Mz,{asChild:!0,...c,children:(0,h.jsx)(d.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...i,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{l.open&&l.onClose(),x.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{x.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});D.displayName=k;var[M,I]=v("TooltipPortal",{forceMount:void 0}),P="TooltipContent",_=n.forwardRef((e,t)=>{let r=I(P,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...a}=e,i=j(P,e.__scopeTooltip);return(0,h.jsx)(c.C,{present:n||i.open,children:i.disableHoverableContent?(0,h.jsx)(F,{side:o,...a,ref:t}):(0,h.jsx)(A,{side:o,...a,ref:t})})}),A=n.forwardRef((e,t)=>{let r=j(P,e.__scopeTooltip),o=C(P,e.__scopeTooltip),i=n.useRef(null),l=(0,a.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=i.current,{onPointerInTransitChange:f}=o,x=n.useCallback(()=>{u(null),f(!1)},[f]),v=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>x(),[x]),n.useEffect(()=>{if(c&&p){let e=e=>v(e,p),t=e=>v(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,v,x]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||p?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],s=i.x,u=i.y,c=l.x,d=l.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?x():o&&(x(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,x]),(0,h.jsx)(F,{...e,ref:l})}),[B,N]=v(E,{isInside:!1}),O=(0,p.Dc)("TooltipContent"),F=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":a,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,d=j(P,r),p=b(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(m,f),()=>document.removeEventListener(m,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,h.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,h.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(O,{children:o}),(0,h.jsx)(B,{scope:r,isInside:!0,children:(0,h.jsx)(x.bL,{id:d.contentId,role:"tooltip",children:a||o})})]})})});_.displayName=P;var G="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=b(r);return N(G,r).isInside?null:(0,h.jsx)(u.i3,{...o,...n,ref:t})}).displayName=G;var q=T,H=L,K=D,V=_}};