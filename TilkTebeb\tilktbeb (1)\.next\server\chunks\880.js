"use strict";exports.id=880,exports.ids=[880],exports.modules={87103:(e,t,a)=>{a.d(t,{t1:()=>y,at:()=>m,hx:()=>u,II:()=>h});var s=a(45512),i=a(58009),r=a(24808),n=a(59462);let o=r.Kq,l=r.bL,c=r.l9,d=i.forwardRef(({className:e,sideOffset:t=4,...a},i)=>(0,s.jsx)(r.UC,{ref:i,sideOffset:t,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}));function m({children:e}){return(0,s.jsx)("div",{className:"rounded-md border",children:e})}function u({children:e}){return(0,s.jsx)("h3",{className:"text-lg font-semibold",children:e})}function y({children:e}){return(0,s.jsx)("div",{children:e})}function h({children:e}){return(0,s.jsx)(o,{children:(0,s.jsxs)(l,{children:[(0,s.jsx)(c,{asChild:!0,children:e}),(0,s.jsx)(d,{children:e})]})})}d.displayName=r.UC.displayName},69193:(e,t,a)=>{a.d(t,{Tabs:()=>o,TabsContent:()=>d,TabsList:()=>l,TabsTrigger:()=>c});var s=a(45512),i=a(58009),r=a(55613),n=a(59462);let o=r.bL,l=i.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.B8,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=r.B8.displayName;let c=i.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.l9,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));c.displayName=r.l9.displayName;let d=i.forwardRef(({className:e,...t},a)=>(0,s.jsx)(r.UC,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));d.displayName=r.UC.displayName},97730:(e,t,a)=>{a.d(t,{FH:()=>o,lj:()=>c,hS:()=>l,Pr:()=>d}),a(58009);let s=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:8000/api";class i{static{this.baseUrl=s}static async fetchWithErrorHandling(e,t={}){let a=`${this.baseUrl}${e}`;try{let e=await fetch(a,{headers:{"Content-Type":"application/json",...t.headers},...t});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(t){throw console.error(`API call failed for ${e}:`,t),t}}static async getBooks(e){let t=new URLSearchParams;e?.category&&t.append("category",e.category),e?.query&&t.append("query",e.query),e?.premium!==void 0&&t.append("premium",e.premium.toString()),e?.limit&&t.append("limit",e.limit.toString());let a=`/books/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(a)}static async getBookById(e){return this.fetchWithErrorHandling(`/books/${e}/`)}static async getBusinessPlans(e){let t=new URLSearchParams;e?.size&&t.append("size",e.size),e?.category&&t.append("category",e.category),e?.query&&t.append("query",e.query),e?.premium!==void 0&&t.append("premium",e.premium.toString());let a=`/business-plans/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(a)}static async getBusinessPlanById(e){return this.fetchWithErrorHandling(`/business-plans/${e}/`)}static async login(e){return this.fetchWithErrorHandling("/auth/login/",{method:"POST",body:JSON.stringify(e)})}static async register(e){return this.fetchWithErrorHandling("/auth/register/",{method:"POST",body:JSON.stringify(e)})}static async getUserProfile(e){return this.fetchWithErrorHandling(`/user/${e}/`)}static async updateUserProfile(e,t){return this.fetchWithErrorHandling(`/user/${e}/`,{method:"PATCH",body:JSON.stringify(t)})}static async processPayment(e){return this.fetchWithErrorHandling("/payments/",{method:"POST",body:JSON.stringify(e)})}static async verifyPayment(e){return this.fetchWithErrorHandling(`/payments/verify/${e}/`)}static async getPaymentHistory(e){return this.fetchWithErrorHandling(`/payments/?userId=${e}`)}static async getAdminStats(){return this.fetchWithErrorHandling("/admin/stats/")}static async getAdminUsers(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search);let a=`/admin/users/${t.toString()?`?${t.toString()}`:""}`;return this.fetchWithErrorHandling(a)}static async getUserBookmarks(e){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/`)}static async addBookmark(e,t){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/`,{method:"POST",body:JSON.stringify({bookId:t})})}static async removeBookmark(e,t){return this.fetchWithErrorHandling(`/user/${e}/bookmarks/${t}/`,{method:"DELETE"})}static async getUserAnalytics(e){return this.fetchWithErrorHandling(`/user/${e}/analytics/`)}}var r=a(12362);class n{static delay(e=500){return new Promise(t=>setTimeout(t,e))}static async getBooks(e){await this.delay();let t=[...r.tn];if(e?.category&&"all"!==e.category&&(t=t.filter(t=>t.category.toLowerCase()===e.category.toLowerCase())),e?.query){let a=e.query.toLowerCase();t=t.filter(e=>e.title.toLowerCase().includes(a)||e.author.toLowerCase().includes(a))}if(e?.premium!==void 0){let a=r.hr.filter(t=>t.isPremium===e.premium).map(e=>e.id);t=t.filter(e=>a.includes(e.id))}return e?.limit&&(t=t.slice(0,e.limit)),t}static async getBookById(e){await this.delay();let t=r.hr.find(t=>t.id===e);if(!t)throw Error(`Book with id ${e} not found`);return t}static async getBusinessPlans(e){await this.delay();let t=[...r.RJ];if(e?.size&&"all"!==e.size&&(t=t.filter(t=>t.size===e.size)),e?.category&&"all"!==e.category&&(t=t.filter(t=>t.category.toLowerCase()===e.category.toLowerCase())),e?.query){let a=e.query.toLowerCase();t=t.filter(e=>e.title.toLowerCase().includes(a)||e.category.toLowerCase().includes(a))}return e?.premium!==void 0&&(t=t.filter(t=>t.isPremium===e.premium)),t}static async getBusinessPlanById(e){await this.delay();let t=r.Z9.find(t=>t.id===e);if(!t)throw Error(`Business plan with id ${e} not found`);return t}static async login(e){return await this.delay(),{id:"user-1",firstName:"John",lastName:"Doe",email:e.email,plan:"medium",token:"mock-jwt-token-123"}}static async register(e){return await this.delay(),{id:`user-${Date.now()}`,firstName:e.firstName,lastName:e.lastName,email:e.email,plan:"base",token:"mock-jwt-token-456"}}static async getUserProfile(e){return await this.delay(),{user:{id:e,firstName:"John",lastName:"Doe",email:"<EMAIL>",plan:"medium"},activities:[{id:"activity-1",userId:e,type:"book_read",itemId:"the-psychology-of-money",itemTitle:"The Psychology of Money",timestamp:new Date(Date.now()-864e5).toISOString()},{id:"activity-2",userId:e,type:"plan_viewed",itemId:"small-1",itemTitle:"Local Coffee Shop",timestamp:new Date(Date.now()-1728e5).toISOString()}]}}static async updateUserProfile(e,t){return await this.delay(),{id:e,firstName:t.firstName||"John",lastName:t.lastName||"Doe",email:t.email||"<EMAIL>",plan:"medium"}}static async processPayment(e){return await this.delay(1e3),{success:!0,transactionId:`TRX-${Date.now()}-${Math.floor(1e3*Math.random())}`,message:"Payment processed successfully",timestamp:new Date().toISOString(),plan:e.plan}}static async verifyPayment(e){return await this.delay(),{success:!0,status:"completed",message:"Payment verified successfully"}}static async getPaymentHistory(e){return await this.delay(),[{success:!0,transactionId:"TRX-123456789",message:"Payment processed successfully",timestamp:new Date(Date.now()-2592e6).toISOString(),plan:"medium"}]}static async getAdminStats(){return await this.delay(),{totalUsers:1250,totalBooks:r.hr.length,totalBusinessPlans:r.Z9.length,revenueThisMonth:15750}}static async getAdminUsers(e){await this.delay();let t=[{id:"user-1",firstName:"John",lastName:"Doe",email:"<EMAIL>",plan:"medium",createdAt:"2023-01-15T00:00:00Z"},{id:"user-2",firstName:"Jane",lastName:"Smith",email:"<EMAIL>",plan:"base",createdAt:"2023-03-22T00:00:00Z"}];return{users:t,total:t.length,page:e?.page||1,totalPages:1}}static async getUserBookmarks(e){return await this.delay(),r.tn.slice(0,2)}static async addBookmark(e,t){return await this.delay(),{success:!0}}static async removeBookmark(e,t){return await this.delay(),{success:!0}}static async getUserAnalytics(e){return await this.delay(),{readingStats:{booksRead:12,totalReadingTime:2400,averageReadingSpeed:250,streakDays:7},recentActivity:[{id:"activity-1",type:"book_read",itemTitle:"The Psychology of Money",timestamp:new Date(Date.now()-864e5).toISOString()},{id:"activity-2",type:"plan_viewed",itemTitle:"Local Coffee Shop",timestamp:new Date(Date.now()-1728e5).toISOString()}]}}}let o="true"===process.env.NEXT_PUBLIC_USE_MOCK_API?n:i,l=e=>e instanceof Error?e.message:"An unexpected error occurred";process.env.NEXT_PUBLIC_API_BASE_URL;let c={getToken:()=>null,setToken:e=>{},removeToken:()=>{},isAuthenticated:()=>!!c.getToken()},d={getCurrentUser:()=>null,setCurrentUser:e=>{},removeCurrentUser:()=>{},logout:()=>{c.removeToken(),d.removeCurrentUser()}}}};