"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/books/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/book-open.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookOpen)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst BookOpen = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"BookOpen\", [\n    [\n        \"path\",\n        {\n            d: \"M12 7v14\",\n            key: \"1akyts\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\",\n            key: \"ruj8y\"\n        }\n    ]\n]);\n //# sourceMappingURL=book-open.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/book-card.tsx":
/*!**********************************!*\
  !*** ./components/book-card.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookCard: () => (/* binding */ BookCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ BookCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction BookCard(param) {\n    let { id, title, author, coverUrl, category, rating, price = 9.99, isPurchased = false, isFree = false } = param;\n    _s();\n    const [showPurchaseModal, setShowPurchaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const book = {\n        id,\n        title,\n        author,\n        coverUrl,\n        category,\n        rating,\n        price,\n        isPurchased,\n        isFree,\n        pages: 300,\n        language: \"English\",\n        summary: \"<p>Discover the insights and strategies that make this book a must-read in the \".concat(category, \" category.</p>\")\n    };\n    const isBookFree = isFree || price === 0;\n    const handlePurchaseClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setShowPurchaseModal(true);\n    };\n    const handlePurchaseComplete = ()=>{\n        // Handle successful purchase\n        console.log(\"Successfully purchased \".concat(title));\n    // You could update the book state here or refresh the page\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"book-card overflow-hidden h-full transition-all duration-300 hover:shadow-xl group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/books/\".concat(id),\n                className: \"block\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-[3/4] relative overflow-hidden rounded-t-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: coverUrl || \"/placeholder.svg\",\n                                alt: \"\".concat(title, \" book cover\"),\n                                className: \"object-cover w-full h-full transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 glass dark:glass-dark text-secondary text-xs px-2 py-1 rounded-full\",\n                                children: category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            isPurchased && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: \"Owned\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            isBookFree && !isPurchased && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: \"FREE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4 glass dark:glass-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-sm\",\n                                children: author\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 fill-secondary text-secondary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: rating.toFixed(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-primary\",\n                                        children: isBookFree ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-green-500 hover:bg-green-600\",\n                                            children: \"FREE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this) : \"$\".concat(price.toFixed(2))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                className: \"p-4 pt-0 glass dark:glass-dark\",\n                children: isPurchased ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/reader/\".concat(id),\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        className: \"w-full\",\n                        variant: \"outline\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this),\n                            \"Read Now\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this) : isBookFree ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/reader/\".concat(id),\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        className: \"w-full gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this),\n                            \"Read Free\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full gap-2\",\n                    onClick: handlePurchaseClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this),\n                        \"Buy Now\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Astewai\\\\TilkTebeb\\\\tilktbeb (1)\\\\components\\\\book-card.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(BookCard, \"GMDkQOp6xhk7B/PDWUW4Vk/PMws=\");\n_c = BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/book-card.tsx\n"));

/***/ })

});